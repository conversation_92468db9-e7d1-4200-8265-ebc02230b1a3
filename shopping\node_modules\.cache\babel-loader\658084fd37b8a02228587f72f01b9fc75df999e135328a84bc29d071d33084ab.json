{"ast": null, "code": "import baseSortedIndex from './_baseSortedIndex.js';\nimport eq from './eq.js';\n\n/**\n * This method is like `_.lastIndexOf` except that it performs a binary\n * search on a sorted `array`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n * @example\n *\n * _.sortedLastIndexOf([4, 5, 5, 5, 6], 5);\n * // => 3\n */\nfunction sortedLastIndexOf(array, value) {\n  var length = array == null ? 0 : array.length;\n  if (length) {\n    var index = baseSortedIndex(array, value, true) - 1;\n    if (eq(array[index], value)) {\n      return index;\n    }\n  }\n  return -1;\n}\nexport default sortedLastIndexOf;", "map": {"version": 3, "names": ["baseSortedIndex", "eq", "sortedLastIndexOf", "array", "value", "length", "index"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/sortedLastIndexOf.js"], "sourcesContent": ["import baseSortedIndex from './_baseSortedIndex.js';\nimport eq from './eq.js';\n\n/**\n * This method is like `_.lastIndexOf` except that it performs a binary\n * search on a sorted `array`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n * @example\n *\n * _.sortedLastIndexOf([4, 5, 5, 5, 6], 5);\n * // => 3\n */\nfunction sortedLastIndexOf(array, value) {\n  var length = array == null ? 0 : array.length;\n  if (length) {\n    var index = baseSortedIndex(array, value, true) - 1;\n    if (eq(array[index], value)) {\n      return index;\n    }\n  }\n  return -1;\n}\n\nexport default sortedLastIndexOf;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;AACnD,OAAOC,EAAE,MAAM,SAAS;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACvC,IAAIC,MAAM,GAAGF,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACE,MAAM;EAC7C,IAAIA,MAAM,EAAE;IACV,IAAIC,KAAK,GAAGN,eAAe,CAACG,KAAK,EAAEC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC;IACnD,IAAIH,EAAE,CAACE,KAAK,CAACG,KAAK,CAAC,EAAEF,KAAK,CAAC,EAAE;MAC3B,OAAOE,KAAK;IACd;EACF;EACA,OAAO,CAAC,CAAC;AACX;AAEA,eAAeJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}