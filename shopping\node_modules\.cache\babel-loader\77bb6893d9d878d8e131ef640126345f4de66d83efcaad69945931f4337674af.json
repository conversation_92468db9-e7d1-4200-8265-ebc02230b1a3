{"ast": null, "code": "import baseSortedUniq from './_baseSortedUniq.js';\n\n/**\n * This method is like `_.uniq` except that it's designed and optimized\n * for sorted arrays.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @returns {Array} Returns the new duplicate free array.\n * @example\n *\n * _.sortedUniq([1, 1, 2]);\n * // => [1, 2]\n */\nfunction sortedUniq(array) {\n  return array && array.length ? baseSortedUniq(array) : [];\n}\nexport default sortedUniq;", "map": {"version": 3, "names": ["baseSortedUniq", "sortedUniq", "array", "length"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/sortedUniq.js"], "sourcesContent": ["import baseSortedUniq from './_baseSortedUniq.js';\n\n/**\n * This method is like `_.uniq` except that it's designed and optimized\n * for sorted arrays.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @returns {Array} Returns the new duplicate free array.\n * @example\n *\n * _.sortedUniq([1, 1, 2]);\n * // => [1, 2]\n */\nfunction sortedUniq(array) {\n  return (array && array.length)\n    ? baseSortedUniq(array)\n    : [];\n}\n\nexport default sortedUniq;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sBAAsB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAQA,KAAK,IAAIA,KAAK,CAACC,MAAM,GACzBH,cAAc,CAACE,KAAK,CAAC,GACrB,EAAE;AACR;AAEA,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}