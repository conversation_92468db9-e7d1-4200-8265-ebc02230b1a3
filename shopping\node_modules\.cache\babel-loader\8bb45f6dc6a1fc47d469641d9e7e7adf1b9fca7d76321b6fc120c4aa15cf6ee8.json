{"ast": null, "code": "function useOption(props, {\n  emit\n}) {\n  return {\n    hoverItem: () => {\n      if (!props.disabled) {\n        emit(\"hover\", props.index);\n      }\n    },\n    selectOptionClick: () => {\n      if (!props.disabled) {\n        emit(\"select\", props.item, props.index);\n      }\n    }\n  };\n}\nexport { useOption };", "map": {"version": 3, "names": ["useOption", "props", "emit", "hoverItem", "disabled", "index", "selectOptionClick", "item"], "sources": ["../../../../../../packages/components/select-v2/src/useOption.ts"], "sourcesContent": ["import type { OptionV2EmitFn, OptionV2Props } from './defaults'\n\nexport function useOption(\n  props: OptionV2Props,\n  { emit }: { emit: OptionV2EmitFn }\n) {\n  return {\n    hoverItem: () => {\n      if (!props.disabled) {\n        emit('hover', props.index)\n      }\n    },\n    selectOptionClick: () => {\n      if (!props.disabled) {\n        emit('select', props.item, props.index)\n      }\n    },\n  }\n}\n"], "mappings": "AAAO,SAASA,SAASA,CAACC,KAAK,EAAE;EAAEC;AAAI,CAAE,EAAE;EACzC,OAAO;IACLC,SAAS,EAAEA,CAAA,KAAM;MACf,IAAI,CAACF,KAAK,CAACG,QAAQ,EAAE;QACnBF,IAAI,CAAC,OAAO,EAAED,KAAK,CAACI,KAAK,CAAC;MAClC;IACA,CAAK;IACDC,iBAAiB,EAAEA,CAAA,KAAM;MACvB,IAAI,CAACL,KAAK,CAACG,QAAQ,EAAE;QACnBF,IAAI,CAAC,QAAQ,EAAED,KAAK,CAACM,IAAI,EAAEN,KAAK,CAACI,KAAK,CAAC;MAC/C;IACA;EACA,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}