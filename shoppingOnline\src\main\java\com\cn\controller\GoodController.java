package com.cn.controller;

import com.cn.annotation.Authority;
import com.cn.common.Result;
import com.cn.entity.AuthorityType;
import com.cn.entity.Good;
import com.cn.service.GoodService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Authority(AuthorityType.noRequire)
@RestController
@RequestMapping("/goodapi")
public class GoodController {

    @Resource
    private GoodService goodService;

    @GetMapping("/list")
    public Result list() {
        List<Good> goods = goodService.list();
        return Result.success(goods);
    }
}



