package com.cn.controller;

import com.cn.annotation.Authority;
import com.cn.common.Result;
import com.cn.entity.AuthorityType;
import com.cn.entity.Good;
import com.cn.service.GoodService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Authority(AuthorityType.noRequire)
@RestController
@RequestMapping("/goodapi")
public class GoodController {

    @Resource
    private GoodService goodService;

    @GetMapping("/list")
    public Result list() {
        List<Good> goods = goodService.list();
        return Result.success(goods);
    }

    /**
     * 根据ID获取商品详情
     */
    @GetMapping("/{id}")
    public Result getById(@PathVariable Long id) {
        try {
            Good good = goodService.getById(id);
            if (good != null) {
                return Result.success(good);
            } else {
                return Result.error("404", "商品不存在");
            }
        } catch (Exception e) {
            return Result.error("500", "获取商品详情失败：" + e.getMessage());
        }
    }


}



