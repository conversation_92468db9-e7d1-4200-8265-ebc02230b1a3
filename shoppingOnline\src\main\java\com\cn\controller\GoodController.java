package com.cn.controller;

import com.cn.annotation.Authority;
import com.cn.common.Result;
import com.cn.entity.AuthorityType;
import com.cn.entity.Good;
import com.cn.service.GoodService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Authority(AuthorityType.noRequire)
@RestController
@RequestMapping("/goodapi")
public class GoodController {

    @Resource
    private GoodService goodService;

    @GetMapping("/list")
    public Result list() {
        List<Good> goods = goodService.list();
        return Result.success(goods);
    }

    /**
     * 根据ID获取商品详情
     */
    @GetMapping("/{id}")
    public Result getById(@PathVariable Long id) {
        try {
            Good good = goodService.getById(id);
            if (good != null) {
                return Result.success(good);
            } else {
                return Result.error("404", "商品不存在");
            }
        } catch (Exception e) {
            return Result.error("500", "获取商品详情失败：" + e.getMessage());
        }
    }

    /**
     * 添加商品 - 仅管理员权限
     */
    @Authority(AuthorityType.requireAuthority)
    @PostMapping("/add")
    public Result addGood(@RequestBody Good good) {
        try {
            // 设置创建时间
            good.setCreateTime(LocalDateTime.now());
            // 设置默认值
            if (good.getSales() == null) good.setSales(0L);
            if (good.getRecommend() == null) good.setRecommend(false);
            if (good.getIsDelete() == null) good.setIsDelete(false);

            goodService.save(good);
            return Result.success("商品添加成功");
        } catch (Exception e) {
            return Result.error("500", "添加商品失败：" + e.getMessage());
        }
    }

    /**
     * 更新商品 - 仅管理员权限
     */
    @Authority(AuthorityType.requireAuthority)
    @PutMapping("/update")
    public Result updateGood(@RequestBody Good good) {
        try {
            goodService.updateById(good);
            return Result.success("商品更新成功");
        } catch (Exception e) {
            return Result.error("500", "更新商品失败：" + e.getMessage());
        }
    }

    /**
     * 删除商品 - 仅管理员权限
     */
    @Authority(AuthorityType.requireAuthority)
    @DeleteMapping("/delete/{id}")
    public Result deleteGood(@PathVariable Long id) {
        try {
            goodService.removeById(id);
            return Result.success("商品删除成功");
        } catch (Exception e) {
            return Result.error("500", "删除商品失败：" + e.getMessage());
        }
    }
}



