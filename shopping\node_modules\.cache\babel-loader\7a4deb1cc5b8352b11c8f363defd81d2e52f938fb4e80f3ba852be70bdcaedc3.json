{"ast": null, "code": "import baseRest from './_baseRest.js';\nimport createWrap from './_createWrap.js';\nimport getHolder from './_getHolder.js';\nimport replaceHolders from './_replaceHolders.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n  WRAP_BIND_KEY_FLAG = 2,\n  WRAP_PARTIAL_FLAG = 32;\n\n/**\n * Creates a function that invokes the method at `object[key]` with `partials`\n * prepended to the arguments it receives.\n *\n * This method differs from `_.bind` by allowing bound functions to reference\n * methods that may be redefined or don't yet exist. See\n * [<PERSON>'s article](http://peter.michaux.ca/articles/lazy-function-definition-pattern)\n * for more details.\n *\n * The `_.bindKey.placeholder` value, which defaults to `_` in monolithic\n * builds, may be used as a placeholder for partially applied arguments.\n *\n * @static\n * @memberOf _\n * @since 0.10.0\n * @category Function\n * @param {Object} object The object to invoke the method on.\n * @param {string} key The key of the method.\n * @param {...*} [partials] The arguments to be partially applied.\n * @returns {Function} Returns the new bound function.\n * @example\n *\n * var object = {\n *   'user': 'fred',\n *   'greet': function(greeting, punctuation) {\n *     return greeting + ' ' + this.user + punctuation;\n *   }\n * };\n *\n * var bound = _.bindKey(object, 'greet', 'hi');\n * bound('!');\n * // => 'hi fred!'\n *\n * object.greet = function(greeting, punctuation) {\n *   return greeting + 'ya ' + this.user + punctuation;\n * };\n *\n * bound('!');\n * // => 'hiya fred!'\n *\n * // Bound with placeholders.\n * var bound = _.bindKey(object, 'greet', _, '!');\n * bound('hi');\n * // => 'hiya fred!'\n */\nvar bindKey = baseRest(function (object, key, partials) {\n  var bitmask = WRAP_BIND_FLAG | WRAP_BIND_KEY_FLAG;\n  if (partials.length) {\n    var holders = replaceHolders(partials, getHolder(bindKey));\n    bitmask |= WRAP_PARTIAL_FLAG;\n  }\n  return createWrap(key, bitmask, object, partials, holders);\n});\n\n// Assign default placeholders.\nbindKey.placeholder = {};\nexport default bindKey;", "map": {"version": 3, "names": ["baseRest", "createWrap", "getHolder", "replaceHolders", "WRAP_BIND_FLAG", "WRAP_BIND_KEY_FLAG", "WRAP_PARTIAL_FLAG", "<PERSON><PERSON><PERSON>", "object", "key", "partials", "bitmask", "length", "holders", "placeholder"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/bindKey.js"], "sourcesContent": ["import baseRest from './_baseRest.js';\nimport createWrap from './_createWrap.js';\nimport getHolder from './_getHolder.js';\nimport replaceHolders from './_replaceHolders.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n    WRAP_BIND_KEY_FLAG = 2,\n    WRAP_PARTIAL_FLAG = 32;\n\n/**\n * Creates a function that invokes the method at `object[key]` with `partials`\n * prepended to the arguments it receives.\n *\n * This method differs from `_.bind` by allowing bound functions to reference\n * methods that may be redefined or don't yet exist. See\n * [<PERSON>'s article](http://peter.michaux.ca/articles/lazy-function-definition-pattern)\n * for more details.\n *\n * The `_.bindKey.placeholder` value, which defaults to `_` in monolithic\n * builds, may be used as a placeholder for partially applied arguments.\n *\n * @static\n * @memberOf _\n * @since 0.10.0\n * @category Function\n * @param {Object} object The object to invoke the method on.\n * @param {string} key The key of the method.\n * @param {...*} [partials] The arguments to be partially applied.\n * @returns {Function} Returns the new bound function.\n * @example\n *\n * var object = {\n *   'user': 'fred',\n *   'greet': function(greeting, punctuation) {\n *     return greeting + ' ' + this.user + punctuation;\n *   }\n * };\n *\n * var bound = _.bindKey(object, 'greet', 'hi');\n * bound('!');\n * // => 'hi fred!'\n *\n * object.greet = function(greeting, punctuation) {\n *   return greeting + 'ya ' + this.user + punctuation;\n * };\n *\n * bound('!');\n * // => 'hiya fred!'\n *\n * // Bound with placeholders.\n * var bound = _.bindKey(object, 'greet', _, '!');\n * bound('hi');\n * // => 'hiya fred!'\n */\nvar bindKey = baseRest(function(object, key, partials) {\n  var bitmask = WRAP_BIND_FLAG | WRAP_BIND_KEY_FLAG;\n  if (partials.length) {\n    var holders = replaceHolders(partials, getHolder(bindKey));\n    bitmask |= WRAP_PARTIAL_FLAG;\n  }\n  return createWrap(key, bitmask, object, partials, holders);\n});\n\n// Assign default placeholders.\nbindKey.placeholder = {};\n\nexport default bindKey;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,cAAc,MAAM,sBAAsB;;AAEjD;AACA,IAAIC,cAAc,GAAG,CAAC;EAClBC,kBAAkB,GAAG,CAAC;EACtBC,iBAAiB,GAAG,EAAE;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAGP,QAAQ,CAAC,UAASQ,MAAM,EAAEC,GAAG,EAAEC,QAAQ,EAAE;EACrD,IAAIC,OAAO,GAAGP,cAAc,GAAGC,kBAAkB;EACjD,IAAIK,QAAQ,CAACE,MAAM,EAAE;IACnB,IAAIC,OAAO,GAAGV,cAAc,CAACO,QAAQ,EAAER,SAAS,CAACK,OAAO,CAAC,CAAC;IAC1DI,OAAO,IAAIL,iBAAiB;EAC9B;EACA,OAAOL,UAAU,CAACQ,GAAG,EAAEE,OAAO,EAAEH,MAAM,EAAEE,QAAQ,EAAEG,OAAO,CAAC;AAC5D,CAAC,CAAC;;AAEF;AACAN,OAAO,CAACO,WAAW,GAAG,CAAC,CAAC;AAExB,eAAeP,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}