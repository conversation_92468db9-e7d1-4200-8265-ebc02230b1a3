{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { inject } from 'vue';\nimport { getFixedColumnOffset, ensurePosition, getFixedColumnsClass } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isFunction, isString, isArray, isObject } from '@vue/shared';\nfunction useStyles(props) {\n  const parent = inject(TABLE_INJECTION_KEY);\n  const ns = useNamespace(\"table\");\n  const getRowStyle = (row, rowIndex) => {\n    const rowStyle = parent == null ? void 0 : parent.props.rowStyle;\n    if (isFunction(rowStyle)) {\n      return rowStyle.call(null, {\n        row,\n        rowIndex\n      });\n    }\n    return rowStyle || null;\n  };\n  const getRowClass = (row, rowIndex) => {\n    var _a;\n    const classes = [ns.e(\"row\")];\n    if ((parent == null ? void 0 : parent.props.highlightCurrentRow) && row === ((_a = props.store) == null ? void 0 : _a.states.currentRow.value)) {\n      classes.push(\"current-row\");\n    }\n    if (props.stripe && rowIndex % 2 === 1) {\n      classes.push(ns.em(\"row\", \"striped\"));\n    }\n    const rowClassName = parent == null ? void 0 : parent.props.rowClassName;\n    if (isString(rowClassName)) {\n      classes.push(rowClassName);\n    } else if (isFunction(rowClassName)) {\n      classes.push(rowClassName.call(null, {\n        row,\n        rowIndex\n      }));\n    }\n    return classes;\n  };\n  const getCellStyle = (rowIndex, columnIndex, row, column) => {\n    const cellStyle = parent == null ? void 0 : parent.props.cellStyle;\n    let cellStyles = cellStyle != null ? cellStyle : {};\n    if (isFunction(cellStyle)) {\n      cellStyles = cellStyle.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column\n      });\n    }\n    const fixedStyle = getFixedColumnOffset(columnIndex, props == null ? void 0 : props.fixed, props.store);\n    ensurePosition(fixedStyle, \"left\");\n    ensurePosition(fixedStyle, \"right\");\n    return Object.assign({}, cellStyles, fixedStyle);\n  };\n  const getCellClass = (rowIndex, columnIndex, row, column, offset) => {\n    const fixedClasses = getFixedColumnsClass(ns.b(), columnIndex, props == null ? void 0 : props.fixed, props.store, void 0, offset);\n    const classes = [column.id, column.align, column.className, ...fixedClasses];\n    const cellClassName = parent == null ? void 0 : parent.props.cellClassName;\n    if (isString(cellClassName)) {\n      classes.push(cellClassName);\n    } else if (isFunction(cellClassName)) {\n      classes.push(cellClassName.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column\n      }));\n    }\n    classes.push(ns.e(\"cell\"));\n    return classes.filter(className => Boolean(className)).join(\" \");\n  };\n  const getSpan = (row, column, rowIndex, columnIndex) => {\n    let rowspan = 1;\n    let colspan = 1;\n    const fn = parent == null ? void 0 : parent.props.spanMethod;\n    if (isFunction(fn)) {\n      const result = fn({\n        row,\n        column,\n        rowIndex,\n        columnIndex\n      });\n      if (isArray(result)) {\n        rowspan = result[0];\n        colspan = result[1];\n      } else if (isObject(result)) {\n        rowspan = result.rowspan;\n        colspan = result.colspan;\n      }\n    }\n    return {\n      rowspan,\n      colspan\n    };\n  };\n  const getColspanRealWidth = (columns, colspan, index) => {\n    if (colspan < 1) {\n      return columns[index].realWidth;\n    }\n    const widthArr = columns.map(({\n      realWidth,\n      width\n    }) => realWidth || width).slice(index, index + colspan);\n    return Number(widthArr.reduce((acc, width) => Number(acc) + Number(width), -1));\n  };\n  return {\n    getRowStyle,\n    getRowClass,\n    getCellStyle,\n    getCellClass,\n    getSpan,\n    getColspanRealWidth\n  };\n}\nexport { useStyles as default };", "map": {"version": 3, "names": ["useStyles", "props", "parent", "inject", "TABLE_INJECTION_KEY", "ns", "useNamespace", "getRowStyle", "row", "rowIndex", "rowStyle", "isFunction", "call", "getRowClass", "_a", "classes", "e", "highlightCurrentRow", "store", "states", "currentRow", "value", "push", "stripe", "em", "rowClassName", "isString", "getCellStyle", "columnIndex", "column", "cellStyle", "cellStyles", "fixedStyle", "getFixedColumnOffset", "fixed", "ensurePosition", "Object", "assign", "getCellClass", "offset", "fixedClasses", "getFixedColumnsClass", "b", "id", "align", "className", "cellClassName", "filter", "Boolean", "join", "getSpan", "rowspan", "colspan", "fn", "spanMethod", "result", "isArray", "isObject", "getColspanRealWidth", "columns", "index", "realWidth", "widthArr", "map", "width", "slice", "Number", "reduce", "acc"], "sources": ["../../../../../../../packages/components/table/src/table-body/styles-helper.ts"], "sourcesContent": ["import { inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isArray, isFunction, isObject, isString } from '@element-plus/utils'\nimport {\n  ensurePosition,\n  getFixedColumnOffset,\n  getFixedColumnsClass,\n} from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\n\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableBodyProps } from './defaults'\nimport type { DefaultRow, Table } from '../table/defaults'\n\nfunction useStyles<T extends DefaultRow>(props: Partial<TableBodyProps<T>>) {\n  const parent = inject(TABLE_INJECTION_KEY) as Table<T>\n  const ns = useNamespace('table')\n\n  const getRowStyle = (row: T, rowIndex: number) => {\n    const rowStyle = parent?.props.rowStyle\n    if (isFunction(rowStyle)) {\n      return rowStyle.call(null, {\n        row,\n        rowIndex,\n      })\n    }\n    return rowStyle || null\n  }\n\n  const getRowClass = (row: T, rowIndex: number) => {\n    const classes = [ns.e('row')]\n    if (\n      parent?.props.highlightCurrentRow &&\n      row === props.store?.states.currentRow.value\n    ) {\n      classes.push('current-row')\n    }\n\n    if (props.stripe && rowIndex % 2 === 1) {\n      classes.push(ns.em('row', 'striped'))\n    }\n    const rowClassName = parent?.props.rowClassName\n    if (isString(rowClassName)) {\n      classes.push(rowClassName)\n    } else if (isFunction(rowClassName)) {\n      classes.push(\n        rowClassName.call(null, {\n          row,\n          rowIndex,\n        })\n      )\n    }\n    return classes\n  }\n\n  const getCellStyle = (\n    rowIndex: number,\n    columnIndex: number,\n    row: T,\n    column: TableColumnCtx<T>\n  ) => {\n    const cellStyle = parent?.props.cellStyle\n    let cellStyles = cellStyle ?? {}\n    if (isFunction(cellStyle)) {\n      cellStyles = cellStyle.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column,\n      })\n    }\n    const fixedStyle = getFixedColumnOffset(\n      columnIndex,\n      props?.fixed,\n      props.store\n    )\n    ensurePosition(fixedStyle, 'left')\n    ensurePosition(fixedStyle, 'right')\n    return Object.assign({}, cellStyles, fixedStyle)\n  }\n\n  const getCellClass = (\n    rowIndex: number,\n    columnIndex: number,\n    row: T,\n    column: TableColumnCtx<T>,\n    offset: number\n  ) => {\n    const fixedClasses = getFixedColumnsClass(\n      ns.b(),\n      columnIndex,\n      props?.fixed,\n      props.store,\n      undefined,\n      offset\n    )\n    const classes = [column.id, column.align, column.className, ...fixedClasses]\n    const cellClassName = parent?.props.cellClassName\n    if (isString(cellClassName)) {\n      classes.push(cellClassName)\n    } else if (isFunction(cellClassName)) {\n      classes.push(\n        cellClassName.call(null, {\n          rowIndex,\n          columnIndex,\n          row,\n          column,\n        })\n      )\n    }\n    classes.push(ns.e('cell'))\n    return classes.filter((className) => Boolean(className)).join(' ')\n  }\n  const getSpan = (\n    row: T,\n    column: TableColumnCtx<T>,\n    rowIndex: number,\n    columnIndex: number\n  ) => {\n    let rowspan = 1\n    let colspan = 1\n    const fn = parent?.props.spanMethod\n    if (isFunction(fn)) {\n      const result = fn({\n        row,\n        column,\n        rowIndex,\n        columnIndex,\n      })\n      if (isArray(result)) {\n        rowspan = result[0]\n        colspan = result[1]\n      } else if (isObject(result)) {\n        rowspan = result.rowspan\n        colspan = result.colspan\n      }\n    }\n    return { rowspan, colspan }\n  }\n  const getColspanRealWidth = (\n    columns: TableColumnCtx<T>[],\n    colspan: number,\n    index: number\n  ): number => {\n    if (colspan < 1) {\n      return columns[index].realWidth!\n    }\n    const widthArr = columns\n      .map(({ realWidth, width }) => realWidth || width)\n      .slice(index, index + colspan)\n    return Number(\n      widthArr.reduce((acc, width) => Number(acc) + Number(width), -1)\n    )\n  }\n\n  return {\n    getRowStyle,\n    getRowClass,\n    getCellStyle,\n    getCellClass,\n    getSpan,\n    getColspanRealWidth,\n  }\n}\n\nexport default useStyles\n"], "mappings": ";;;;;;;;;AASA,SAASA,SAASA,CAACC,KAAK,EAAE;EACxB,MAAMC,MAAM,GAAGC,MAAM,CAACC,mBAAmB,CAAC;EAC1C,MAAMC,EAAE,GAAGC,YAAY,CAAC,OAAO,CAAC;EAChC,MAAMC,WAAW,GAAGA,CAACC,GAAG,EAAEC,QAAQ,KAAK;IACrC,MAAMC,QAAQ,GAAGR,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAACS,QAAQ;IAChE,IAAIC,UAAU,CAACD,QAAQ,CAAC,EAAE;MACxB,OAAOA,QAAQ,CAACE,IAAI,CAAC,IAAI,EAAE;QACzBJ,GAAG;QACHC;MACR,CAAO,CAAC;IACR;IACI,OAAOC,QAAQ,IAAI,IAAI;EAC3B,CAAG;EACD,MAAMG,WAAW,GAAGA,CAACL,GAAG,EAAEC,QAAQ,KAAK;IACrC,IAAIK,EAAE;IACN,MAAMC,OAAO,GAAG,CAACV,EAAE,CAACW,CAAC,CAAC,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACd,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAACgB,mBAAmB,KAAKT,GAAG,MAAM,CAACM,EAAE,GAAGb,KAAK,CAACiB,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,EAAE,CAACK,MAAM,CAACC,UAAU,CAACC,KAAK,CAAC,EAAE;MAC9IN,OAAO,CAACO,IAAI,CAAC,aAAa,CAAC;IACjC;IACI,IAAIrB,KAAK,CAACsB,MAAM,IAAId,QAAQ,GAAG,CAAC,KAAK,CAAC,EAAE;MACtCM,OAAO,CAACO,IAAI,CAACjB,EAAE,CAACmB,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAC3C;IACI,MAAMC,YAAY,GAAGvB,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAACwB,YAAY;IACxE,IAAIC,QAAQ,CAACD,YAAY,CAAC,EAAE;MAC1BV,OAAO,CAACO,IAAI,CAACG,YAAY,CAAC;IAChC,CAAK,MAAM,IAAId,UAAU,CAACc,YAAY,CAAC,EAAE;MACnCV,OAAO,CAACO,IAAI,CAACG,YAAY,CAACb,IAAI,CAAC,IAAI,EAAE;QACnCJ,GAAG;QACHC;MACR,CAAO,CAAC,CAAC;IACT;IACI,OAAOM,OAAO;EAClB,CAAG;EACD,MAAMY,YAAY,GAAGA,CAAClB,QAAQ,EAAEmB,WAAW,EAAEpB,GAAG,EAAEqB,MAAM,KAAK;IAC3D,MAAMC,SAAS,GAAG5B,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAAC6B,SAAS;IAClE,IAAIC,UAAU,GAAGD,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAG,EAAE;IACnD,IAAInB,UAAU,CAACmB,SAAS,CAAC,EAAE;MACzBC,UAAU,GAAGD,SAAS,CAAClB,IAAI,CAAC,IAAI,EAAE;QAChCH,QAAQ;QACRmB,WAAW;QACXpB,GAAG;QACHqB;MACR,CAAO,CAAC;IACR;IACI,MAAMG,UAAU,GAAGC,oBAAoB,CAACL,WAAW,EAAE3B,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACiC,KAAK,EAAEjC,KAAK,CAACiB,KAAK,CAAC;IACvGiB,cAAc,CAACH,UAAU,EAAE,MAAM,CAAC;IAClCG,cAAc,CAACH,UAAU,EAAE,OAAO,CAAC;IACnC,OAAOI,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEN,UAAU,EAAEC,UAAU,CAAC;EACpD,CAAG;EACD,MAAMM,YAAY,GAAGA,CAAC7B,QAAQ,EAAEmB,WAAW,EAAEpB,GAAG,EAAEqB,MAAM,EAAEU,MAAM,KAAK;IACnE,MAAMC,YAAY,GAAGC,oBAAoB,CAACpC,EAAE,CAACqC,CAAC,EAAE,EAAEd,WAAW,EAAE3B,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACiC,KAAK,EAAEjC,KAAK,CAACiB,KAAK,EAAE,KAAK,CAAC,EAAEqB,MAAM,CAAC;IACjI,MAAMxB,OAAO,GAAG,CAACc,MAAM,CAACc,EAAE,EAAEd,MAAM,CAACe,KAAK,EAAEf,MAAM,CAACgB,SAAS,EAAE,GAAGL,YAAY,CAAC;IAC5E,MAAMM,aAAa,GAAG5C,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAAC6C,aAAa;IAC1E,IAAIpB,QAAQ,CAACoB,aAAa,CAAC,EAAE;MAC3B/B,OAAO,CAACO,IAAI,CAACwB,aAAa,CAAC;IACjC,CAAK,MAAM,IAAInC,UAAU,CAACmC,aAAa,CAAC,EAAE;MACpC/B,OAAO,CAACO,IAAI,CAACwB,aAAa,CAAClC,IAAI,CAAC,IAAI,EAAE;QACpCH,QAAQ;QACRmB,WAAW;QACXpB,GAAG;QACHqB;MACR,CAAO,CAAC,CAAC;IACT;IACId,OAAO,CAACO,IAAI,CAACjB,EAAE,CAACW,CAAC,CAAC,MAAM,CAAC,CAAC;IAC1B,OAAOD,OAAO,CAACgC,MAAM,CAAEF,SAAS,IAAKG,OAAO,CAACH,SAAS,CAAC,CAAC,CAACI,IAAI,CAAC,GAAG,CAAC;EACtE,CAAG;EACD,MAAMC,OAAO,GAAGA,CAAC1C,GAAG,EAAEqB,MAAM,EAAEpB,QAAQ,EAAEmB,WAAW,KAAK;IACtD,IAAIuB,OAAO,GAAG,CAAC;IACf,IAAIC,OAAO,GAAG,CAAC;IACf,MAAMC,EAAE,GAAGnD,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAACqD,UAAU;IAC5D,IAAI3C,UAAU,CAAC0C,EAAE,CAAC,EAAE;MAClB,MAAME,MAAM,GAAGF,EAAE,CAAC;QAChB7C,GAAG;QACHqB,MAAM;QACNpB,QAAQ;QACRmB;MACR,CAAO,CAAC;MACF,IAAI4B,OAAO,CAACD,MAAM,CAAC,EAAE;QACnBJ,OAAO,GAAGI,MAAM,CAAC,CAAC,CAAC;QACnBH,OAAO,GAAGG,MAAM,CAAC,CAAC,CAAC;MAC3B,CAAO,MAAM,IAAIE,QAAQ,CAACF,MAAM,CAAC,EAAE;QAC3BJ,OAAO,GAAGI,MAAM,CAACJ,OAAO;QACxBC,OAAO,GAAGG,MAAM,CAACH,OAAO;MAChC;IACA;IACI,OAAO;MAAED,OAAO;MAAEC;IAAO,CAAE;EAC/B,CAAG;EACD,MAAMM,mBAAmB,GAAGA,CAACC,OAAO,EAAEP,OAAO,EAAEQ,KAAK,KAAK;IACvD,IAAIR,OAAO,GAAG,CAAC,EAAE;MACf,OAAOO,OAAO,CAACC,KAAK,CAAC,CAACC,SAAS;IACrC;IACI,MAAMC,QAAQ,GAAGH,OAAO,CAACI,GAAG,CAAC,CAAC;MAAEF,SAAS;MAAEG;IAAK,CAAE,KAAKH,SAAS,IAAIG,KAAK,CAAC,CAACC,KAAK,CAACL,KAAK,EAAEA,KAAK,GAAGR,OAAO,CAAC;IACxG,OAAOc,MAAM,CAACJ,QAAQ,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEJ,KAAK,KAAKE,MAAM,CAACE,GAAG,CAAC,GAAGF,MAAM,CAACF,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnF,CAAG;EACD,OAAO;IACLzD,WAAW;IACXM,WAAW;IACXc,YAAY;IACZW,YAAY;IACZY,OAAO;IACPQ;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}