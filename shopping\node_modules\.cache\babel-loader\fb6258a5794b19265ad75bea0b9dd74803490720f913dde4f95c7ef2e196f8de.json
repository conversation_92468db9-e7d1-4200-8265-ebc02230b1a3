{"ast": null, "code": "import baseAssign from './_baseAssign.js';\nimport baseCreate from './_baseCreate.js';\n\n/**\n * Creates an object that inherits from the `prototype` object. If a\n * `properties` object is given, its own enumerable string keyed properties\n * are assigned to the created object.\n *\n * @static\n * @memberOf _\n * @since 2.3.0\n * @category Object\n * @param {Object} prototype The object to inherit from.\n * @param {Object} [properties] The properties to assign to the object.\n * @returns {Object} Returns the new object.\n * @example\n *\n * function Shape() {\n *   this.x = 0;\n *   this.y = 0;\n * }\n *\n * function Circle() {\n *   Shape.call(this);\n * }\n *\n * Circle.prototype = _.create(Shape.prototype, {\n *   'constructor': Circle\n * });\n *\n * var circle = new Circle;\n * circle instanceof Circle;\n * // => true\n *\n * circle instanceof Shape;\n * // => true\n */\nfunction create(prototype, properties) {\n  var result = baseCreate(prototype);\n  return properties == null ? result : baseAssign(result, properties);\n}\nexport default create;", "map": {"version": 3, "names": ["baseAssign", "baseCreate", "create", "prototype", "properties", "result"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/create.js"], "sourcesContent": ["import baseAssign from './_baseAssign.js';\nimport baseCreate from './_baseCreate.js';\n\n/**\n * Creates an object that inherits from the `prototype` object. If a\n * `properties` object is given, its own enumerable string keyed properties\n * are assigned to the created object.\n *\n * @static\n * @memberOf _\n * @since 2.3.0\n * @category Object\n * @param {Object} prototype The object to inherit from.\n * @param {Object} [properties] The properties to assign to the object.\n * @returns {Object} Returns the new object.\n * @example\n *\n * function Shape() {\n *   this.x = 0;\n *   this.y = 0;\n * }\n *\n * function Circle() {\n *   Shape.call(this);\n * }\n *\n * Circle.prototype = _.create(Shape.prototype, {\n *   'constructor': Circle\n * });\n *\n * var circle = new Circle;\n * circle instanceof Circle;\n * // => true\n *\n * circle instanceof Shape;\n * // => true\n */\nfunction create(prototype, properties) {\n  var result = baseCreate(prototype);\n  return properties == null ? result : baseAssign(result, properties);\n}\n\nexport default create;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,UAAU,MAAM,kBAAkB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,SAAS,EAAEC,UAAU,EAAE;EACrC,IAAIC,MAAM,GAAGJ,UAAU,CAACE,SAAS,CAAC;EAClC,OAAOC,UAAU,IAAI,IAAI,GAAGC,MAAM,GAAGL,UAAU,CAACK,MAAM,EAAED,UAAU,CAAC;AACrE;AAEA,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}