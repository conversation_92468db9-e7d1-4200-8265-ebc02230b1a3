<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="d5a9a22c-a331-4a6e-a5da-e2cf750c792b" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/shopping/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/shopping/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shopping/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/shopping/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shopping/src/App.vue" beforeDir="false" afterPath="$PROJECT_DIR$/shopping/src/App.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shopping/src/main.js" beforeDir="false" afterPath="$PROJECT_DIR$/shopping/src/main.js" afterDir="false" />
    </list>
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DefaultGradleProjectSettings">
    <option name="isMigrated" value="true" />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/shopping/src/views/goods.vue">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="3648">
              <caret line="192" column="6" selection-start-line="192" selection-start-column="6" selection-end-line="192" selection-end-column="6" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/shopping/src/components/login.vue">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="228">
              <caret line="12" column="26" selection-start-line="12" selection-start-column="26" selection-end-line="12" selection-end-column="26" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/shopping/src/router/index.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="230">
              <caret line="13" column="18" selection-start-line="13" selection-start-column="18" selection-end-line="13" selection-end-column="18" />
              <folding>
                <element signature="e#0#59#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/shoppingOnline/pom.xml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="1786">
              <caret line="94" column="20" selection-start-line="94" selection-start-column="20" selection-end-line="94" selection-end-column="20" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/resources/application.yml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="133">
              <caret line="7" column="20" selection-start-line="7" selection-start-column="20" selection-end-line="7" selection-end-column="20" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/service/CategoryService.java">
          <provider selected="true" editor-type-id="text-editor" />
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/SpringBOOTApplication.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="247">
              <caret line="15" selection-start-line="15" selection-end-line="15" />
              <folding>
                <element signature="imports" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/shoppingOnline/shoppingOnline.iml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="19">
              <caret line="1" column="41" selection-start-line="1" selection-start-column="41" selection-end-line="1" selection-end-column="41" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/controller/HomeController.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="266">
              <caret line="18" selection-start-line="18" selection-end-line="18" />
              <folding>
                <element signature="imports" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="JavaScript File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="ROOT_SYNC" value="DONT_SYNC" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/shopping" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/shoppingOnline/pom.xml" />
        <option value="$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/controller/GoodController.java" />
        <option value="$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/controller/CategoryController.java" />
        <option value="$PROJECT_DIR$/shoppingOnline/src/main/resources/mapper/Good.xml" />
        <option value="$PROJECT_DIR$/shoppingOnline/src/main/resources/mapper/Category.xml" />
        <option value="$PROJECT_DIR$/shopping/src/views/goods.vue" />
        <option value="$PROJECT_DIR$/shoppingOnline/src/main/resources/application.yml" />
        <option value="$PROJECT_DIR$/shopping/src/router/login.js" />
        <option value="$PROJECT_DIR$/shopping/src/router/index.js" />
      </list>
    </option>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\repository" />
        <option name="mavenHome" value="$PROJECT_DIR$/../maven/apache-maven-3.6.1-bin/apache-maven-3.6.1" />
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.6.1-bin\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="vmOptionsForImporter" value="-Xmx768m" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="35" />
    <option name="y" value="30" />
    <option name="width" value="1750" />
    <option name="height" value="995" />
  </component>
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="PackagesPane" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="shoppingOnline-20250826" type="b2602c69:ProjectViewProjectNode" />
              <item name="shoppingOnline-20250826" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="shoppingOnline-20250826" type="b2602c69:ProjectViewProjectNode" />
              <item name="shoppingOnline-20250826" type="462c0819:PsiDirectoryNode" />
              <item name="shopping" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="Scope" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="aspect.path.notification.shown" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/../../shoppingOnline-0829-1528-down/shoppingOnline" />
    <property name="node.js.detected.package.eslint" value="true" />
    <property name="node.js.detected.package.tslint" value="true" />
    <property name="node.js.path.for.package.eslint" value="project" />
    <property name="node.js.path.for.package.tslint" value="project" />
    <property name="node.js.selected.package.eslint" value="(autodetect)" />
    <property name="node.js.selected.package.tslint" value="(autodetect)" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
    <property name="settings.editor.selected.configurable" value="consents" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\shoppingOnline-20250826\shoppingOnline\src\main\resources\mapper" />
      <recent name="D:\shoppingOnline-20250826\shoppingOnline\src\main\java\com\cn\entity" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="d5a9a22c-a331-4a6e-a5da-e2cf750c792b" name="Default Changelist" comment="" />
      <created>1756218484384</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756218484384</updated>
      <workItem from="1756218487038" duration="2801000" />
      <workItem from="1756255861694" duration="3924000" />
      <workItem from="1756277257030" duration="6444000" />
      <workItem from="1756342268352" duration="873000" />
      <workItem from="1756343170407" duration="3410000" />
      <workItem from="1756384223655" duration="72000" />
      <workItem from="1756385967595" duration="637000" />
      <workItem from="1756394981309" duration="12000" />
      <workItem from="1756428384437" duration="645000" />
      <workItem from="1756437629113" duration="2935000" />
      <workItem from="1756515169660" duration="41000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="21794000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="-7" y="-7" width="1550" height="830" extended-state="6" />
    <layout>
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.2855228" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="Image Layers" order="2" />
      <window_info id="Designer" order="3" />
      <window_info id="UI Designer" order="4" />
      <window_info id="Capture Tool" order="5" />
      <window_info id="Favorites" order="6" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Docker" order="7" show_stripe_button="false" />
      <window_info anchor="bottom" id="Version Control" order="8" />
      <window_info anchor="bottom" id="Database Changes" order="9" />
      <window_info anchor="bottom" id="Terminal" order="10" />
      <window_info anchor="bottom" id="Event Log" order="11" side_tool="true" weight="0.32914925" />
      <window_info anchor="bottom" id="Messages" order="12" weight="0.95815897" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" visible="true" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="right" id="Palette" order="3" />
      <window_info anchor="right" id="Theme Preview" order="4" />
      <window_info anchor="right" id="Maven" order="5" />
      <window_info anchor="right" id="Capture Analysis" order="6" />
      <window_info anchor="right" id="Palette&#9;" order="7" />
      <window_info anchor="right" id="Database" order="8" />
    </layout>
    <layout-to-restore>
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.2533512" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="Image Layers" order="2" />
      <window_info id="Designer" order="3" />
      <window_info id="UI Designer" order="4" />
      <window_info id="Capture Tool" order="5" />
      <window_info id="Favorites" order="6" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Docker" order="7" show_stripe_button="false" />
      <window_info anchor="bottom" id="Version Control" order="8" />
      <window_info anchor="bottom" id="Database Changes" order="9" />
      <window_info anchor="bottom" id="Terminal" order="10" />
      <window_info anchor="bottom" id="Event Log" order="11" side_tool="true" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="right" id="Palette" order="3" />
      <window_info anchor="right" id="Theme Preview" order="4" />
      <window_info anchor="right" id="Maven" order="5" />
      <window_info anchor="right" id="Capture Analysis" order="6" />
      <window_info anchor="right" id="Palette&#9;" order="7" />
      <window_info anchor="right" id="Database" order="8" />
    </layout-to-restore>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="antWorkspaceConfiguration">
    <option name="IS_AUTOSCROLL_TO_SOURCE" value="false" />
    <option name="FILTER_TARGETS" value="false" />
    <buildFile url="file://$PROJECT_DIR$/shoppingOnline/pom.xml">
      <expanded value="true" />
    </buildFile>
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/config/InterceptorConfig.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="171">
          <caret line="15" column="46" selection-start-line="15" selection-start-column="46" selection-end-line="15" selection-end-column="46" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/entity/User.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-970">
          <caret line="2" column="7" selection-start-line="2" selection-start-column="7" selection-end-line="2" selection-end-column="7" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/controller/UserController.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="95">
          <caret line="14" column="15" selection-start-line="14" selection-start-column="15" selection-end-line="14" selection-end-column="15" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/controller/GoodController.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="152">
          <caret line="18" selection-start-line="18" selection-end-line="18" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/entity/Good.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="285">
          <caret line="19" column="24" selection-start-line="19" selection-start-column="24" selection-end-line="19" selection-end-column="24" />
          <folding>
            <element signature="imports" expanded="true" />
            <element signature="e#627#628#0" expanded="true" />
            <element signature="e#652#653#0" expanded="true" />
            <element signature="e#686#687#0" expanded="true" />
            <element signature="e#714#715#0" expanded="true" />
            <element signature="e#745#746#0" expanded="true" />
            <element signature="e#772#773#0" expanded="true" />
            <element signature="e#812#813#0" expanded="true" />
            <element signature="e#844#845#0" expanded="true" />
            <element signature="e#882#883#0" expanded="true" />
            <element signature="e#916#917#0" expanded="true" />
            <element signature="e#970#971#0" expanded="true" />
            <element signature="e#1016#1017#0" expanded="true" />
            <element signature="e#1051#1052#0" expanded="true" />
            <element signature="e#1082#1083#0" expanded="true" />
            <element signature="e#1130#1131#0" expanded="true" />
            <element signature="e#1170#1171#0" expanded="true" />
            <element signature="e#1200#1201#0" expanded="true" />
            <element signature="e#1228#1229#0" expanded="true" />
            <element signature="e#1268#1269#0" expanded="true" />
            <element signature="e#1302#1303#0" expanded="true" />
            <element signature="e#1338#1339#0" expanded="true" />
            <element signature="e#1370#1371#0" expanded="true" />
            <element signature="e#1420#1421#0" expanded="true" />
            <element signature="e#1462#1463#0" expanded="true" />
            <element signature="e#1497#1498#0" expanded="true" />
            <element signature="e#1530#1531#0" expanded="true" />
            <element signature="e#1580#1581#0" expanded="true" />
            <element signature="e#1624#1625#0" expanded="true" />
            <element signature="e#1655#1656#0" expanded="true" />
            <element signature="e#1682#1683#0" expanded="true" />
            <element signature="e#1722#1723#0" expanded="true" />
            <element signature="e#1754#1755#0" expanded="true" />
            <element signature="e#1798#1799#0" expanded="true" />
            <element signature="e#1831#1832#0" expanded="true" />
            <element signature="e#1890#1891#0" expanded="true" />
            <element signature="e#1934#1935#0" expanded="true" />
            <element signature="e#1971#1972#0" expanded="true" />
            <element signature="e#2003#2004#0" expanded="true" />
            <element signature="e#2054#2055#0" expanded="true" />
            <element signature="e#2096#2097#0" expanded="true" />
            <element signature="e#2132#2133#0" expanded="true" />
            <element signature="e#2163#2164#0" expanded="true" />
            <element signature="e#2212#2213#0" expanded="true" />
            <element signature="e#2252#2253#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/mapper/CategoryMapper.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="171">
          <caret line="11" selection-start-line="11" selection-end-line="11" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/controller/CategoryController.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="171">
          <caret line="19" column="13" selection-start-line="19" selection-start-column="13" selection-end-line="19" selection-end-column="13" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/mapper/UserMapper.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="133">
          <caret line="9" selection-start-line="9" selection-end-line="9" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/config/MybatisPlusConfig.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="342">
          <caret line="18" lean-forward="true" selection-start-line="18" selection-end-line="18" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/utils/UserHolder.java">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/mapper/GoodMapper.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="209">
          <caret line="11" selection-start-line="11" selection-end-line="11" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/entity/AuthorityType.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="133">
          <caret line="7" lean-forward="true" selection-start-line="7" selection-end-line="7" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/interceptor/AuthorityInterceptor.java">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/entity/Category.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="38">
          <caret line="2" column="7" selection-start-line="2" selection-start-column="7" selection-end-line="2" selection-end-column="7" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/resources/mapper/Category.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="133">
          <caret line="7" selection-start-line="7" selection-end-line="7" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/service/UserService.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="76">
          <caret line="11" column="8" selection-start-line="11" selection-start-column="8" selection-end-line="11" selection-end-column="8" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/service/GoodService.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="171">
          <caret line="12" selection-start-line="12" selection-end-line="12" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/resources/mapper/User.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="627">
          <caret line="33" column="9" selection-start-line="33" selection-start-column="9" selection-end-line="33" selection-end-column="9" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/resources/mapper/Good.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="133">
          <caret line="7" selection-start-line="7" selection-end-line="7" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shopping/src/main.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="171">
          <caret line="12" column="17" selection-start-line="12" selection-start-column="17" selection-end-line="12" selection-end-column="17" />
          <folding>
            <element signature="e#0#31#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shopping/src/App.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="437">
          <caret line="23" column="1" selection-start-line="23" selection-start-column="1" selection-end-line="23" selection-end-column="1" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shopping/src/views/category.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="779">
          <caret line="41" column="20" selection-start-line="41" selection-start-column="20" selection-end-line="41" selection-end-column="20" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shopping/src/router/login.js" />
    <entry file="file://$PROJECT_DIR$/shopping/src/views/goods.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="3648">
          <caret line="192" column="6" selection-start-line="192" selection-start-column="6" selection-end-line="192" selection-end-column="6" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shopping/src/components/login.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="228">
          <caret line="12" column="26" selection-start-line="12" selection-start-column="26" selection-end-line="12" selection-end-column="26" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/pom.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1786">
          <caret line="94" column="20" selection-start-line="94" selection-start-column="20" selection-end-line="94" selection-end-column="20" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/resources/application.yml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="133">
          <caret line="7" column="20" selection-start-line="7" selection-start-column="20" selection-end-line="7" selection-end-column="20" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/service/CategoryService.java">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/SpringBOOTApplication.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="247">
          <caret line="15" selection-start-line="15" selection-end-line="15" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/shoppingOnline.iml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="19">
          <caret line="1" column="41" selection-start-line="1" selection-start-column="41" selection-end-line="1" selection-end-column="41" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shoppingOnline/src/main/java/com/cn/controller/HomeController.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="266">
          <caret line="18" selection-start-line="18" selection-end-line="18" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shopping/src/router/index.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="230">
          <caret line="13" column="18" selection-start-line="13" selection-start-column="18" selection-end-line="13" selection-end-column="18" />
          <folding>
            <element signature="e#0#59#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
  </component>
</project>