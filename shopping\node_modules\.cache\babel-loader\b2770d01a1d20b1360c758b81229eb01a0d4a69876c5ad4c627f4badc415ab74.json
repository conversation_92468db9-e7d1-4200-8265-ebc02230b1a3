{"ast": null, "code": "import baseFindKey from './_baseFindKey.js';\nimport baseForOwn from './_baseForOwn.js';\nimport baseIteratee from './_baseIteratee.js';\n\n/**\n * This method is like `_.find` except that it returns the key of the first\n * element `predicate` returns truthy for instead of the element itself.\n *\n * @static\n * @memberOf _\n * @since 1.1.0\n * @category Object\n * @param {Object} object The object to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {string|undefined} Returns the key of the matched element,\n *  else `undefined`.\n * @example\n *\n * var users = {\n *   'barney':  { 'age': 36, 'active': true },\n *   'fred':    { 'age': 40, 'active': false },\n *   'pebbles': { 'age': 1,  'active': true }\n * };\n *\n * _.findKey(users, function(o) { return o.age < 40; });\n * // => 'barney' (iteration order is not guaranteed)\n *\n * // The `_.matches` iteratee shorthand.\n * _.findKey(users, { 'age': 1, 'active': true });\n * // => 'pebbles'\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.findKey(users, ['active', false]);\n * // => 'fred'\n *\n * // The `_.property` iteratee shorthand.\n * _.findKey(users, 'active');\n * // => 'barney'\n */\nfunction findKey(object, predicate) {\n  return baseFindKey(object, baseIteratee(predicate, 3), baseForOwn);\n}\nexport default findKey;", "map": {"version": 3, "names": ["baseFindKey", "baseForOwn", "baseIteratee", "<PERSON><PERSON><PERSON>", "object", "predicate"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/findKey.js"], "sourcesContent": ["import baseFindKey from './_baseFindKey.js';\nimport baseForOwn from './_baseForOwn.js';\nimport baseIteratee from './_baseIteratee.js';\n\n/**\n * This method is like `_.find` except that it returns the key of the first\n * element `predicate` returns truthy for instead of the element itself.\n *\n * @static\n * @memberOf _\n * @since 1.1.0\n * @category Object\n * @param {Object} object The object to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {string|undefined} Returns the key of the matched element,\n *  else `undefined`.\n * @example\n *\n * var users = {\n *   'barney':  { 'age': 36, 'active': true },\n *   'fred':    { 'age': 40, 'active': false },\n *   'pebbles': { 'age': 1,  'active': true }\n * };\n *\n * _.findKey(users, function(o) { return o.age < 40; });\n * // => 'barney' (iteration order is not guaranteed)\n *\n * // The `_.matches` iteratee shorthand.\n * _.findKey(users, { 'age': 1, 'active': true });\n * // => 'pebbles'\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.findKey(users, ['active', false]);\n * // => 'fred'\n *\n * // The `_.property` iteratee shorthand.\n * _.findKey(users, 'active');\n * // => 'barney'\n */\nfunction findKey(object, predicate) {\n  return baseFindKey(object, baseIteratee(predicate, 3), baseForOwn);\n}\n\nexport default findKey;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,YAAY,MAAM,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,MAAM,EAAEC,SAAS,EAAE;EAClC,OAAOL,WAAW,CAACI,MAAM,EAAEF,YAAY,CAACG,SAAS,EAAE,CAAC,CAAC,EAAEJ,UAAU,CAAC;AACpE;AAEA,eAAeE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}