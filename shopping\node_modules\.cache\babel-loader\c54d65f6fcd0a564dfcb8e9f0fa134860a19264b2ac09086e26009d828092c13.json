{"ast": null, "code": "/**\n * This method returns `true`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `true`.\n * @example\n *\n * _.times(2, _.stubTrue);\n * // => [true, true]\n */\nfunction stubTrue() {\n  return true;\n}\nexport default stubTrue;", "map": {"version": 3, "names": ["stubTrue"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/stubTrue.js"], "sourcesContent": ["/**\n * This method returns `true`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `true`.\n * @example\n *\n * _.times(2, _.stubTrue);\n * // => [true, true]\n */\nfunction stubTrue() {\n  return true;\n}\n\nexport default stubTrue;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAAA,EAAG;EAClB,OAAO,IAAI;AACb;AAEA,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}