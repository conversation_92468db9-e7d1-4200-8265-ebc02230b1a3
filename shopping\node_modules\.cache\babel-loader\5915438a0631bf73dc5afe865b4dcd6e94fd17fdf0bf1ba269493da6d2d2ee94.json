{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createVNode as _createVNode, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"goods-container\"\n};\nconst _hoisted_2 = {\n  class: \"header\"\n};\nconst _hoisted_3 = {\n  class: \"user-info\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"loading\"\n};\nconst _hoisted_5 = {\n  class: \"goods-list\"\n};\nconst _hoisted_6 = {\n  class: \"goods-card-header\"\n};\nconst _hoisted_7 = {\n  class: \"goods-card-title\"\n};\nconst _hoisted_8 = {\n  class: \"goods-card-price\"\n};\nconst _hoisted_9 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[2] || (_cache[2] = _createElementVNode(\"h2\", {\n    class: \"page-title\"\n  }, \"在线购物商城\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"span\", null, \"欢迎，\" + _toDisplayString($data.username), 1 /* TEXT */), $data.userRole === 'admin' ? (_openBlock(), _createBlock(_component_el_button, {\n    key: 0,\n    type: \"primary\",\n    onClick: $options.goToAdmin\n  }, {\n    default: _withCtx(() => [...(_cache[0] || (_cache[0] = [_createTextVNode(\"管理后台\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n    type: \"danger\",\n    onClick: $options.logout\n  }, {\n    default: _withCtx(() => [...(_cache[1] || (_cache[1] = [_createTextVNode(\"退出登录\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createCommentVNode(\" 加载状态 \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, \"加载中...\")) : $data.goodsList.length > 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 商品列表（卡片样式） \"), _createElementVNode(\"div\", _hoisted_5, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.goodsList, good => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"goods-card\",\n      key: good.id\n    }, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString(good.categoryName || '未分类'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_7, _toDisplayString(good.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_8, \"¥\" + _toDisplayString($options.formatPrice(good.price)), 1 /* TEXT */), _createElementVNode(\"button\", {\n      class: \"btn-add-cart\",\n      onClick: $event => $options.addToCart(good)\n    }, \"加入购物车\", 8 /* PROPS */, _hoisted_9)]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" 无数据 \"), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n    class: \"no-data\"\n  }, \" 暂无商品 \", -1 /* CACHED */))], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "$data", "username", "userRole", "_createBlock", "_component_el_button", "type", "onClick", "$options", "goToAdmin", "_cache", "_createVNode", "logout", "_createCommentVNode", "loading", "_hoisted_4", "goodsList", "length", "_Fragment", "key", "_hoisted_5", "_renderList", "good", "id", "_hoisted_6", "categoryName", "_hoisted_7", "name", "_hoisted_8", "formatPrice", "price", "$event", "addToCart", "_hoisted_9"], "sources": ["D:\\2025_down\\project\\shoppingOnline-20250826-sfl\\shoppingOnline-20250826\\shopping\\src\\views\\goods.vue"], "sourcesContent": ["<template>\r\n  <div class=\"goods-container\">\r\n    <div class=\"header\">\r\n      <h2 class=\"page-title\">在线购物商城</h2>\r\n      <div class=\"user-info\">\r\n        <span>欢迎，{{ username }}</span>\r\n        <el-button v-if=\"userRole === 'admin'\" type=\"primary\" @click=\"goToAdmin\">管理后台</el-button>\r\n        <el-button type=\"danger\" @click=\"logout\">退出登录</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n\r\n    <!-- 商品列表（卡片样式） -->\r\n    <div v-else-if=\"goodsList.length > 0\" class=\"goods-list\">\r\n      <div class=\"goods-card\" v-for=\"good in goodsList\" :key=\"good.id\">\r\n        <div class=\"goods-card-header\">{{ good.categoryName || '未分类' }}</div>\r\n        <div class=\"goods-card-title\">{{ good.name }}</div>\r\n        <div class=\"goods-card-price\">¥{{ formatPrice(good.price) }}</div>\r\n        <button class=\"btn-add-cart\" @click=\"addToCart(good)\">加入购物车</button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 无数据 -->\r\n    <div v-else class=\"no-data\">\r\n      暂无商品\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import axios from 'axios'\r\n  import { ElMessage } from 'element-plus'\r\n\r\n  export default {\r\n    name: 'GoodsView',\r\n\r\n    data() {\r\n      return {\r\n        name: '',   // 初始为空\r\n        awesome: false,\r\n        loading: true,\r\n        goodsList: [],\r\n        categoryMap: {},\r\n        username: localStorage.getItem('username') || '',\r\n        userRole: localStorage.getItem('userRole') || ''\r\n      };\r\n    },\r\n\r\n    computed: {\r\n      // 示例：计算商品总数\r\n      totalGoods() {\r\n        return this.goodsList.length;\r\n      }\r\n    },\r\n\r\n    created() {\r\n      const BASE = 'http://localhost:9191'\r\n      this.loading = true\r\n      Promise.all([\r\n        axios.get(BASE + '/goodapi/list'),\r\n        axios.get(BASE + '/categoryapi/list')\r\n      ])\r\n              .then(([goodsResp, categoryResp]) => {\r\n                const goodsData = (goodsResp.data && goodsResp.data.data) || []\r\n                const categories = (categoryResp.data && categoryResp.data.data) || []\r\n                const map = {}\r\n                categories.forEach(c => { map[c.id] = c.name })\r\n                this.categoryMap = map\r\n                this.goodsList = goodsData.map(g => ({\r\n                  id: g.id,\r\n                  name: g.name,\r\n                  description: g.description,\r\n                  // 后端 imgs 形如 /file/xxx.jpg，需要拼接服务器前缀\r\n                  image: g.imgs ? (BASE + g.imgs) : '',\r\n                  // 暂用 saleMoney 做展示价格（后端无单价字段时）\r\n                  price: g.saleMoney,\r\n                  categoryId: g.categoryId,\r\n                  categoryName: map[g.categoryId]\r\n                }))\r\n              })\r\n              .catch(err => {\r\n                console.error('加载商品/分类失败', err)\r\n              })\r\n              .finally(() => {\r\n                this.loading = false\r\n              })\r\n    },\r\n\r\n    methods: {\r\n      formatPrice(v){\r\n        if(v === null || v === undefined) return '-'\r\n        const n = Number(v)\r\n        return Number.isNaN(n) ? v : n.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 2 })\r\n      },\r\n      addToCart(good) {\r\n        // TODO: 实际项目中调用 Vuex 或 API\r\n        console.log('加入购物车:', good.name);\r\n        this.$emit('add-to-cart', good); // 可用于父组件监听\r\n        alert(`已加入购物车：${good.name}`);\r\n      },\r\n      clear() {\r\n        this.name = '';\r\n      },\r\n      logout() {\r\n        localStorage.clear()\r\n        ElMessage.success('已退出登录')\r\n        this.$router.push('/login')\r\n      },\r\n      goToAdmin() {\r\n        this.$router.push('/admin')\r\n      }\r\n    },\r\n\r\n    mounted() {\r\n      console.log('商品页面已挂载');\r\n    },\r\n\r\n    beforeUnmount() {\r\n      console.log('商品页面即将卸载');\r\n    }\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .goods-container {\r\n    max-width: 1200px;\r\n    margin: 20px auto;\r\n    padding: 0 16px;\r\n  }\r\n\r\n  .page-title {\r\n    text-align: center;\r\n    color: #333;\r\n    margin-bottom: 20px;\r\n    font-size: 28px;\r\n  }\r\n\r\n  .loading,\r\n  .no-data {\r\n    text-align: center;\r\n    color: #999;\r\n    font-size: 16px;\r\n    padding: 40px 0;\r\n  }\r\n\r\n  .goods-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(4, 1fr);\r\n    gap: 24px;\r\n  }\r\n\r\n  .goods-card {\r\n    border: 1px solid #e5e5e5;\r\n    border-radius: 8px;\r\n    background: #fff;\r\n    padding: 16px 16px 12px;\r\n    text-align: center;\r\n  }\r\n\r\n  .goods-card-header {\r\n    color: #888;\r\n    font-size: 14px;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .goods-card-title {\r\n    font-weight: 700;\r\n    color: #222;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .goods-card-price {\r\n    color: #e60000;\r\n    font-weight: 700;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .btn-add-cart {\r\n    width: 60%;\r\n    margin: 0 auto;\r\n    padding: 10px 12px;\r\n    background-color: #42b983;\r\n    color: #fff;\r\n    border: none;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .btn-add-cart:hover {\r\n    background-color: #369a6e;\r\n  }\r\n\r\n  /* 响应式：手机适配 */\r\n  @media (max-width: 768px) {\r\n    .goods-list {\r\n      grid-template-columns: 1fr 1fr;\r\n      padding: 0 8px;\r\n    }\r\n\r\n    .page-title {\r\n      font-size: 24px;\r\n    }\r\n  }\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAQ;;EAEZA,KAAK,EAAC;AAAW;;;EAQJA,KAAK,EAAC;;;EAGYA,KAAK,EAAC;AAAY;;EAE/CA,KAAK,EAAC;AAAmB;;EACzBA,KAAK,EAAC;AAAkB;;EACxBA,KAAK,EAAC;AAAkB;;;;uBAlBnCC,mBAAA,CA2BM,OA3BNC,UA2BM,GA1BJC,mBAAA,CAOM,OAPNC,UAOM,G,0BANJD,mBAAA,CAAkC;IAA9BH,KAAK,EAAC;EAAY,GAAC,QAAM,qBAC7BG,mBAAA,CAIM,OAJNE,UAIM,GAHJF,mBAAA,CAA8B,cAAxB,KAAG,GAAAG,gBAAA,CAAGC,KAAA,CAAAC,QAAQ,kBACHD,KAAA,CAAAE,QAAQ,gB,cAAzBC,YAAA,CAAyFC,oBAAA;;IAAlDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEC,QAAA,CAAAC;;sBAAW,MAAI,KAAAC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,mB;;uEAC7EC,YAAA,CAAyDN,oBAAA;IAA9CC,IAAI,EAAC,QAAQ;IAAEC,OAAK,EAAEC,QAAA,CAAAI;;sBAAQ,MAAI,KAAAF,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,mB;;sCAIjDG,mBAAA,UAAa,EACFZ,KAAA,CAAAa,OAAO,I,cAAlBnB,mBAAA,CAAgD,OAAhDoB,UAAgD,EAAZ,QAAM,KAG1Bd,KAAA,CAAAe,SAAS,CAACC,MAAM,Q,cAAhCtB,mBAAA,CAOMuB,SAAA;IAAAC,GAAA;EAAA,IARNN,mBAAA,gBAAmB,EACnBhB,mBAAA,CAOM,OAPNuB,UAOM,I,kBANJzB,mBAAA,CAKMuB,SAAA,QAAAG,WAAA,CALiCpB,KAAA,CAAAe,SAAS,EAAjBM,IAAI;yBAAnC3B,mBAAA,CAKM;MALDD,KAAK,EAAC,YAAY;MAA4ByB,GAAG,EAAEG,IAAI,CAACC;QAC3D1B,mBAAA,CAAqE,OAArE2B,UAAqE,EAAAxB,gBAAA,CAAnCsB,IAAI,CAACG,YAAY,2BACnD5B,mBAAA,CAAmD,OAAnD6B,UAAmD,EAAA1B,gBAAA,CAAlBsB,IAAI,CAACK,IAAI,kBAC1C9B,mBAAA,CAAkE,OAAlE+B,UAAkE,EAApC,GAAC,GAAA5B,gBAAA,CAAGQ,QAAA,CAAAqB,WAAW,CAACP,IAAI,CAACQ,KAAK,mBACxDjC,mBAAA,CAAoE;MAA5DH,KAAK,EAAC,cAAc;MAAEa,OAAK,EAAAwB,MAAA,IAAEvB,QAAA,CAAAwB,SAAS,CAACV,IAAI;OAAG,OAAK,iBAAAW,UAAA,E;sGAK/DtC,mBAAA,CAEMuB,SAAA;IAAAC,GAAA;EAAA,IAHNN,mBAAA,SAAY,E,0BACZhB,mBAAA,CAEM;IAFMH,KAAK,EAAC;EAAS,GAAC,QAE5B,oB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}