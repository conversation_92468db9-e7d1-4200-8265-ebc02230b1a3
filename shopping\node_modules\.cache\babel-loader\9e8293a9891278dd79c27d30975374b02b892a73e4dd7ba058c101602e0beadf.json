{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport axios from 'axios';\nimport { ElMessage } from 'element-plus';\nexport default {\n  name: 'GoodsView',\n  data() {\n    return {\n      name: '',\n      // 初始为空\n      awesome: false,\n      loading: true,\n      goodsList: [],\n      categoryMap: {},\n      username: localStorage.getItem('username') || '',\n      userRole: localStorage.getItem('userRole') || ''\n    };\n  },\n  computed: {\n    // 示例：计算商品总数\n    totalGoods() {\n      return this.goodsList.length;\n    }\n  },\n  created() {\n    const BASE = 'http://localhost:9191';\n    this.loading = true;\n    Promise.all([axios.get(BASE + '/goodapi/list'), axios.get(BASE + '/categoryapi/list')]).then(([goodsResp, categoryResp]) => {\n      const goodsData = goodsResp.data && goodsResp.data.data || [];\n      const categories = categoryResp.data && categoryResp.data.data || [];\n      const map = {};\n      categories.forEach(c => {\n        map[c.id] = c.name;\n      });\n      this.categoryMap = map;\n      this.goodsList = goodsData.map(g => ({\n        id: g.id,\n        name: g.name,\n        description: g.description,\n        // 后端 imgs 形如 /file/xxx.jpg，需要拼接服务器前缀\n        image: g.imgs ? BASE + g.imgs : '',\n        // 暂用 saleMoney 做展示价格（后端无单价字段时）\n        price: g.saleMoney,\n        categoryId: g.categoryId,\n        categoryName: map[g.categoryId]\n      }));\n    }).catch(err => {\n      console.error('加载商品/分类失败', err);\n    }).finally(() => {\n      this.loading = false;\n    });\n  },\n  methods: {\n    formatPrice(v) {\n      if (v === null || v === undefined) return '-';\n      const n = Number(v);\n      return Number.isNaN(n) ? v : n.toLocaleString('zh-CN', {\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 2\n      });\n    },\n    addToCart(good) {\n      // TODO: 实际项目中调用 Vuex 或 API\n      console.log('加入购物车:', good.name);\n      this.$emit('add-to-cart', good); // 可用于父组件监听\n      alert(`已加入购物车：${good.name}`);\n    },\n    clear() {\n      this.name = '';\n    },\n    logout() {\n      localStorage.clear();\n      ElMessage.success('已退出登录');\n      this.$router.push('/login');\n    },\n    goToAdmin() {\n      this.$router.push('/admin');\n    }\n  },\n  mounted() {\n    console.log('商品页面已挂载');\n  },\n  beforeUnmount() {\n    console.log('商品页面即将卸载');\n  }\n};", "map": {"version": 3, "names": ["axios", "ElMessage", "name", "data", "awesome", "loading", "goodsList", "categoryMap", "username", "localStorage", "getItem", "userRole", "computed", "totalGoods", "length", "created", "BASE", "Promise", "all", "get", "then", "goodsResp", "categoryResp", "goodsData", "categories", "map", "for<PERSON>ach", "c", "id", "g", "description", "image", "imgs", "price", "saleMoney", "categoryId", "categoryName", "catch", "err", "console", "error", "finally", "methods", "formatPrice", "v", "undefined", "n", "Number", "isNaN", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "addToCart", "good", "log", "$emit", "alert", "clear", "logout", "success", "$router", "push", "goToAdmin", "mounted", "beforeUnmount"], "sources": ["D:\\2025_down\\project\\shoppingOnline-20250826-sfl\\shoppingOnline-20250826\\shopping\\src\\views\\goods.vue"], "sourcesContent": ["<template>\r\n  <div class=\"goods-container\">\r\n    <div class=\"header\">\r\n      <h2 class=\"page-title\">在线购物商城</h2>\r\n      <div class=\"user-info\">\r\n        <span>欢迎，{{ username }}</span>\r\n        <el-button v-if=\"userRole === 'admin'\" type=\"primary\" @click=\"goToAdmin\">管理后台</el-button>\r\n        <el-button type=\"danger\" @click=\"logout\">退出登录</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n\r\n    <!-- 商品列表（卡片样式） -->\r\n    <div v-else-if=\"goodsList.length > 0\" class=\"goods-list\">\r\n      <div class=\"goods-card\" v-for=\"good in goodsList\" :key=\"good.id\">\r\n        <div class=\"goods-card-header\">{{ good.categoryName || '未分类' }}</div>\r\n        <div class=\"goods-card-title\">{{ good.name }}</div>\r\n        <div class=\"goods-card-price\">¥{{ formatPrice(good.price) }}</div>\r\n        <button class=\"btn-add-cart\" @click=\"addToCart(good)\">加入购物车</button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 无数据 -->\r\n    <div v-else class=\"no-data\">\r\n      暂无商品\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import axios from 'axios'\r\n  import { ElMessage } from 'element-plus'\r\n\r\n  export default {\r\n    name: 'GoodsView',\r\n\r\n    data() {\r\n      return {\r\n        name: '',   // 初始为空\r\n        awesome: false,\r\n        loading: true,\r\n        goodsList: [],\r\n        categoryMap: {},\r\n        username: localStorage.getItem('username') || '',\r\n        userRole: localStorage.getItem('userRole') || ''\r\n      };\r\n    },\r\n\r\n    computed: {\r\n      // 示例：计算商品总数\r\n      totalGoods() {\r\n        return this.goodsList.length;\r\n      }\r\n    },\r\n\r\n    created() {\r\n      const BASE = 'http://localhost:9191'\r\n      this.loading = true\r\n      Promise.all([\r\n        axios.get(BASE + '/goodapi/list'),\r\n        axios.get(BASE + '/categoryapi/list')\r\n      ])\r\n              .then(([goodsResp, categoryResp]) => {\r\n                const goodsData = (goodsResp.data && goodsResp.data.data) || []\r\n                const categories = (categoryResp.data && categoryResp.data.data) || []\r\n                const map = {}\r\n                categories.forEach(c => { map[c.id] = c.name })\r\n                this.categoryMap = map\r\n                this.goodsList = goodsData.map(g => ({\r\n                  id: g.id,\r\n                  name: g.name,\r\n                  description: g.description,\r\n                  // 后端 imgs 形如 /file/xxx.jpg，需要拼接服务器前缀\r\n                  image: g.imgs ? (BASE + g.imgs) : '',\r\n                  // 暂用 saleMoney 做展示价格（后端无单价字段时）\r\n                  price: g.saleMoney,\r\n                  categoryId: g.categoryId,\r\n                  categoryName: map[g.categoryId]\r\n                }))\r\n              })\r\n              .catch(err => {\r\n                console.error('加载商品/分类失败', err)\r\n              })\r\n              .finally(() => {\r\n                this.loading = false\r\n              })\r\n    },\r\n\r\n    methods: {\r\n      formatPrice(v){\r\n        if(v === null || v === undefined) return '-'\r\n        const n = Number(v)\r\n        return Number.isNaN(n) ? v : n.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 2 })\r\n      },\r\n      addToCart(good) {\r\n        // TODO: 实际项目中调用 Vuex 或 API\r\n        console.log('加入购物车:', good.name);\r\n        this.$emit('add-to-cart', good); // 可用于父组件监听\r\n        alert(`已加入购物车：${good.name}`);\r\n      },\r\n      clear() {\r\n        this.name = '';\r\n      },\r\n      logout() {\r\n        localStorage.clear()\r\n        ElMessage.success('已退出登录')\r\n        this.$router.push('/login')\r\n      },\r\n      goToAdmin() {\r\n        this.$router.push('/admin')\r\n      }\r\n    },\r\n\r\n    mounted() {\r\n      console.log('商品页面已挂载');\r\n    },\r\n\r\n    beforeUnmount() {\r\n      console.log('商品页面即将卸载');\r\n    }\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .goods-container {\r\n    max-width: 1200px;\r\n    margin: 20px auto;\r\n    padding: 0 16px;\r\n  }\r\n\r\n  .page-title {\r\n    text-align: center;\r\n    color: #333;\r\n    margin-bottom: 20px;\r\n    font-size: 28px;\r\n  }\r\n\r\n  .loading,\r\n  .no-data {\r\n    text-align: center;\r\n    color: #999;\r\n    font-size: 16px;\r\n    padding: 40px 0;\r\n  }\r\n\r\n  .goods-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(4, 1fr);\r\n    gap: 24px;\r\n  }\r\n\r\n  .goods-card {\r\n    border: 1px solid #e5e5e5;\r\n    border-radius: 8px;\r\n    background: #fff;\r\n    padding: 16px 16px 12px;\r\n    text-align: center;\r\n  }\r\n\r\n  .goods-card-header {\r\n    color: #888;\r\n    font-size: 14px;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .goods-card-title {\r\n    font-weight: 700;\r\n    color: #222;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .goods-card-price {\r\n    color: #e60000;\r\n    font-weight: 700;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .btn-add-cart {\r\n    width: 60%;\r\n    margin: 0 auto;\r\n    padding: 10px 12px;\r\n    background-color: #42b983;\r\n    color: #fff;\r\n    border: none;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .btn-add-cart:hover {\r\n    background-color: #369a6e;\r\n  }\r\n\r\n  /* 响应式：手机适配 */\r\n  @media (max-width: 768px) {\r\n    .goods-list {\r\n      grid-template-columns: 1fr 1fr;\r\n      padding: 0 8px;\r\n    }\r\n\r\n    .page-title {\r\n      font-size: 24px;\r\n    }\r\n  }\r\n</style>"], "mappings": ";;;;AAgCE,OAAOA,KAAI,MAAO,OAAM;AACxB,SAASC,SAAQ,QAAS,cAAa;AAEvC,eAAe;EACbC,IAAI,EAAE,WAAW;EAEjBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLD,IAAI,EAAE,EAAE;MAAI;MACZE,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,CAAC,CAAC;MACfC,QAAQ,EAAEC,YAAY,CAACC,OAAO,CAAC,UAAU,KAAK,EAAE;MAChDC,QAAQ,EAAEF,YAAY,CAACC,OAAO,CAAC,UAAU,KAAK;IAChD,CAAC;EACH,CAAC;EAEDE,QAAQ,EAAE;IACR;IACAC,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACP,SAAS,CAACQ,MAAM;IAC9B;EACF,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR,MAAMC,IAAG,GAAI,uBAAsB;IACnC,IAAI,CAACX,OAAM,GAAI,IAAG;IAClBY,OAAO,CAACC,GAAG,CAAC,CACVlB,KAAK,CAACmB,GAAG,CAACH,IAAG,GAAI,eAAe,CAAC,EACjChB,KAAK,CAACmB,GAAG,CAACH,IAAG,GAAI,mBAAmB,EACrC,EACQI,IAAI,CAAC,CAAC,CAACC,SAAS,EAAEC,YAAY,CAAC,KAAK;MACnC,MAAMC,SAAQ,GAAKF,SAAS,CAAClB,IAAG,IAAKkB,SAAS,CAAClB,IAAI,CAACA,IAAI,IAAK,EAAC;MAC9D,MAAMqB,UAAS,GAAKF,YAAY,CAACnB,IAAG,IAAKmB,YAAY,CAACnB,IAAI,CAACA,IAAI,IAAK,EAAC;MACrE,MAAMsB,GAAE,GAAI,CAAC;MACbD,UAAU,CAACE,OAAO,CAACC,CAAA,IAAK;QAAEF,GAAG,CAACE,CAAC,CAACC,EAAE,IAAID,CAAC,CAACzB,IAAG;MAAE,CAAC;MAC9C,IAAI,CAACK,WAAU,GAAIkB,GAAE;MACrB,IAAI,CAACnB,SAAQ,GAAIiB,SAAS,CAACE,GAAG,CAACI,CAAA,KAAM;QACnCD,EAAE,EAAEC,CAAC,CAACD,EAAE;QACR1B,IAAI,EAAE2B,CAAC,CAAC3B,IAAI;QACZ4B,WAAW,EAAED,CAAC,CAACC,WAAW;QAC1B;QACAC,KAAK,EAAEF,CAAC,CAACG,IAAG,GAAKhB,IAAG,GAAIa,CAAC,CAACG,IAAI,GAAI,EAAE;QACpC;QACAC,KAAK,EAAEJ,CAAC,CAACK,SAAS;QAClBC,UAAU,EAAEN,CAAC,CAACM,UAAU;QACxBC,YAAY,EAAEX,GAAG,CAACI,CAAC,CAACM,UAAU;MAChC,CAAC,CAAC;IACJ,CAAC,EACAE,KAAK,CAACC,GAAE,IAAK;MACZC,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEF,GAAG;IAChC,CAAC,EACAG,OAAO,CAAC,MAAM;MACb,IAAI,CAACpC,OAAM,GAAI,KAAI;IACrB,CAAC;EACX,CAAC;EAEDqC,OAAO,EAAE;IACPC,WAAWA,CAACC,CAAC,EAAC;MACZ,IAAGA,CAAA,KAAM,IAAG,IAAKA,CAAA,KAAMC,SAAS,EAAE,OAAO,GAAE;MAC3C,MAAMC,CAAA,GAAIC,MAAM,CAACH,CAAC;MAClB,OAAOG,MAAM,CAACC,KAAK,CAACF,CAAC,IAAIF,CAAA,GAAIE,CAAC,CAACG,cAAc,CAAC,OAAO,EAAE;QAAEC,qBAAqB,EAAE,CAAC;QAAEC,qBAAqB,EAAE;MAAE,CAAC;IAC/G,CAAC;IACDC,SAASA,CAACC,IAAI,EAAE;MACd;MACAd,OAAO,CAACe,GAAG,CAAC,QAAQ,EAAED,IAAI,CAACnD,IAAI,CAAC;MAChC,IAAI,CAACqD,KAAK,CAAC,aAAa,EAAEF,IAAI,CAAC,EAAE;MACjCG,KAAK,CAAC,UAAUH,IAAI,CAACnD,IAAI,EAAE,CAAC;IAC9B,CAAC;IACDuD,KAAKA,CAAA,EAAG;MACN,IAAI,CAACvD,IAAG,GAAI,EAAE;IAChB,CAAC;IACDwD,MAAMA,CAAA,EAAG;MACPjD,YAAY,CAACgD,KAAK,CAAC;MACnBxD,SAAS,CAAC0D,OAAO,CAAC,OAAO;MACzB,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,QAAQ;IAC5B,CAAC;IACDC,SAASA,CAAA,EAAG;MACV,IAAI,CAACF,OAAO,CAACC,IAAI,CAAC,QAAQ;IAC5B;EACF,CAAC;EAEDE,OAAOA,CAAA,EAAG;IACRxB,OAAO,CAACe,GAAG,CAAC,SAAS,CAAC;EACxB,CAAC;EAEDU,aAAaA,CAAA,EAAG;IACdzB,OAAO,CAACe,GAAG,CAAC,UAAU,CAAC;EACzB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}