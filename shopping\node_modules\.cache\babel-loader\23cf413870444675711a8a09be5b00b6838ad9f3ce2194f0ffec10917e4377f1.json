{"ast": null, "code": "/** Used to match wrap detail comments. */\nvar reWrapComment = /\\{(?:\\n\\/\\* \\[wrapped with .+\\] \\*\\/)?\\n?/;\n\n/**\n * Inserts wrapper `details` in a comment at the top of the `source` body.\n *\n * @private\n * @param {string} source The source to modify.\n * @returns {Array} details The details to insert.\n * @returns {string} Returns the modified source.\n */\nfunction insertWrapDetails(source, details) {\n  var length = details.length;\n  if (!length) {\n    return source;\n  }\n  var lastIndex = length - 1;\n  details[lastIndex] = (length > 1 ? '& ' : '') + details[lastIndex];\n  details = details.join(length > 2 ? ', ' : ' ');\n  return source.replace(reWrapComment, '{\\n/* [wrapped with ' + details + '] */\\n');\n}\nexport default insertWrapDetails;", "map": {"version": 3, "names": ["reWrapComment", "insertWrapDetails", "source", "details", "length", "lastIndex", "join", "replace"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_insertWrapDetails.js"], "sourcesContent": ["/** Used to match wrap detail comments. */\nvar reWrapComment = /\\{(?:\\n\\/\\* \\[wrapped with .+\\] \\*\\/)?\\n?/;\n\n/**\n * Inserts wrapper `details` in a comment at the top of the `source` body.\n *\n * @private\n * @param {string} source The source to modify.\n * @returns {Array} details The details to insert.\n * @returns {string} Returns the modified source.\n */\nfunction insertWrapDetails(source, details) {\n  var length = details.length;\n  if (!length) {\n    return source;\n  }\n  var lastIndex = length - 1;\n  details[lastIndex] = (length > 1 ? '& ' : '') + details[lastIndex];\n  details = details.join(length > 2 ? ', ' : ' ');\n  return source.replace(reWrapComment, '{\\n/* [wrapped with ' + details + '] */\\n');\n}\n\nexport default insertWrapDetails;\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAG,2CAA2C;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC1C,IAAIC,MAAM,GAAGD,OAAO,CAACC,MAAM;EAC3B,IAAI,CAACA,MAAM,EAAE;IACX,OAAOF,MAAM;EACf;EACA,IAAIG,SAAS,GAAGD,MAAM,GAAG,CAAC;EAC1BD,OAAO,CAACE,SAAS,CAAC,GAAG,CAACD,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,IAAID,OAAO,CAACE,SAAS,CAAC;EAClEF,OAAO,GAAGA,OAAO,CAACG,IAAI,CAACF,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;EAC/C,OAAOF,MAAM,CAACK,OAAO,CAACP,aAAa,EAAE,sBAAsB,GAAGG,OAAO,GAAG,QAAQ,CAAC;AACnF;AAEA,eAAeF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}