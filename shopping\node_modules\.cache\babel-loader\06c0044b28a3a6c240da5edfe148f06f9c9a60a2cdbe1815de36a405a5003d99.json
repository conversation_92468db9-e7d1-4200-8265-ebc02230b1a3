{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"goods-container\"\n};\nconst _hoisted_2 = {\n  class: \"header\"\n};\nconst _hoisted_3 = {\n  class: \"user-info\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"loading\"\n};\nconst _hoisted_5 = {\n  class: \"goods-list\"\n};\nconst _hoisted_6 = {\n  class: \"goods-card-header\"\n};\nconst _hoisted_7 = [\"onClick\"];\nconst _hoisted_8 = {\n  class: \"goods-card-description\"\n};\nconst _hoisted_9 = {\n  class: \"goods-card-price\"\n};\nconst _hoisted_10 = {\n  class: \"goods-card-actions\"\n};\nconst _hoisted_11 = [\"onClick\"];\nconst _hoisted_12 = {\n  key: 0,\n  class: \"good-detail\"\n};\nconst _hoisted_13 = {\n  class: \"detail-item\"\n};\nconst _hoisted_14 = {\n  class: \"detail-item\"\n};\nconst _hoisted_15 = {\n  class: \"detail-item\"\n};\nconst _hoisted_16 = {\n  class: \"detail-item\"\n};\nconst _hoisted_17 = {\n  class: \"detail-item\"\n};\nconst _hoisted_18 = {\n  class: \"detail-item\"\n};\nconst _hoisted_19 = {\n  class: \"detail-item\"\n};\nconst _hoisted_20 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[4] || (_cache[4] = _createElementVNode(\"h2\", {\n    class: \"page-title\"\n  }, \"在线购物商城\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"span\", null, \"欢迎，\" + _toDisplayString($data.username), 1 /* TEXT */), _createVNode(_component_el_button, {\n    type: \"danger\",\n    onClick: $options.logout\n  }, {\n    default: _withCtx(() => [...(_cache[3] || (_cache[3] = [_createTextVNode(\"退出登录\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createCommentVNode(\" 加载状态 \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, \"加载中...\")) : $data.goodsList.length > 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 商品列表（卡片样式） \"), _createElementVNode(\"div\", _hoisted_5, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.goodsList, good => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"goods-card\",\n      key: good.id\n    }, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString(good.categoryName || '未分类'), 1 /* TEXT */), _createElementVNode(\"div\", {\n      class: \"goods-card-title\",\n      onClick: $event => $options.showGoodDetail(good)\n    }, _toDisplayString(good.name), 9 /* TEXT, PROPS */, _hoisted_7), _createElementVNode(\"div\", _hoisted_8, _toDisplayString(good.description || '暂无描述'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_9, \"¥\" + _toDisplayString($options.formatPrice(good.price)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"button\", {\n      class: \"btn-add-cart\",\n      onClick: $event => $options.addToCart(good)\n    }, \"加入购物车\", 8 /* PROPS */, _hoisted_11)])]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" 无数据 \"), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"no-data\"\n  }, \" 暂无商品 \", -1 /* CACHED */))], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 商品详情对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"商品详情\",\n    modelValue: $data.detailDialogVisible,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.detailDialogVisible = $event),\n    width: \"500px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_20, [_createVNode(_component_el_button, {\n      onClick: _cache[0] || (_cache[0] = $event => $data.detailDialogVisible = false)\n    }, {\n      default: _withCtx(() => [...(_cache[13] || (_cache[13] = [_createTextVNode(\"关闭\", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[1] || (_cache[1] = $event => $options.addToCart($data.selectedGood))\n    }, {\n      default: _withCtx(() => [...(_cache[14] || (_cache[14] = [_createTextVNode(\"加入购物车\", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    })])]),\n    default: _withCtx(() => [$data.selectedGood ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_cache[6] || (_cache[6] = _createElementVNode(\"label\", null, \"商品名称：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($data.selectedGood.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_14, [_cache[7] || (_cache[7] = _createElementVNode(\"label\", null, \"商品描述：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($data.selectedGood.description || '暂无描述'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_15, [_cache[8] || (_cache[8] = _createElementVNode(\"label\", null, \"价格：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, \"¥\" + _toDisplayString($options.formatPrice($data.selectedGood.price)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_16, [_cache[9] || (_cache[9] = _createElementVNode(\"label\", null, \"折扣：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($data.selectedGood.discount ? ($data.selectedGood.discount * 100).toFixed(0) + '%' : '无折扣'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_17, [_cache[10] || (_cache[10] = _createElementVNode(\"label\", null, \"分类：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($data.selectedGood.categoryName || '未分类'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_18, [_cache[11] || (_cache[11] = _createElementVNode(\"label\", null, \"销量：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($data.selectedGood.sales || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_19, [_cache[12] || (_cache[12] = _createElementVNode(\"label\", null, \"是否推荐：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($data.selectedGood.recommend ? '是' : '否'), 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "$data", "username", "_createVNode", "_component_el_button", "type", "onClick", "$options", "logout", "_cache", "_createCommentVNode", "loading", "_hoisted_4", "goodsList", "length", "_Fragment", "key", "_hoisted_5", "_renderList", "good", "id", "_hoisted_6", "categoryName", "$event", "showGoodDetail", "name", "_hoisted_7", "_hoisted_8", "description", "_hoisted_9", "formatPrice", "price", "_hoisted_10", "addToCart", "_hoisted_11", "_component_el_dialog", "title", "detailDialogVisible", "width", "footer", "_withCtx", "_hoisted_20", "<PERSON><PERSON><PERSON>", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "discount", "toFixed", "_hoisted_17", "_hoisted_18", "sales", "_hoisted_19", "recommend"], "sources": ["D:\\2025_down\\project\\shoppingOnline-20250826-sfl\\shoppingOnline-20250826\\shopping\\src\\views\\goods.vue"], "sourcesContent": ["<template>\r\n  <div class=\"goods-container\">\r\n    <div class=\"header\">\r\n      <h2 class=\"page-title\">在线购物商城</h2>\r\n      <div class=\"user-info\">\r\n        <span>欢迎，{{ username }}</span>\r\n        <el-button type=\"danger\" @click=\"logout\">退出登录</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n\r\n    <!-- 商品列表（卡片样式） -->\r\n    <div v-else-if=\"goodsList.length > 0\" class=\"goods-list\">\r\n      <div class=\"goods-card\" v-for=\"good in goodsList\" :key=\"good.id\">\r\n        <div class=\"goods-card-header\">{{ good.categoryName || '未分类' }}</div>\r\n        <div class=\"goods-card-title\" @click=\"showGoodDetail(good)\">{{ good.name }}</div>\r\n        <div class=\"goods-card-description\">{{ good.description || '暂无描述' }}</div>\r\n        <div class=\"goods-card-price\">¥{{ formatPrice(good.price) }}</div>\r\n        <div class=\"goods-card-actions\">\r\n          <button class=\"btn-add-cart\" @click=\"addToCart(good)\">加入购物车</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 无数据 -->\r\n    <div v-else class=\"no-data\">\r\n      暂无商品\r\n    </div>\r\n\r\n\r\n\r\n    <!-- 商品详情对话框 -->\r\n    <el-dialog\r\n      title=\"商品详情\"\r\n      v-model=\"detailDialogVisible\"\r\n      width=\"500px\"\r\n    >\r\n      <div v-if=\"selectedGood\" class=\"good-detail\">\r\n        <div class=\"detail-item\">\r\n          <label>商品名称：</label>\r\n          <span>{{ selectedGood.name }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>商品描述：</label>\r\n          <span>{{ selectedGood.description || '暂无描述' }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>价格：</label>\r\n          <span>¥{{ formatPrice(selectedGood.price) }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>折扣：</label>\r\n          <span>{{ selectedGood.discount ? (selectedGood.discount * 100).toFixed(0) + '%' : '无折扣' }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>分类：</label>\r\n          <span>{{ selectedGood.categoryName || '未分类' }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>销量：</label>\r\n          <span>{{ selectedGood.sales || 0 }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>是否推荐：</label>\r\n          <span>{{ selectedGood.recommend ? '是' : '否' }}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"detailDialogVisible = false\">关闭</el-button>\r\n          <el-button type=\"primary\" @click=\"addToCart(selectedGood)\">加入购物车</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import axios from 'axios'\r\n  import { ElMessage } from 'element-plus'\r\n\r\n  export default {\r\n    name: 'GoodsView',\r\n\r\n    data() {\r\n      return {\r\n        name: '',   // 初始为空\r\n        awesome: false,\r\n        loading: true,\r\n        goodsList: [],\r\n        categoryMap: {},\r\n        username: localStorage.getItem('username') || '',\r\n\r\n        // 商品详情相关\r\n        detailDialogVisible: false,\r\n        selectedGood: null\r\n      };\r\n    },\r\n\r\n    computed: {\r\n      // 示例：计算商品总数\r\n      totalGoods() {\r\n        return this.goodsList.length;\r\n      },\r\n\r\n\r\n    },\r\n\r\n    created() {\r\n      const BASE = 'http://localhost:9192'\r\n      this.loading = true\r\n      Promise.all([\r\n        axios.get(BASE + '/goodapi/list'),\r\n        axios.get(BASE + '/categoryapi/list')\r\n      ])\r\n              .then(([goodsResp, categoryResp]) => {\r\n                const goodsData = (goodsResp.data && goodsResp.data.data) || []\r\n                const categories = (categoryResp.data && categoryResp.data.data) || []\r\n                const map = {}\r\n                categories.forEach(c => { map[c.id] = c.name })\r\n                this.categoryMap = map\r\n                this.categories = categories\r\n                this.goodsList = goodsData.map(g => ({\r\n                  id: g.id,\r\n                  name: g.name,\r\n                  description: g.description,\r\n                  // 后端 imgs 形如 /file/xxx.jpg，需要拼接服务器前缀\r\n                  image: g.imgs ? (BASE + g.imgs) : '',\r\n                  // 暂用 saleMoney 做展示价格（后端无单价字段时）\r\n                  price: g.saleMoney,\r\n                  categoryId: g.categoryId,\r\n                  categoryName: map[g.categoryId]\r\n                }))\r\n              })\r\n              .catch(err => {\r\n                console.error('加载商品/分类失败', err)\r\n              })\r\n              .finally(() => {\r\n                this.loading = false\r\n              })\r\n    },\r\n\r\n    methods: {\r\n      formatPrice(v){\r\n        if(v === null || v === undefined) return '-'\r\n        const n = Number(v)\r\n        return Number.isNaN(n) ? v : n.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 2 })\r\n      },\r\n      addToCart(good) {\r\n        // TODO: 实际项目中调用 Vuex 或 API\r\n        console.log('加入购物车:', good.name);\r\n        this.$emit('add-to-cart', good); // 可用于父组件监听\r\n        alert(`已加入购物车：${good.name}`);\r\n      },\r\n      clear() {\r\n        this.name = '';\r\n      },\r\n      logout() {\r\n        localStorage.clear()\r\n        ElMessage.success('已退出登录')\r\n        this.$router.push('/login')\r\n      },\r\n      goToAdmin() {\r\n        this.$router.push('/admin')\r\n      },\r\n\r\n      // 显示商品详情\r\n      showGoodDetail(good) {\r\n        this.selectedGood = good\r\n        this.detailDialogVisible = true\r\n      },\r\n\r\n      // 显示添加商品对话框\r\n      showAddGoodDialog() {\r\n        this.goodDialogTitle = '添加商品'\r\n        this.isEditGood = false\r\n        this.goodForm = {\r\n          id: null,\r\n          name: '',\r\n          description: '',\r\n          saleMoney: 0,\r\n          discount: 0,\r\n          categoryId: null,\r\n          recommend: false\r\n        }\r\n        this.goodDialogVisible = true\r\n      },\r\n\r\n      // 编辑商品\r\n      editGood(good) {\r\n        this.goodDialogTitle = '编辑商品'\r\n        this.isEditGood = true\r\n        this.goodForm = {\r\n          id: good.id,\r\n          name: good.name,\r\n          description: good.description,\r\n          saleMoney: good.saleMoney,\r\n          discount: good.discount,\r\n          categoryId: good.categoryId,\r\n          recommend: good.recommend\r\n        }\r\n        this.goodDialogVisible = true\r\n      },\r\n\r\n      // 保存商品\r\n      async saveGood() {\r\n        if (!this.$refs.goodFormRef) return\r\n\r\n        this.$refs.goodFormRef.validate(async (valid) => {\r\n          if (valid) {\r\n            this.goodSaveLoading = true\r\n\r\n            try {\r\n              const url = this.isEditGood\r\n                ? 'http://localhost:9192/goodapi/update'\r\n                : 'http://localhost:9192/goodapi/add'\r\n\r\n              const method = this.isEditGood ? 'PUT' : 'POST'\r\n\r\n              const response = await fetch(url, {\r\n                method,\r\n                headers: {\r\n                  'Content-Type': 'application/json',\r\n                  'token': localStorage.getItem('token')\r\n                },\r\n                body: JSON.stringify(this.goodForm)\r\n              })\r\n\r\n              const result = await response.json()\r\n\r\n              if (result.code === '200') {\r\n                this.$message.success(this.isEditGood ? '商品更新成功' : '商品添加成功')\r\n                this.goodDialogVisible = false\r\n                this.refreshGoods()\r\n              } else {\r\n                this.$message.error(result.msg || '操作失败')\r\n              }\r\n            } catch (error) {\r\n              console.error('保存商品失败:', error)\r\n              this.$message.error('网络错误，请稍后重试')\r\n            } finally {\r\n              this.goodSaveLoading = false\r\n            }\r\n          }\r\n        })\r\n      },\r\n\r\n      // 删除商品\r\n      async deleteGood(good) {\r\n        try {\r\n          await this.$confirm(`确定要删除商品 \"${good.name}\" 吗？`, '确认删除', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning',\r\n          })\r\n\r\n          const response = await fetch(`http://localhost:9192/goodapi/delete/${good.id}`, {\r\n            method: 'DELETE',\r\n            headers: {\r\n              'token': localStorage.getItem('token')\r\n            }\r\n          })\r\n\r\n          const result = await response.json()\r\n\r\n          if (result.code === '200') {\r\n            this.$message.success('商品删除成功')\r\n            this.refreshGoods()\r\n          } else {\r\n            this.$message.error(result.msg || '删除失败')\r\n          }\r\n        } catch (error) {\r\n          if (error !== 'cancel') {\r\n            console.error('删除商品失败:', error)\r\n            this.$message.error('网络错误，请稍后重试')\r\n          }\r\n        }\r\n      },\r\n\r\n      // 刷新商品列表\r\n      refreshGoods() {\r\n        this.loading = true\r\n        const BASE = 'http://localhost:9192'\r\n        axios.get(BASE + '/goodapi/list')\r\n          .then(response => {\r\n            const goodsData = (response.data && response.data.data) || []\r\n            this.goodsList = goodsData.map(good => ({\r\n              ...good,\r\n              categoryName: this.categoryMap[good.categoryId] || '未分类',\r\n              price: good.saleMoney * (good.discount || 1)\r\n            }))\r\n          })\r\n          .catch(error => {\r\n            console.error('获取商品列表失败:', error)\r\n            this.$message.error('获取商品列表失败')\r\n          })\r\n          .finally(() => {\r\n            this.loading = false\r\n          })\r\n      }\r\n    },\r\n\r\n    mounted() {\r\n      console.log('商品页面已挂载');\r\n    },\r\n\r\n    beforeUnmount() {\r\n      console.log('商品页面即将卸载');\r\n    }\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  /* 滚动条样式优化 */\r\n  ::-webkit-scrollbar {\r\n    width: 8px;\r\n  }\r\n\r\n  ::-webkit-scrollbar-track {\r\n    background: #f1f1f1;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  ::-webkit-scrollbar-thumb {\r\n    background: #c1c1c1;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  ::-webkit-scrollbar-thumb:hover {\r\n    background: #a8a8a8;\r\n  }\r\n  .goods-container {\r\n    width: 100%;\r\n    min-height: 100vh;\r\n    padding: 20px;\r\n    background-color: #f5f5f5;\r\n    overflow-x: hidden;\r\n    scroll-behavior: smooth;\r\n  }\r\n\r\n  .header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 30px;\r\n    padding: 20px;\r\n    background: white;\r\n    border-radius: 12px;\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.1);\r\n    max-width: 1400px;\r\n    margin: 0 auto 30px auto;\r\n  }\r\n\r\n  .page-title {\r\n    color: #333;\r\n    margin: 0;\r\n    font-size: 28px;\r\n  }\r\n\r\n  .user-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 15px;\r\n  }\r\n\r\n  .user-info span {\r\n    color: #666;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .loading {\r\n    text-align: center;\r\n    font-size: 16px;\r\n    padding: 60px 0;\r\n    color: #666;\r\n    max-width: 1400px;\r\n    margin: 0 auto;\r\n  }\r\n\r\n  .no-data {\r\n    text-align: center;\r\n    color: #999;\r\n    font-size: 18px;\r\n    padding: 80px 0;\r\n    max-width: 1400px;\r\n    margin: 0 auto;\r\n    background: white;\r\n    border-radius: 12px;\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.1);\r\n  }\r\n\r\n  .goods-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n    gap: 24px;\r\n    max-width: 1400px;\r\n    margin: 0 auto;\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .goods-card {\r\n    border: 1px solid #e5e5e5;\r\n    border-radius: 12px;\r\n    background: #fff;\r\n    padding: 20px;\r\n    text-align: center;\r\n    transition: all 0.3s ease;\r\n    height: auto;\r\n    min-height: 320px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .goods-card:hover {\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n    transform: translateY(-4px);\r\n  }\r\n\r\n  .goods-card-header {\r\n    color: #888;\r\n    font-size: 14px;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .goods-card-title {\r\n    font-weight: 700;\r\n    color: #222;\r\n    margin-bottom: 8px;\r\n    cursor: pointer;\r\n    transition: color 0.3s;\r\n  }\r\n\r\n  .goods-card-title:hover {\r\n    color: #409EFF;\r\n  }\r\n\r\n  .goods-card-description {\r\n    font-size: 14px;\r\n    color: #666;\r\n    margin-bottom: 12px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    line-height: 1.4;\r\n    height: 2.8em;\r\n    flex-grow: 1;\r\n  }\r\n\r\n  .goods-card-price {\r\n    color: #e60000;\r\n    font-weight: 700;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .btn-add-cart {\r\n    width: 60%;\r\n    margin: 0 auto;\r\n    padding: 10px 12px;\r\n    background-color: #42b983;\r\n    color: #fff;\r\n    border: none;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .btn-add-cart:hover {\r\n    background-color: #369a6e;\r\n  }\r\n\r\n  .goods-card-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .btn-edit, .btn-delete {\r\n    padding: 8px 12px;\r\n    border: none;\r\n    border-radius: 4px;\r\n    cursor: pointer;\r\n    transition: background-color 0.3s;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .btn-edit {\r\n    background-color: #409EFF;\r\n    color: #fff;\r\n  }\r\n\r\n  .btn-edit:hover {\r\n    background-color: #337ecc;\r\n  }\r\n\r\n  .btn-delete {\r\n    background-color: #F56C6C;\r\n    color: #fff;\r\n  }\r\n\r\n  .btn-delete:hover {\r\n    background-color: #dd6161;\r\n  }\r\n\r\n  .good-detail {\r\n    padding: 20px 0;\r\n  }\r\n\r\n  .detail-item {\r\n    display: flex;\r\n    margin-bottom: 15px;\r\n    align-items: center;\r\n  }\r\n\r\n  .detail-item label {\r\n    font-weight: bold;\r\n    width: 100px;\r\n    color: #333;\r\n  }\r\n\r\n  .detail-item span {\r\n    color: #666;\r\n    flex: 1;\r\n  }\r\n\r\n  .dialog-footer {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    gap: 10px;\r\n  }\r\n\r\n  /* 响应式设计 */\r\n\r\n  /* 大屏幕 */\r\n  @media (min-width: 1400px) {\r\n    .goods-list {\r\n      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n    }\r\n  }\r\n\r\n  /* 中等屏幕 */\r\n  @media (max-width: 1200px) {\r\n    .goods-container {\r\n      padding: 15px;\r\n    }\r\n\r\n    .goods-list {\r\n      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n      gap: 20px;\r\n      padding: 0 10px;\r\n    }\r\n\r\n    .header {\r\n      padding: 15px;\r\n      margin-bottom: 20px;\r\n    }\r\n  }\r\n\r\n  /* 平板适配 */\r\n  @media (max-width: 768px) {\r\n    .goods-container {\r\n      padding: 10px;\r\n    }\r\n\r\n    .goods-list {\r\n      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n      gap: 15px;\r\n      padding: 0 5px;\r\n    }\r\n\r\n    .goods-card {\r\n      padding: 15px;\r\n      min-height: 280px;\r\n    }\r\n\r\n    .goods-card-title {\r\n      font-size: 16px;\r\n    }\r\n\r\n    .header {\r\n      flex-direction: column;\r\n      gap: 15px;\r\n      text-align: center;\r\n    }\r\n\r\n    .user-info {\r\n      flex-wrap: wrap;\r\n      justify-content: center;\r\n    }\r\n\r\n    .page-title {\r\n      font-size: 22px;\r\n    }\r\n  }\r\n\r\n  /* 手机适配 */\r\n  @media (max-width: 480px) {\r\n    .goods-container {\r\n      padding: 8px;\r\n    }\r\n\r\n    .goods-list {\r\n      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));\r\n      gap: 12px;\r\n      padding: 0;\r\n    }\r\n\r\n    .goods-card {\r\n      padding: 12px;\r\n      min-height: 250px;\r\n    }\r\n\r\n    .goods-card-title {\r\n      font-size: 14px;\r\n    }\r\n\r\n    .goods-card-description {\r\n      font-size: 12px;\r\n    }\r\n\r\n    .goods-card-price {\r\n      font-size: 16px;\r\n    }\r\n\r\n    .header {\r\n      padding: 12px;\r\n      margin-bottom: 15px;\r\n    }\r\n\r\n    .page-title {\r\n      font-size: 18px;\r\n    }\r\n\r\n    .user-info span {\r\n      font-size: 12px;\r\n    }\r\n\r\n    .btn-add-cart, .btn-edit, .btn-delete {\r\n      padding: 6px 8px;\r\n      font-size: 11px;\r\n    }\r\n  }\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAQ;;EAEZA,KAAK,EAAC;AAAW;;;EAOJA,KAAK,EAAC;;;EAGYA,KAAK,EAAC;AAAY;;EAE/CA,KAAK,EAAC;AAAmB;;;EAEzBA,KAAK,EAAC;AAAwB;;EAC9BA,KAAK,EAAC;AAAkB;;EACxBA,KAAK,EAAC;AAAoB;;;;EAmBRA,KAAK,EAAC;;;EACxBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAOlBA,KAAK,EAAC;AAAe;;;;uBAtEjCC,mBAAA,CA4EM,OA5ENC,UA4EM,GA3EJC,mBAAA,CAMM,OANNC,UAMM,G,0BALJD,mBAAA,CAAkC;IAA9BH,KAAK,EAAC;EAAY,GAAC,QAAM,qBAC7BG,mBAAA,CAGM,OAHNE,UAGM,GAFJF,mBAAA,CAA8B,cAAxB,KAAG,GAAAG,gBAAA,CAAGC,KAAA,CAAAC,QAAQ,kBACpBC,YAAA,CAAyDC,oBAAA;IAA9CC,IAAI,EAAC,QAAQ;IAAEC,OAAK,EAAEC,QAAA,CAAAC;;sBAAQ,MAAI,KAAAC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,mB;;sCAIjDC,mBAAA,UAAa,EACFT,KAAA,CAAAU,OAAO,I,cAAlBhB,mBAAA,CAAgD,OAAhDiB,UAAgD,EAAZ,QAAM,KAG1BX,KAAA,CAAAY,SAAS,CAACC,MAAM,Q,cAAhCnB,mBAAA,CAUMoB,SAAA;IAAAC,GAAA;EAAA,IAXNN,mBAAA,gBAAmB,EACnBb,mBAAA,CAUM,OAVNoB,UAUM,I,kBATJtB,mBAAA,CAQMoB,SAAA,QAAAG,WAAA,CARiCjB,KAAA,CAAAY,SAAS,EAAjBM,IAAI;yBAAnCxB,mBAAA,CAQM;MARDD,KAAK,EAAC,YAAY;MAA4BsB,GAAG,EAAEG,IAAI,CAACC;QAC3DvB,mBAAA,CAAqE,OAArEwB,UAAqE,EAAArB,gBAAA,CAAnCmB,IAAI,CAACG,YAAY,2BACnDzB,mBAAA,CAAiF;MAA5EH,KAAK,EAAC,kBAAkB;MAAEY,OAAK,EAAAiB,MAAA,IAAEhB,QAAA,CAAAiB,cAAc,CAACL,IAAI;wBAAMA,IAAI,CAACM,IAAI,wBAAAC,UAAA,GACxE7B,mBAAA,CAA0E,OAA1E8B,UAA0E,EAAA3B,gBAAA,CAAnCmB,IAAI,CAACS,WAAW,4BACvD/B,mBAAA,CAAkE,OAAlEgC,UAAkE,EAApC,GAAC,GAAA7B,gBAAA,CAAGO,QAAA,CAAAuB,WAAW,CAACX,IAAI,CAACY,KAAK,mBACxDlC,mBAAA,CAEM,OAFNmC,WAEM,GADJnC,mBAAA,CAAoE;MAA5DH,KAAK,EAAC,cAAc;MAAEY,OAAK,EAAAiB,MAAA,IAAEhB,QAAA,CAAA0B,SAAS,CAACd,IAAI;OAAG,OAAK,iBAAAe,WAAA,E;sGAMjEvC,mBAAA,CAEMoB,SAAA;IAAAC,GAAA;EAAA,IAHNN,mBAAA,SAAY,E,0BACZb,mBAAA,CAEM;IAFMH,KAAK,EAAC;EAAS,GAAC,QAE5B,oB,mDAIAgB,mBAAA,aAAgB,EAChBP,YAAA,CA0CYgC,oBAAA;IAzCVC,KAAK,EAAC,MAAM;gBACHnC,KAAA,CAAAoC,mBAAmB;+DAAnBpC,KAAA,CAAAoC,mBAAmB,GAAAd,MAAA;IAC5Be,KAAK,EAAC;;IAiCKC,MAAM,EAAAC,QAAA,CACf,MAGO,CAHP3C,mBAAA,CAGO,QAHP4C,WAGO,GAFLtC,YAAA,CAA8DC,oBAAA;MAAlDE,OAAK,EAAAG,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAAEtB,KAAA,CAAAoC,mBAAmB;;wBAAU,MAAE,KAAA5B,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,mB;;QAClDN,YAAA,CAA4EC,oBAAA;MAAjEC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAAG,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAAEhB,QAAA,CAAA0B,SAAS,CAAChC,KAAA,CAAAyC,YAAY;;wBAAG,MAAK,KAAAjC,MAAA,SAAAA,MAAA,Q,iBAAL,OAAK,mB;;;sBAlCpE,MA6BM,CA7BKR,KAAA,CAAAyC,YAAY,I,cAAvB/C,mBAAA,CA6BM,OA7BNgD,WA6BM,GA5BJ9C,mBAAA,CAGM,OAHN+C,WAGM,G,0BAFJ/C,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAAoC,cAAAG,gBAAA,CAA3BC,KAAA,CAAAyC,YAAY,CAACjB,IAAI,iB,GAE5B5B,mBAAA,CAGM,OAHNgD,WAGM,G,0BAFJhD,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAAqD,cAAAG,gBAAA,CAA5CC,KAAA,CAAAyC,YAAY,CAACd,WAAW,2B,GAEnC/B,mBAAA,CAGM,OAHNiD,WAGM,G,0BAFJjD,mBAAA,CAAkB,eAAX,KAAG,qBACVA,mBAAA,CAAmD,cAA7C,GAAC,GAAAG,gBAAA,CAAGO,QAAA,CAAAuB,WAAW,CAAC7B,KAAA,CAAAyC,YAAY,CAACX,KAAK,kB,GAE1ClC,mBAAA,CAGM,OAHNkD,WAGM,G,0BAFJlD,mBAAA,CAAkB,eAAX,KAAG,qBACVA,mBAAA,CAAiG,cAAAG,gBAAA,CAAxFC,KAAA,CAAAyC,YAAY,CAACM,QAAQ,IAAI/C,KAAA,CAAAyC,YAAY,CAACM,QAAQ,QAAQC,OAAO,kC,GAExEpD,mBAAA,CAGM,OAHNqD,WAGM,G,4BAFJrD,mBAAA,CAAkB,eAAX,KAAG,qBACVA,mBAAA,CAAqD,cAAAG,gBAAA,CAA5CC,KAAA,CAAAyC,YAAY,CAACpB,YAAY,0B,GAEpCzB,mBAAA,CAGM,OAHNsD,WAGM,G,4BAFJtD,mBAAA,CAAkB,eAAX,KAAG,qBACVA,mBAAA,CAA0C,cAAAG,gBAAA,CAAjCC,KAAA,CAAAyC,YAAY,CAACU,KAAK,sB,GAE7BvD,mBAAA,CAGM,OAHNwD,WAGM,G,4BAFJxD,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAAqD,cAAAG,gBAAA,CAA5CC,KAAA,CAAAyC,YAAY,CAACY,SAAS,6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}