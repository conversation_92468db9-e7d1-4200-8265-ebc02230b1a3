{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive, onMounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage, ElNotification } from 'element-plus';\n\n// 表单引用\n\nexport default {\n  __name: 'login',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const loginFormRef = ref();\n\n    // 路由实例\n    const router = useRouter();\n\n    // 登录表单数据\n    const loginForm = reactive({\n      username: '',\n      password: '',\n      captcha: ''\n    });\n\n    // 记住密码\n    const rememberMe = ref(false);\n\n    // 加载状态\n    const loading = ref(false);\n\n    // 验证码文本\n    const captchaText = ref('');\n\n    // 表单验证规则\n    const loginRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }, {\n        min: 3,\n        max: 20,\n        message: '用户名长度为3-20个字符',\n        trigger: 'blur'\n      }],\n      password: [{\n        required: true,\n        message: '请输入密码',\n        trigger: 'blur'\n      }, {\n        min: 6,\n        max: 20,\n        message: '密码长度为6-20个字符',\n        trigger: 'blur'\n      }],\n      captcha: [{\n        required: true,\n        message: '请输入验证码',\n        trigger: 'blur'\n      }]\n    };\n\n    // 生成随机验证码\n    const generateCaptcha = () => {\n      const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';\n      let result = '';\n      for (let i = 0; i < 4; i++) {\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n      }\n      captchaText.value = result;\n      loginForm.captcha = '';\n    };\n\n    // 刷新验证码\n    const refreshCaptcha = () => {\n      generateCaptcha();\n    };\n\n    // 处理登录\n    const handleLogin = async () => {\n      if (!loginFormRef.value) return;\n      await loginFormRef.value.validate(valid => {\n        if (valid) {\n          // 验证验证码\n          if (loginForm.captcha.toLowerCase() !== captchaText.value.toLowerCase()) {\n            ElMessage.error('验证码错误');\n            refreshCaptcha();\n            return;\n          }\n\n          // 模拟登录过程\n          loading.value = true;\n          setTimeout(() => {\n            loading.value = false;\n\n            // 模拟登录成功\n            if (loginForm.username === 'admin' && loginForm.password === '123456') {\n              ElNotification({\n                title: '登录成功',\n                message: `欢迎回来，${loginForm.username}！`,\n                type: 'success',\n                duration: 2000\n              });\n\n              // 保存登录状态（实际项目中应该保存token）\n              localStorage.setItem('isLoggedIn', 'true');\n              localStorage.setItem('username', loginForm.username);\n\n              // 跳转到首页\n              router.push('/');\n            } else {\n              ElMessage.error('用户名或密码错误');\n              refreshCaptcha();\n            }\n          }, 1500);\n        } else {\n          ElMessage.error('请填写正确的登录信息');\n          return false;\n        }\n      });\n    };\n\n    // 忘记密码\n    const handleForgetPassword = () => {\n      ElMessage.info('忘记密码功能开发中...');\n    };\n\n    // 微信登录\n    const handleWechatLogin = () => {\n      ElMessage.info('微信登录功能开发中...');\n    };\n\n    // QQ登录\n    const handleQQLogin = () => {\n      ElMessage.info('QQ登录功能开发中...');\n    };\n\n    // 注册\n    const handleRegister = () => {\n      ElMessage.info('注册功能开发中...');\n      // router.push('/register')\n    };\n\n    // 组件挂载时生成验证码\n    onMounted(() => {\n      generateCaptcha();\n\n      // 检查是否记住密码\n      const savedUsername = localStorage.getItem('savedUsername');\n      if (savedUsername) {\n        loginForm.username = savedUsername;\n        rememberMe.value = true;\n      }\n    });\n    const __returned__ = {\n      loginFormRef,\n      router,\n      loginForm,\n      rememberMe,\n      loading,\n      captchaText,\n      loginRules,\n      generateCaptcha,\n      refreshCaptcha,\n      handleLogin,\n      handleForgetPassword,\n      handleWechatLogin,\n      handleQQLogin,\n      handleRegister,\n      ref,\n      reactive,\n      onMounted,\n      get useRouter() {\n        return useRouter;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElNotification() {\n        return ElNotification;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "useRouter", "ElMessage", "ElNotification", "loginFormRef", "router", "loginForm", "username", "password", "<PERSON><PERSON>a", "rememberMe", "loading", "captchaText", "loginRules", "required", "message", "trigger", "min", "max", "generateCaptcha", "chars", "result", "i", "char<PERSON>t", "Math", "floor", "random", "length", "value", "refreshCaptcha", "handleLogin", "validate", "valid", "toLowerCase", "error", "setTimeout", "title", "type", "duration", "localStorage", "setItem", "push", "handleForgetPassword", "info", "handleWechatLogin", "handleQQLogin", "handleRegister", "savedUsername", "getItem"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/src/components/login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-box\">\n      <div class=\"login-header\">\n        <h2>🛒 购物商城</h2>\n        <p>欢迎登录</p>\n      </div>\n      \n      <el-form\n        ref=\"loginFormRef\"\n        :model=\"loginForm\"\n        :rules=\"loginRules\"\n        class=\"login-form\"\n        @keyup.enter=\"handleLogin\"\n      >\n        <!-- 用户名输入框 -->\n        <el-form-item prop=\"username\">\n          <el-input\n            v-model=\"loginForm.username\"\n            placeholder=\"请输入用户名\"\n            prefix-icon=\"User\"\n            clearable\n            size=\"large\"\n          />\n        </el-form-item>\n        \n        <!-- 密码输入框 -->\n        <el-form-item prop=\"password\">\n          <el-input\n            v-model=\"loginForm.password\"\n            type=\"password\"\n            placeholder=\"请输入密码\"\n            prefix-icon=\"Lock\"\n            show-password\n            size=\"large\"\n          />\n        </el-form-item>\n        \n        <!-- 验证码 -->\n        <el-form-item prop=\"captcha\" class=\"captcha-item\">\n          <div class=\"captcha-wrapper\">\n            <el-input\n              v-model=\"loginForm.captcha\"\n              placeholder=\"请输入验证码\"\n              prefix-icon=\"Key\"\n              size=\"large\"\n              class=\"captcha-input\"\n            />\n            <div class=\"captcha-image\" @click=\"refreshCaptcha\">\n              {{ captchaText }}\n            </div>\n          </div>\n        </el-form-item>\n        \n        <!-- 记住密码和忘记密码 -->\n        <el-form-item>\n          <div class=\"login-options\">\n            <el-checkbox v-model=\"rememberMe\">记住密码</el-checkbox>\n            <el-link type=\"primary\" @click=\"handleForgetPassword\">忘记密码？</el-link>\n          </div>\n        </el-form-item>\n        \n        <!-- 登录按钮 -->\n        <el-form-item>\n          <el-button\n            type=\"primary\"\n            size=\"large\"\n            class=\"login-button\"\n            :loading=\"loading\"\n            @click=\"handleLogin\"\n            block\n          >\n            {{ loading ? '登录中...' : '登录' }}\n          </el-button>\n        </el-form-item>\n        \n        <!-- 其他登录方式 -->\n        <el-form-item>\n          <div class=\"other-login\">\n            <span>其他登录方式：</span>\n            <el-link type=\"primary\" @click=\"handleWechatLogin\">微信登录</el-link>\n            <el-divider direction=\"vertical\"></el-divider>\n            <el-link type=\"primary\" @click=\"handleQQLogin\">QQ登录</el-link>\n          </div>\n        </el-form-item>\n        \n        <!-- 注册链接 -->\n        <el-form-item>\n          <div class=\"register-link\">\n            还没有账号？\n            <el-link type=\"primary\" @click=\"handleRegister\">立即注册</el-link>\n          </div>\n        </el-form-item>\n      </el-form>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage, ElNotification } from 'element-plus'\n\n// 表单引用\nconst loginFormRef = ref()\n\n// 路由实例\nconst router = useRouter()\n\n// 登录表单数据\nconst loginForm = reactive({\n  username: '',\n  password: '',\n  captcha: ''\n})\n\n// 记住密码\nconst rememberMe = ref(false)\n\n// 加载状态\nconst loading = ref(false)\n\n// 验证码文本\nconst captchaText = ref('')\n\n// 表单验证规则\nconst loginRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' },\n    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }\n  ],\n  captcha: [\n    { required: true, message: '请输入验证码', trigger: 'blur' }\n  ]\n}\n\n// 生成随机验证码\nconst generateCaptcha = () => {\n  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789'\n  let result = ''\n  for (let i = 0; i < 4; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length))\n  }\n  captchaText.value = result\n  loginForm.captcha = ''\n}\n\n// 刷新验证码\nconst refreshCaptcha = () => {\n  generateCaptcha()\n}\n\n// 处理登录\nconst handleLogin = async () => {\n  if (!loginFormRef.value) return\n  \n  await loginFormRef.value.validate((valid) => {\n    if (valid) {\n      // 验证验证码\n      if (loginForm.captcha.toLowerCase() !== captchaText.value.toLowerCase()) {\n        ElMessage.error('验证码错误')\n        refreshCaptcha()\n        return\n      }\n      \n      // 模拟登录过程\n      loading.value = true\n      \n      setTimeout(() => {\n        loading.value = false\n        \n        // 模拟登录成功\n        if (loginForm.username === 'admin' && loginForm.password === '123456') {\n          ElNotification({\n            title: '登录成功',\n            message: `欢迎回来，${loginForm.username}！`,\n            type: 'success',\n            duration: 2000\n          })\n          \n          // 保存登录状态（实际项目中应该保存token）\n          localStorage.setItem('isLoggedIn', 'true')\n          localStorage.setItem('username', loginForm.username)\n          \n          // 跳转到首页\n          router.push('/')\n        } else {\n          ElMessage.error('用户名或密码错误')\n          refreshCaptcha()\n        }\n      }, 1500)\n    } else {\n      ElMessage.error('请填写正确的登录信息')\n      return false\n    }\n  })\n}\n\n// 忘记密码\nconst handleForgetPassword = () => {\n  ElMessage.info('忘记密码功能开发中...')\n}\n\n// 微信登录\nconst handleWechatLogin = () => {\n  ElMessage.info('微信登录功能开发中...')\n}\n\n// QQ登录\nconst handleQQLogin = () => {\n  ElMessage.info('QQ登录功能开发中...')\n}\n\n// 注册\nconst handleRegister = () => {\n  ElMessage.info('注册功能开发中...')\n  // router.push('/register')\n}\n\n// 组件挂载时生成验证码\nonMounted(() => {\n  generateCaptcha()\n  \n  // 检查是否记住密码\n  const savedUsername = localStorage.getItem('savedUsername')\n  if (savedUsername) {\n    loginForm.username = savedUsername\n    rememberMe.value = true\n  }\n})\n</script>\n\n<style scoped>\n.login-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n}\n\n.login-box {\n  width: 100%;\n  max-width: 400px;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 10px;\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  padding: 40px 30px;\n  backdrop-filter: blur(10px);\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.login-header h2 {\n  color: #333;\n  margin-bottom: 10px;\n  font-size: 24px;\n}\n\n.login-header p {\n  color: #666;\n  font-size: 14px;\n}\n\n.login-form {\n  width: 100%;\n}\n\n.captcha-item :deep(.el-form-item__content) {\n  display: block;\n}\n\n.captcha-wrapper {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.captcha-input {\n  flex: 1;\n}\n\n.captcha-image {\n  width: 100px;\n  height: 40px;\n  background: linear-gradient(45deg, #f0f0f0, #e0e0e0);\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 18px;\n  color: #333;\n  cursor: pointer;\n  user-select: none;\n  transition: all 0.3s;\n}\n\n.captcha-image:hover {\n  background: linear-gradient(45deg, #e0e0e0, #d0d0d0);\n}\n\n.login-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.login-button {\n  margin-top: 10px;\n}\n\n.other-login {\n  text-align: center;\n  color: #666;\n  font-size: 14px;\n}\n\n.other-login span {\n  margin-right: 10px;\n}\n\n.register-link {\n  text-align: center;\n  color: #666;\n  font-size: 14px;\n}\n\n:deep(.el-input__wrapper) {\n  border-radius: 20px;\n}\n\n:deep(.el-button) {\n  border-radius: 20px;\n}\n\n@media (max-width: 480px) {\n  .login-box {\n    padding: 30px 20px;\n    margin: 10px;\n  }\n  \n  .captcha-wrapper {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .captcha-image {\n    width: 100%;\n    margin-top: 10px;\n  }\n}\n</style>"], "mappings": ";AAmGA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,KAAI;AAC7C,SAASC,SAAS,QAAQ,YAAW;AACrC,SAASC,SAAS,EAAEC,cAAc,QAAQ,cAAa;;AAEvD;;;;;;;;IACA,MAAMC,YAAY,GAAGN,GAAG,CAAC;;IAEzB;IACA,MAAMO,MAAM,GAAGJ,SAAS,CAAC;;IAEzB;IACA,MAAMK,SAAS,GAAGP,QAAQ,CAAC;MACzBQ,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE;IACX,CAAC;;IAED;IACA,MAAMC,UAAU,GAAGZ,GAAG,CAAC,KAAK;;IAE5B;IACA,MAAMa,OAAO,GAAGb,GAAG,CAAC,KAAK;;IAEzB;IACA,MAAMc,WAAW,GAAGd,GAAG,CAAC,EAAE;;IAE1B;IACA,MAAMe,UAAU,GAAG;MACjBN,QAAQ,EAAE,CACR;QAAEO,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,eAAe;QAAEC,OAAO,EAAE;MAAO,EAC9D;MACDR,QAAQ,EAAE,CACR;QAAEM,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,cAAc;QAAEC,OAAO,EAAE;MAAO,EAC7D;MACDP,OAAO,EAAE,CACP;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO;IAEzD;;IAEA;IACA,MAAMG,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,KAAK,GAAG,wDAAuD;MACrE,IAAIC,MAAM,GAAG,EAAC;MACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1BD,MAAM,IAAID,KAAK,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGN,KAAK,CAACO,MAAM,CAAC;MACjE;MACAf,WAAW,CAACgB,KAAK,GAAGP,MAAK;MACzBf,SAAS,CAACG,OAAO,GAAG,EAAC;IACvB;;IAEA;IACA,MAAMoB,cAAc,GAAGA,CAAA,KAAM;MAC3BV,eAAe,CAAC;IAClB;;IAEA;IACA,MAAMW,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI,CAAC1B,YAAY,CAACwB,KAAK,EAAE;MAEzB,MAAMxB,YAAY,CAACwB,KAAK,CAACG,QAAQ,CAAEC,KAAK,IAAK;QAC3C,IAAIA,KAAK,EAAE;UACT;UACA,IAAI1B,SAAS,CAACG,OAAO,CAACwB,WAAW,CAAC,CAAC,KAAKrB,WAAW,CAACgB,KAAK,CAACK,WAAW,CAAC,CAAC,EAAE;YACvE/B,SAAS,CAACgC,KAAK,CAAC,OAAO;YACvBL,cAAc,CAAC;YACf;UACF;;UAEA;UACAlB,OAAO,CAACiB,KAAK,GAAG,IAAG;UAEnBO,UAAU,CAAC,MAAM;YACfxB,OAAO,CAACiB,KAAK,GAAG,KAAI;;YAEpB;YACA,IAAItB,SAAS,CAACC,QAAQ,KAAK,OAAO,IAAID,SAAS,CAACE,QAAQ,KAAK,QAAQ,EAAE;cACrEL,cAAc,CAAC;gBACbiC,KAAK,EAAE,MAAM;gBACbrB,OAAO,EAAE,QAAQT,SAAS,CAACC,QAAQ,GAAG;gBACtC8B,IAAI,EAAE,SAAS;gBACfC,QAAQ,EAAE;cACZ,CAAC;;cAED;cACAC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,MAAM;cACzCD,YAAY,CAACC,OAAO,CAAC,UAAU,EAAElC,SAAS,CAACC,QAAQ;;cAEnD;cACAF,MAAM,CAACoC,IAAI,CAAC,GAAG;YACjB,CAAC,MAAM;cACLvC,SAAS,CAACgC,KAAK,CAAC,UAAU;cAC1BL,cAAc,CAAC;YACjB;UACF,CAAC,EAAE,IAAI;QACT,CAAC,MAAM;UACL3B,SAAS,CAACgC,KAAK,CAAC,YAAY;UAC5B,OAAO,KAAI;QACb;MACF,CAAC;IACH;;IAEA;IACA,MAAMQ,oBAAoB,GAAGA,CAAA,KAAM;MACjCxC,SAAS,CAACyC,IAAI,CAAC,cAAc;IAC/B;;IAEA;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B1C,SAAS,CAACyC,IAAI,CAAC,cAAc;IAC/B;;IAEA;IACA,MAAME,aAAa,GAAGA,CAAA,KAAM;MAC1B3C,SAAS,CAACyC,IAAI,CAAC,cAAc;IAC/B;;IAEA;IACA,MAAMG,cAAc,GAAGA,CAAA,KAAM;MAC3B5C,SAAS,CAACyC,IAAI,CAAC,YAAY;MAC3B;IACF;;IAEA;IACA3C,SAAS,CAAC,MAAM;MACdmB,eAAe,CAAC;;MAEhB;MACA,MAAM4B,aAAa,GAAGR,YAAY,CAACS,OAAO,CAAC,eAAe;MAC1D,IAAID,aAAa,EAAE;QACjBzC,SAAS,CAACC,QAAQ,GAAGwC,aAAY;QACjCrC,UAAU,CAACkB,KAAK,GAAG,IAAG;MACxB;IACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}