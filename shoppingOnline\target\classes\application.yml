server:
  port: 9192
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************
    username: root
    password: 123456
  servlet:
    multipart:
      max-file-size: 30MB
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    lettuce:
      pool:
        min-idle: 0
        max-active: 8
        max-idle: 8
        max-wait: -1ms
    connect-timeout: 30000ms

mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
