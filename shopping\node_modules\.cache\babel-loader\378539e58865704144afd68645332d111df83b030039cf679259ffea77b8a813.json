{"ast": null, "code": "import asciiWords from './_asciiWords.js';\nimport hasUnicodeWord from './_hasUnicodeWord.js';\nimport toString from './toString.js';\nimport unicodeWords from './_unicodeWords.js';\n\n/**\n * Splits `string` into an array of its words.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to inspect.\n * @param {RegExp|string} [pattern] The pattern to match words.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the words of `string`.\n * @example\n *\n * _.words('fred, barney, & pebbles');\n * // => ['fred', 'barney', 'pebbles']\n *\n * _.words('fred, barney, & pebbles', /[^, ]+/g);\n * // => ['fred', 'barney', '&', 'pebbles']\n */\nfunction words(string, pattern, guard) {\n  string = toString(string);\n  pattern = guard ? undefined : pattern;\n  if (pattern === undefined) {\n    return hasUnicodeWord(string) ? unicodeWords(string) : asciiWords(string);\n  }\n  return string.match(pattern) || [];\n}\nexport default words;", "map": {"version": 3, "names": ["<PERSON>cii<PERSON><PERSON><PERSON>", "hasUnicodeWord", "toString", "unicodeWords", "words", "string", "pattern", "guard", "undefined", "match"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/words.js"], "sourcesContent": ["import asciiWords from './_asciiWords.js';\nimport hasUnicodeWord from './_hasUnicodeWord.js';\nimport toString from './toString.js';\nimport unicodeWords from './_unicodeWords.js';\n\n/**\n * Splits `string` into an array of its words.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to inspect.\n * @param {RegExp|string} [pattern] The pattern to match words.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the words of `string`.\n * @example\n *\n * _.words('fred, barney, & pebbles');\n * // => ['fred', 'barney', 'pebbles']\n *\n * _.words('fred, barney, & pebbles', /[^, ]+/g);\n * // => ['fred', 'barney', '&', 'pebbles']\n */\nfunction words(string, pattern, guard) {\n  string = toString(string);\n  pattern = guard ? undefined : pattern;\n\n  if (pattern === undefined) {\n    return hasUnicodeWord(string) ? unicodeWords(string) : asciiWords(string);\n  }\n  return string.match(pattern) || [];\n}\n\nexport default words;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,YAAY,MAAM,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACrCF,MAAM,GAAGH,QAAQ,CAACG,MAAM,CAAC;EACzBC,OAAO,GAAGC,KAAK,GAAGC,SAAS,GAAGF,OAAO;EAErC,IAAIA,OAAO,KAAKE,SAAS,EAAE;IACzB,OAAOP,cAAc,CAACI,MAAM,CAAC,GAAGF,YAAY,CAACE,MAAM,CAAC,GAAGL,UAAU,CAACK,MAAM,CAAC;EAC3E;EACA,OAAOA,MAAM,CAACI,KAAK,CAACH,OAAO,CAAC,IAAI,EAAE;AACpC;AAEA,eAAeF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}