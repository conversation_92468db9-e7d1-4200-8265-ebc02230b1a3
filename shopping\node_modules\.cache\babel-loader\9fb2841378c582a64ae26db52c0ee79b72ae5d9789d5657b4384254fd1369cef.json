{"ast": null, "code": "import baseForOwnRight from './_baseForOwnRight.js';\nimport createBaseEach from './_createBaseEach.js';\n\n/**\n * The base implementation of `_.forEachRight` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array|Object} Returns `collection`.\n */\nvar baseEachRight = createBaseEach(baseForOwnRight, true);\nexport default baseEachRight;", "map": {"version": 3, "names": ["baseForOwnRight", "createBaseEach", "baseEachRight"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_baseEachRight.js"], "sourcesContent": ["import baseForOwnRight from './_baseForOwnRight.js';\nimport createBaseEach from './_createBaseEach.js';\n\n/**\n * The base implementation of `_.forEachRight` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array|Object} Returns `collection`.\n */\nvar baseEachRight = createBaseEach(baseForOwnRight, true);\n\nexport default baseEachRight;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;AACnD,OAAOC,cAAc,MAAM,sBAAsB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAGD,cAAc,CAACD,eAAe,EAAE,IAAI,CAAC;AAEzD,eAAeE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}