{"ast": null, "code": "import arrayMap from './_arrayMap.js';\nimport baseIntersection from './_baseIntersection.js';\nimport baseRest from './_baseRest.js';\nimport castArrayLikeObject from './_castArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.intersection` except that it accepts `comparator`\n * which is invoked to compare elements of `arrays`. The order and references\n * of result values are determined by the first array. The comparator is\n * invoked with two arguments: (arrVal, othVal).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of intersecting values.\n * @example\n *\n * var objects = [{ 'x': 1, 'y': 2 }, { 'x': 2, 'y': 1 }];\n * var others = [{ 'x': 1, 'y': 1 }, { 'x': 1, 'y': 2 }];\n *\n * _.intersectionWith(objects, others, _.isEqual);\n * // => [{ 'x': 1, 'y': 2 }]\n */\nvar intersectionWith = baseRest(function (arrays) {\n  var comparator = last(arrays),\n    mapped = arrayMap(arrays, castArrayLikeObject);\n  comparator = typeof comparator == 'function' ? comparator : undefined;\n  if (comparator) {\n    mapped.pop();\n  }\n  return mapped.length && mapped[0] === arrays[0] ? baseIntersection(mapped, undefined, comparator) : [];\n});\nexport default intersectionWith;", "map": {"version": 3, "names": ["arrayMap", "baseIntersection", "baseRest", "castArrayLikeObject", "last", "intersectionWith", "arrays", "comparator", "mapped", "undefined", "pop", "length"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/intersectionWith.js"], "sourcesContent": ["import arrayMap from './_arrayMap.js';\nimport baseIntersection from './_baseIntersection.js';\nimport baseRest from './_baseRest.js';\nimport castArrayLikeObject from './_castArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.intersection` except that it accepts `comparator`\n * which is invoked to compare elements of `arrays`. The order and references\n * of result values are determined by the first array. The comparator is\n * invoked with two arguments: (arrVal, othVal).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of intersecting values.\n * @example\n *\n * var objects = [{ 'x': 1, 'y': 2 }, { 'x': 2, 'y': 1 }];\n * var others = [{ 'x': 1, 'y': 1 }, { 'x': 1, 'y': 2 }];\n *\n * _.intersectionWith(objects, others, _.isEqual);\n * // => [{ 'x': 1, 'y': 2 }]\n */\nvar intersectionWith = baseRest(function(arrays) {\n  var comparator = last(arrays),\n      mapped = arrayMap(arrays, castArrayLikeObject);\n\n  comparator = typeof comparator == 'function' ? comparator : undefined;\n  if (comparator) {\n    mapped.pop();\n  }\n  return (mapped.length && mapped[0] === arrays[0])\n    ? baseIntersection(mapped, undefined, comparator)\n    : [];\n});\n\nexport default intersectionWith;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,gBAAgB,MAAM,wBAAwB;AACrD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,mBAAmB,MAAM,2BAA2B;AAC3D,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,gBAAgB,GAAGH,QAAQ,CAAC,UAASI,MAAM,EAAE;EAC/C,IAAIC,UAAU,GAAGH,IAAI,CAACE,MAAM,CAAC;IACzBE,MAAM,GAAGR,QAAQ,CAACM,MAAM,EAAEH,mBAAmB,CAAC;EAElDI,UAAU,GAAG,OAAOA,UAAU,IAAI,UAAU,GAAGA,UAAU,GAAGE,SAAS;EACrE,IAAIF,UAAU,EAAE;IACdC,MAAM,CAACE,GAAG,CAAC,CAAC;EACd;EACA,OAAQF,MAAM,CAACG,MAAM,IAAIH,MAAM,CAAC,CAAC,CAAC,KAAKF,MAAM,CAAC,CAAC,CAAC,GAC5CL,gBAAgB,CAACO,MAAM,EAAEC,SAAS,EAAEF,UAAU,CAAC,GAC/C,EAAE;AACR,CAAC,CAAC;AAEF,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}