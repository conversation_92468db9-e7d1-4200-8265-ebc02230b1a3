{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, with<PERSON><PERSON><PERSON> as _with<PERSON>ey<PERSON>, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"login-container\"\n};\nconst _hoisted_2 = {\n  class: \"login-box\"\n};\nconst _hoisted_3 = {\n  class: \"login-options\"\n};\nconst _hoisted_4 = {\n  class: \"register-link\"\n};\nconst _hoisted_5 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_el_link = _resolveComponent(\"el-link\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n    class: \"login-header\"\n  }, [_createElementVNode(\"h2\", null, \"🛒 购物商城\"), _createElementVNode(\"p\", null, \"欢迎登录\")], -1 /* CACHED */)), _createVNode(_component_el_form, {\n    ref: \"loginFormRef\",\n    model: $setup.loginForm,\n    rules: $setup.loginRules,\n    class: \"login-form\",\n    onKeyup: _withKeys($setup.handleLogin, [\"enter\"])\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 用户名输入框 \"), _createVNode(_component_el_form_item, {\n      prop: \"username\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.loginForm.username,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.loginForm.username = $event),\n        placeholder: \"请输入用户名\",\n        \"prefix-icon\": \"User\",\n        clearable: \"\",\n        size: \"large\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 密码输入框 \"), _createVNode(_component_el_form_item, {\n      prop: \"password\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.loginForm.password,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.loginForm.password = $event),\n        type: \"password\",\n        placeholder: \"请输入密码\",\n        \"prefix-icon\": \"Lock\",\n        \"show-password\": \"\",\n        size: \"large\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 记住密码和忘记密码 \"), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_checkbox, {\n        modelValue: $setup.rememberMe,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.rememberMe = $event)\n      }, {\n        default: _withCtx(() => [...(_cache[12] || (_cache[12] = [_createTextVNode(\"记住密码\", -1 /* CACHED */)]))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_link, {\n        type: \"primary\",\n        onClick: $setup.handleForgetPassword\n      }, {\n        default: _withCtx(() => [...(_cache[13] || (_cache[13] = [_createTextVNode(\"忘记密码？\", -1 /* CACHED */)]))]),\n        _: 1 /* STABLE */\n      })])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 登录按钮 \"), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        size: \"large\",\n        class: \"login-button\",\n        loading: $setup.loading,\n        onClick: $setup.handleLogin,\n        block: \"\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.loading ? '登录中...' : '登录'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"loading\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 注册链接 \"), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_cache[15] || (_cache[15] = _createTextVNode(\" 还没有账号？ \", -1 /* CACHED */)), _createVNode(_component_el_link, {\n        type: \"primary\",\n        onClick: $setup.showRegisterDialog\n      }, {\n        default: _withCtx(() => [...(_cache[14] || (_cache[14] = [_createTextVNode(\"立即注册\", -1 /* CACHED */)]))]),\n        _: 1 /* STABLE */\n      })])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\"])]), _createCommentVNode(\" 注册对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"用户注册\",\n    modelValue: $setup.registerDialogVisible,\n    \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.registerDialogVisible = $event),\n    width: \"500px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_5, [_createVNode(_component_el_button, {\n      onClick: _cache[10] || (_cache[10] = $event => $setup.registerDialogVisible = false)\n    }, {\n      default: _withCtx(() => [...(_cache[17] || (_cache[17] = [_createTextVNode(\"取消\", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.handleRegister,\n      loading: $setup.registerLoading\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.registerLoading ? '注册中...' : '注册'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"loading\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      ref: \"registerFormRef\",\n      model: $setup.registerForm,\n      rules: $setup.registerRules,\n      \"label-width\": \"80px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"用户名\",\n        prop: \"username\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.username,\n          \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.registerForm.username = $event),\n          placeholder: \"请输入用户名\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"密码\",\n        prop: \"password\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.password,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.registerForm.password = $event),\n          type: \"password\",\n          placeholder: \"请输入密码\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"确认密码\",\n        prop: \"confirmPassword\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.confirmPassword,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.registerForm.confirmPassword = $event),\n          type: \"password\",\n          placeholder: \"请再次输入密码\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"昵称\",\n        prop: \"nickname\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.nickname,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.registerForm.nickname = $event),\n          placeholder: \"请输入昵称\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"邮箱\",\n        prop: \"email\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.email,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.registerForm.email = $event),\n          placeholder: \"请输入邮箱\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"电话\",\n        prop: \"phone\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.phone,\n          \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.registerForm.phone = $event),\n          placeholder: \"请输入电话号码\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"地址\",\n        prop: \"address\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.address,\n          \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.registerForm.address = $event),\n          placeholder: \"请输入地址\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_form", "ref", "model", "$setup", "loginForm", "rules", "loginRules", "onKeyup", "_with<PERSON><PERSON><PERSON>", "handleLogin", "_createCommentVNode", "_component_el_form_item", "prop", "_component_el_input", "username", "$event", "placeholder", "clearable", "size", "password", "type", "_hoisted_3", "_component_el_checkbox", "rememberMe", "_cache", "_component_el_link", "onClick", "handleForgetPassword", "_component_el_button", "loading", "block", "_hoisted_4", "showRegisterDialog", "_component_el_dialog", "title", "registerDialogVisible", "width", "footer", "_withCtx", "_hoisted_5", "handleRegister", "registerLoading", "registerForm", "registerRules", "label", "confirmPassword", "nickname", "email", "phone", "address"], "sources": ["D:\\2025_down\\project\\shoppingOnline-20250826-sfl\\shoppingOnline-20250826\\shopping\\src\\components\\login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-box\">\n      <div class=\"login-header\">\n        <h2>🛒 购物商城</h2>\n        <p>欢迎登录</p>\n      </div>\n      \n      <el-form\n        ref=\"loginFormRef\"\n        :model=\"loginForm\"\n        :rules=\"loginRules\"\n        class=\"login-form\"\n        @keyup.enter=\"handleLogin\"\n      >\n        <!-- 用户名输入框 -->\n        <el-form-item prop=\"username\">\n          <el-input\n            v-model=\"loginForm.username\"\n            placeholder=\"请输入用户名\"\n            prefix-icon=\"User\"\n            clearable\n            size=\"large\"\n          />\n        </el-form-item>\n        \n        <!-- 密码输入框 -->\n        <el-form-item prop=\"password\">\n          <el-input\n            v-model=\"loginForm.password\"\n            type=\"password\"\n            placeholder=\"请输入密码\"\n            prefix-icon=\"Lock\"\n            show-password\n            size=\"large\"\n          />\n        </el-form-item>\n        \n\n        \n        <!-- 记住密码和忘记密码 -->\n        <el-form-item>\n          <div class=\"login-options\">\n            <el-checkbox v-model=\"rememberMe\">记住密码</el-checkbox>\n            <el-link type=\"primary\" @click=\"handleForgetPassword\">忘记密码？</el-link>\n          </div>\n        </el-form-item>\n        \n        <!-- 登录按钮 -->\n        <el-form-item>\n          <el-button\n            type=\"primary\"\n            size=\"large\"\n            class=\"login-button\"\n            :loading=\"loading\"\n            @click=\"handleLogin\"\n            block\n          >\n            {{ loading ? '登录中...' : '登录' }}\n          </el-button>\n        </el-form-item>\n\n        <!-- 注册链接 -->\n        <el-form-item>\n          <div class=\"register-link\">\n            还没有账号？\n            <el-link type=\"primary\" @click=\"showRegisterDialog\">立即注册</el-link>\n          </div>\n        </el-form-item>\n\n      </el-form>\n    </div>\n\n    <!-- 注册对话框 -->\n    <el-dialog\n      title=\"用户注册\"\n      v-model=\"registerDialogVisible\"\n      width=\"500px\"\n    >\n      <el-form\n        ref=\"registerFormRef\"\n        :model=\"registerForm\"\n        :rules=\"registerRules\"\n        label-width=\"80px\"\n      >\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"registerForm.username\" placeholder=\"请输入用户名\" />\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <el-input v-model=\"registerForm.password\" type=\"password\" placeholder=\"请输入密码\" />\n        </el-form-item>\n        <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\n          <el-input v-model=\"registerForm.confirmPassword\" type=\"password\" placeholder=\"请再次输入密码\" />\n        </el-form-item>\n        <el-form-item label=\"昵称\" prop=\"nickname\">\n          <el-input v-model=\"registerForm.nickname\" placeholder=\"请输入昵称\" />\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"registerForm.email\" placeholder=\"请输入邮箱\" />\n        </el-form-item>\n        <el-form-item label=\"电话\" prop=\"phone\">\n          <el-input v-model=\"registerForm.phone\" placeholder=\"请输入电话号码\" />\n        </el-form-item>\n        <el-form-item label=\"地址\" prop=\"address\">\n          <el-input v-model=\"registerForm.address\" placeholder=\"请输入地址\" />\n        </el-form-item>\n      </el-form>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"registerDialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"handleRegister\" :loading=\"registerLoading\">\n            {{ registerLoading ? '注册中...' : '注册' }}\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage, ElNotification } from 'element-plus'\n\n// 表单引用\nconst loginFormRef = ref()\nconst registerFormRef = ref()\n\n// 路由实例\nconst router = useRouter()\n\n// 登录表单数据\nconst loginForm = reactive({\n  username: '',\n  password: ''\n})\n\n// 注册表单数据\nconst registerForm = reactive({\n  username: '',\n  password: '',\n  confirmPassword: '',\n  nickname: '',\n  email: '',\n  phone: '',\n  address: ''\n})\n\n// 记住密码\nconst rememberMe = ref(false)\n\n// 加载状态\nconst loading = ref(false)\nconst registerLoading = ref(false)\n\n// 注册对话框显示状态\nconst registerDialogVisible = ref(false)\n\n// 表单验证规则\nconst loginRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 2, max: 20, message: '用户名长度为2-20个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' }\n  ]\n}\n\n// 注册表单验证规则\nconst registerRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' },\n    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }\n  ],\n  confirmPassword: [\n    { required: true, message: '请确认密码', trigger: 'blur' },\n    {\n      validator: (rule, value, callback) => {\n        if (value !== registerForm.password) {\n          callback(new Error('两次输入的密码不一致'))\n        } else {\n          callback()\n        }\n      },\n      trigger: 'blur'\n    }\n  ],\n  nickname: [\n    { required: true, message: '请输入昵称', trigger: 'blur' }\n  ],\n  email: [\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\n  ]\n}\n\n\n\n// 处理登录\nconst handleLogin = async () => {\n  if (!loginFormRef.value) return\n\n  await loginFormRef.value.validate(async (valid) => {\n    if (valid) {\n      // 调用后端登录接口\n      loading.value = true\n\n      try {\n        const response = await fetch('http://localhost:9192/userAPI/login', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            username: loginForm.username,\n            password: loginForm.password\n          })\n        })\n\n        const result = await response.json()\n\n        if (result.code === '200') {\n          const { user, token } = result.data\n\n          ElNotification({\n            title: '登录成功',\n            message: `欢迎回来，${user.username}！`,\n            type: 'success',\n            duration: 2000\n          })\n\n          // 保存登录状态和用户信息\n          localStorage.setItem('isLoggedIn', 'true')\n          localStorage.setItem('username', user.username)\n          localStorage.setItem('token', token)\n          localStorage.setItem('userId', user.id)\n\n          // 登录成功后跳转到商品页面\n          router.push('/')\n        } else {\n          ElMessage.error(result.msg || '登录失败')\n        }\n      } catch (error) {\n        console.error('登录请求失败:', error)\n        ElMessage.error('网络错误，请稍后重试')\n      } finally {\n        loading.value = false\n      }\n    } else {\n      ElMessage.error('请填写正确的登录信息')\n      return false\n    }\n  })\n}\n\n// 忘记密码\nconst handleForgetPassword = () => {\n  ElMessage.info('忘记密码功能开发中...')\n}\n\n// 显示注册对话框\nconst showRegisterDialog = () => {\n  registerDialogVisible.value = true\n  // 重置注册表单\n  registerForm.username = ''\n  registerForm.password = ''\n  registerForm.confirmPassword = ''\n  registerForm.nickname = ''\n  registerForm.email = ''\n  registerForm.phone = ''\n  registerForm.address = ''\n}\n\n// 处理注册\nconst handleRegister = async () => {\n  if (!registerFormRef.value) return\n\n  await registerFormRef.value.validate(async (valid) => {\n    if (valid) {\n      registerLoading.value = true\n\n      try {\n        const response = await fetch('http://localhost:9192/userAPI/register', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            username: registerForm.username,\n            password: registerForm.password,\n            nickname: registerForm.nickname,\n            email: registerForm.email,\n            phone: registerForm.phone,\n            address: registerForm.address,\n            role: 'user' // 默认为普通用户\n          })\n        })\n\n        const result = await response.json()\n\n        if (result.code === '200') {\n          ElNotification({\n            title: '注册成功',\n            message: '账号注册成功，请使用新账号登录！',\n            type: 'success',\n            duration: 3000\n          })\n\n          registerDialogVisible.value = false\n\n          // 将注册的用户名填入登录表单\n          loginForm.username = registerForm.username\n        } else {\n          ElMessage.error(result.msg || '注册失败')\n        }\n      } catch (error) {\n        console.error('注册请求失败:', error)\n        ElMessage.error('网络错误，请稍后重试')\n      } finally {\n        registerLoading.value = false\n      }\n    } else {\n      ElMessage.error('请填写正确的注册信息')\n      return false\n    }\n  })\n}\n\n// 组件挂载时的初始化\nonMounted(() => {\n  // 检查是否记住密码\n  const savedUsername = localStorage.getItem('savedUsername')\n  if (savedUsername) {\n    loginForm.username = savedUsername\n    rememberMe.value = true\n  }\n})\n</script>\n\n<style scoped>\n.login-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n}\n\n.login-box {\n  width: 100%;\n  max-width: 400px;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 10px;\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  padding: 40px 30px;\n  backdrop-filter: blur(10px);\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.login-header h2 {\n  color: #333;\n  margin-bottom: 10px;\n  font-size: 24px;\n}\n\n.login-header p {\n  color: #666;\n  font-size: 14px;\n}\n\n.login-form {\n  width: 100%;\n}\n\n.login-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.login-button {\n  margin-top: 10px;\n}\n\n.register-link {\n  text-align: center;\n  color: #666;\n  font-size: 14px;\n  margin-top: 15px;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n\n:deep(.el-input__wrapper) {\n  border-radius: 20px;\n}\n\n:deep(.el-button) {\n  border-radius: 20px;\n}\n\n@media (max-width: 480px) {\n  .login-box {\n    padding: 30px 20px;\n    margin: 10px;\n  }\n  \n\n}\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAW;;EAwCXA,KAAK,EAAC;AAAe;;EAsBrBA,KAAK,EAAC;AAAe;;EA6CtBA,KAAK,EAAC;AAAe;;;;;;;;;uBA5GjCC,mBAAA,CAoHM,OApHNC,UAoHM,GAnHJC,mBAAA,CAqEM,OArENC,UAqEM,G,4BApEJD,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAc,IACvBG,mBAAA,CAAgB,YAAZ,SAAO,GACXA,mBAAA,CAAW,WAAR,MAAI,E,qBAGTE,YAAA,CA8DUC,kBAAA;IA7DRC,GAAG,EAAC,cAAc;IACjBC,KAAK,EAAEC,MAAA,CAAAC,SAAS;IAChBC,KAAK,EAAEF,MAAA,CAAAG,UAAU;IAClBZ,KAAK,EAAC,YAAY;IACjBa,OAAK,EAAAC,SAAA,CAAQL,MAAA,CAAAM,WAAW;;sBAEzB,MAAe,CAAfC,mBAAA,YAAe,EACfX,YAAA,CAQeY,uBAAA;MARDC,IAAI,EAAC;IAAU;wBAC3B,MAME,CANFb,YAAA,CAMEc,mBAAA;oBALSV,MAAA,CAAAC,SAAS,CAACU,QAAQ;mEAAlBX,MAAA,CAAAC,SAAS,CAACU,QAAQ,GAAAC,MAAA;QAC3BC,WAAW,EAAC,QAAQ;QACpB,aAAW,EAAC,MAAM;QAClBC,SAAS,EAAT,EAAS;QACTC,IAAI,EAAC;;;QAITR,mBAAA,WAAc,EACdX,YAAA,CASeY,uBAAA;MATDC,IAAI,EAAC;IAAU;wBAC3B,MAOE,CAPFb,YAAA,CAOEc,mBAAA;oBANSV,MAAA,CAAAC,SAAS,CAACe,QAAQ;mEAAlBhB,MAAA,CAAAC,SAAS,CAACe,QAAQ,GAAAJ,MAAA;QAC3BK,IAAI,EAAC,UAAU;QACfJ,WAAW,EAAC,OAAO;QACnB,aAAW,EAAC,MAAM;QAClB,eAAa,EAAb,EAAa;QACbE,IAAI,EAAC;;;QAMTR,mBAAA,eAAkB,EAClBX,YAAA,CAKeY,uBAAA;wBAJb,MAGM,CAHNd,mBAAA,CAGM,OAHNwB,UAGM,GAFJtB,YAAA,CAAoDuB,sBAAA;oBAA9BnB,MAAA,CAAAoB,UAAU;mEAAVpB,MAAA,CAAAoB,UAAU,GAAAR,MAAA;;0BAAE,MAAI,KAAAS,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,mB;;yCACtCzB,YAAA,CAAqE0B,kBAAA;QAA5DL,IAAI,EAAC,SAAS;QAAEM,OAAK,EAAEvB,MAAA,CAAAwB;;0BAAsB,MAAK,KAAAH,MAAA,SAAAA,MAAA,Q,iBAAL,OAAK,mB;;;;QAI/Dd,mBAAA,UAAa,EACbX,YAAA,CAWeY,uBAAA;wBAVb,MASY,CATZZ,YAAA,CASY6B,oBAAA;QARVR,IAAI,EAAC,SAAS;QACdF,IAAI,EAAC,OAAO;QACZxB,KAAK,EAAC,cAAc;QACnBmC,OAAO,EAAE1B,MAAA,CAAA0B,OAAO;QAChBH,OAAK,EAAEvB,MAAA,CAAAM,WAAW;QACnBqB,KAAK,EAAL;;0BAEA,MAA+B,C,kCAA5B3B,MAAA,CAAA0B,OAAO,mC;;;;QAIdnB,mBAAA,UAAa,EACbX,YAAA,CAKeY,uBAAA;wBAJb,MAGM,CAHNd,mBAAA,CAGM,OAHNkC,UAGM,G,6CAHqB,UAEzB,qBAAAhC,YAAA,CAAkE0B,kBAAA;QAAzDL,IAAI,EAAC,SAAS;QAAEM,OAAK,EAAEvB,MAAA,CAAA6B;;0BAAoB,MAAI,KAAAR,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,mB;;;;;;kCAOhEd,mBAAA,WAAc,EACdX,YAAA,CA0CYkC,oBAAA;IAzCVC,KAAK,EAAC,MAAM;gBACH/B,MAAA,CAAAgC,qBAAqB;iEAArBhC,MAAA,CAAAgC,qBAAqB,GAAApB,MAAA;IAC9BqB,KAAK,EAAC;;IA+BKC,MAAM,EAAAC,QAAA,CACf,MAKO,CALPzC,mBAAA,CAKO,QALP0C,UAKO,GAJLxC,YAAA,CAAgE6B,oBAAA;MAApDF,OAAK,EAAAF,MAAA,SAAAA,MAAA,OAAAT,MAAA,IAAEZ,MAAA,CAAAgC,qBAAqB;;wBAAU,MAAE,KAAAX,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,mB;;QACpDzB,YAAA,CAEY6B,oBAAA;MAFDR,IAAI,EAAC,SAAS;MAAEM,OAAK,EAAEvB,MAAA,CAAAqC,cAAc;MAAGX,OAAO,EAAE1B,MAAA,CAAAsC;;wBAC1D,MAAuC,C,kCAApCtC,MAAA,CAAAsC,eAAe,mC;;;sBAjCxB,MA2BU,CA3BV1C,YAAA,CA2BUC,kBAAA;MA1BRC,GAAG,EAAC,iBAAiB;MACpBC,KAAK,EAAEC,MAAA,CAAAuC,YAAY;MACnBrC,KAAK,EAAEF,MAAA,CAAAwC,aAAa;MACrB,aAAW,EAAC;;wBAEZ,MAEe,CAFf5C,YAAA,CAEeY,uBAAA;QAFDiC,KAAK,EAAC,KAAK;QAAChC,IAAI,EAAC;;0BAC7B,MAAiE,CAAjEb,YAAA,CAAiEc,mBAAA;sBAA9CV,MAAA,CAAAuC,YAAY,CAAC5B,QAAQ;qEAArBX,MAAA,CAAAuC,YAAY,CAAC5B,QAAQ,GAAAC,MAAA;UAAEC,WAAW,EAAC;;;UAExDjB,YAAA,CAEeY,uBAAA;QAFDiC,KAAK,EAAC,IAAI;QAAChC,IAAI,EAAC;;0BAC5B,MAAgF,CAAhFb,YAAA,CAAgFc,mBAAA;sBAA7DV,MAAA,CAAAuC,YAAY,CAACvB,QAAQ;qEAArBhB,MAAA,CAAAuC,YAAY,CAACvB,QAAQ,GAAAJ,MAAA;UAAEK,IAAI,EAAC,UAAU;UAACJ,WAAW,EAAC;;;UAExEjB,YAAA,CAEeY,uBAAA;QAFDiC,KAAK,EAAC,MAAM;QAAChC,IAAI,EAAC;;0BAC9B,MAAyF,CAAzFb,YAAA,CAAyFc,mBAAA;sBAAtEV,MAAA,CAAAuC,YAAY,CAACG,eAAe;qEAA5B1C,MAAA,CAAAuC,YAAY,CAACG,eAAe,GAAA9B,MAAA;UAAEK,IAAI,EAAC,UAAU;UAACJ,WAAW,EAAC;;;UAE/EjB,YAAA,CAEeY,uBAAA;QAFDiC,KAAK,EAAC,IAAI;QAAChC,IAAI,EAAC;;0BAC5B,MAAgE,CAAhEb,YAAA,CAAgEc,mBAAA;sBAA7CV,MAAA,CAAAuC,YAAY,CAACI,QAAQ;qEAArB3C,MAAA,CAAAuC,YAAY,CAACI,QAAQ,GAAA/B,MAAA;UAAEC,WAAW,EAAC;;;UAExDjB,YAAA,CAEeY,uBAAA;QAFDiC,KAAK,EAAC,IAAI;QAAChC,IAAI,EAAC;;0BAC5B,MAA6D,CAA7Db,YAAA,CAA6Dc,mBAAA;sBAA1CV,MAAA,CAAAuC,YAAY,CAACK,KAAK;qEAAlB5C,MAAA,CAAAuC,YAAY,CAACK,KAAK,GAAAhC,MAAA;UAAEC,WAAW,EAAC;;;UAErDjB,YAAA,CAEeY,uBAAA;QAFDiC,KAAK,EAAC,IAAI;QAAChC,IAAI,EAAC;;0BAC5B,MAA+D,CAA/Db,YAAA,CAA+Dc,mBAAA;sBAA5CV,MAAA,CAAAuC,YAAY,CAACM,KAAK;qEAAlB7C,MAAA,CAAAuC,YAAY,CAACM,KAAK,GAAAjC,MAAA;UAAEC,WAAW,EAAC;;;UAErDjB,YAAA,CAEeY,uBAAA;QAFDiC,KAAK,EAAC,IAAI;QAAChC,IAAI,EAAC;;0BAC5B,MAA+D,CAA/Db,YAAA,CAA+Dc,mBAAA;sBAA5CV,MAAA,CAAAuC,YAAY,CAACO,OAAO;qEAApB9C,MAAA,CAAAuC,YAAY,CAACO,OAAO,GAAAlC,MAAA;UAAEC,WAAW,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}