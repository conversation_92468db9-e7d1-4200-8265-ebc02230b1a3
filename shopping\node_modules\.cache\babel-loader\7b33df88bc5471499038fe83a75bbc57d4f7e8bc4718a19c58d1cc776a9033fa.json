{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, inject, watch, onBeforeUnmount, openBlock, createBlock, unref, withCtx, renderSlot, createElementBlock, mergeProps } from 'vue';\nimport { tooltipV2RootKey } from './constants.mjs';\nimport ForwardRef from './forward-ref.mjs';\nimport { tooltipV2TriggerProps } from './trigger.mjs';\nimport { tooltipV2CommonProps } from './common.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { composeEventHandlers } from '../../../utils/dom/event.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTooltipV2Trigger\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: {\n    ...tooltipV2CommonProps,\n    ...tooltipV2TriggerProps\n  },\n  setup(__props) {\n    const props = __props;\n    const {\n      onClose,\n      onOpen,\n      onDelayOpen,\n      triggerRef,\n      contentId\n    } = inject(tooltipV2RootKey);\n    let isMousedown = false;\n    const setTriggerRef = el => {\n      triggerRef.value = el;\n    };\n    const onMouseup = () => {\n      isMousedown = false;\n    };\n    const onMouseenter = composeEventHandlers(props.onMouseEnter, onDelayOpen);\n    const onMouseleave = composeEventHandlers(props.onMouseLeave, onClose);\n    const onMousedown = composeEventHandlers(props.onMouseDown, () => {\n      onClose();\n      isMousedown = true;\n      document.addEventListener(\"mouseup\", onMouseup, {\n        once: true\n      });\n    });\n    const onFocus = composeEventHandlers(props.onFocus, () => {\n      if (!isMousedown) onOpen();\n    });\n    const onBlur = composeEventHandlers(props.onBlur, onClose);\n    const onClick = composeEventHandlers(props.onClick, e => {\n      if (e.detail === 0) onClose();\n    });\n    const events = {\n      blur: onBlur,\n      click: onClick,\n      focus: onFocus,\n      mousedown: onMousedown,\n      mouseenter: onMouseenter,\n      mouseleave: onMouseleave\n    };\n    const setEvents = (el, events2, type) => {\n      if (el) {\n        Object.entries(events2).forEach(([name, handler]) => {\n          el[type](name, handler);\n        });\n      }\n    };\n    watch(triggerRef, (triggerEl, previousTriggerEl) => {\n      setEvents(triggerEl, events, \"addEventListener\");\n      setEvents(previousTriggerEl, events, \"removeEventListener\");\n      if (triggerEl) {\n        triggerEl.setAttribute(\"aria-describedby\", contentId.value);\n      }\n    });\n    onBeforeUnmount(() => {\n      setEvents(triggerRef.value, events, \"removeEventListener\");\n      document.removeEventListener(\"mouseup\", onMouseup);\n    });\n    return (_ctx, _cache) => {\n      return _ctx.nowrap ? (openBlock(), createBlock(unref(ForwardRef), {\n        key: 0,\n        \"set-ref\": setTriggerRef,\n        \"only-child\": \"\"\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      })) : (openBlock(), createElementBlock(\"button\", mergeProps({\n        key: 1,\n        ref_key: \"triggerRef\",\n        ref: triggerRef\n      }, _ctx.$attrs), [renderSlot(_ctx.$slots, \"default\")], 16));\n    };\n  }\n});\nvar TooltipV2Trigger = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"trigger.vue\"]]);\nexport { TooltipV2Trigger as default };", "map": {"version": 3, "names": ["name", "onClose", "onOpen", "onDelayOpen", "triggerRef", "contentId", "inject", "tooltipV2RootKey", "isMousedown", "setTriggerRef", "el", "value", "onMouseup", "onMouseenter", "composeEventHandlers", "props", "onMouseEnter", "onMouseleave", "onMouseLeave", "onMousedown", "onMouseDown", "document", "addEventListener", "once", "onFocus", "onBlur", "onClick", "e", "detail", "events", "blur", "click", "focus", "mousedown", "mouseenter", "mouseleave", "setEvents", "events2", "type", "Object", "entries", "for<PERSON>ach", "handler", "watch", "triggerEl", "previousTriggerEl", "setAttribute", "onBeforeUnmount"], "sources": ["../../../../../../packages/components/tooltip-v2/src/trigger.vue"], "sourcesContent": ["<template>\n  <forward-ref v-if=\"nowrap\" :set-ref=\"setTriggerRef\" only-child>\n    <slot />\n  </forward-ref>\n  <button v-else ref=\"triggerRef\" v-bind=\"$attrs\">\n    <slot />\n  </button>\n</template>\n\n<script setup lang=\"ts\">\nimport { inject, onBeforeUnmount, watch } from 'vue'\nimport { composeEventHandlers } from '@element-plus/utils'\nimport { tooltipV2RootKey } from './constants'\nimport ForwardRef from './forward-ref'\nimport { tooltipV2TriggerProps } from './trigger'\nimport { tooltipV2CommonProps } from './common'\n\ndefineOptions({\n  name: 'ElTooltipV2Trigger',\n})\n\nconst props = defineProps({\n  ...tooltipV2CommonProps,\n  ...tooltipV2TriggerProps,\n})\n\n/**\n * onOpen opens the tooltip instantly, onTrigger acts a lil bit differently,\n * it will check if delayDuration is set to greater than 0 and based on that result,\n * if true, it opens the tooltip after delayDuration, otherwise it opens it instantly.\n */\nconst { onClose, onOpen, onDelayOpen, triggerRef, contentId } =\n  inject(tooltipV2RootKey)!\n\nlet isMousedown = false\n\nconst setTriggerRef = (el: HTMLElement | null) => {\n  triggerRef.value = el\n}\n\nconst onMouseup = () => {\n  isMousedown = false\n}\n\nconst onMouseenter = composeEventHandlers(props.onMouseEnter, onDelayOpen)\n\nconst onMouseleave = composeEventHandlers(props.onMouseLeave, onClose)\n\nconst onMousedown = composeEventHandlers(props.onMouseDown, () => {\n  onClose()\n  isMousedown = true\n  document.addEventListener('mouseup', onMouseup, { once: true })\n})\n\nconst onFocus = composeEventHandlers(props.onFocus, () => {\n  if (!isMousedown) onOpen()\n})\n\nconst onBlur = composeEventHandlers(props.onBlur, onClose)\n\nconst onClick = composeEventHandlers(props.onClick, (e) => {\n  if ((e as MouseEvent).detail === 0) onClose()\n})\n\nconst events = {\n  blur: onBlur,\n  click: onClick,\n  focus: onFocus,\n  mousedown: onMousedown,\n  mouseenter: onMouseenter,\n  mouseleave: onMouseleave,\n}\n\nconst setEvents = <T extends (e: Event) => void>(\n  el: HTMLElement | null | undefined,\n  events: Record<string, T>,\n  type: 'addEventListener' | 'removeEventListener'\n) => {\n  if (el) {\n    Object.entries(events).forEach(([name, handler]) => {\n      el[type](name, handler)\n    })\n  }\n}\n\nwatch(triggerRef, (triggerEl, previousTriggerEl) => {\n  setEvents(triggerEl, events, 'addEventListener')\n  setEvents(previousTriggerEl, events, 'removeEventListener')\n\n  if (triggerEl) {\n    triggerEl.setAttribute('aria-describedby', contentId.value)\n  }\n})\n\nonBeforeUnmount(() => {\n  setEvents(triggerRef.value, events, 'removeEventListener')\n  document.removeEventListener('mouseup', onMouseup)\n})\n</script>\n"], "mappings": ";;;;;;;;;mCAiBc;EACZA,IAAM;AACR;;;;;;;;;IAYM;MAAEC,OAAA;MAASC,MAAQ;MAAAC,WAAA;MAAaC,UAAA;MAAYC;IAAU,IAC1DC,MAAA,CAAOC,gBAAgB;IAEzB,IAAIC,WAAc;IAEZ,MAAAC,aAAA,GAAiBC,EAA2B;MAChDN,UAAA,CAAWO,KAAQ,GAAAD,EAAA;IAAA,CACrB;IAEA,MAAME,SAAA,GAAYA,CAAA,KAAM;MACRJ,WAAA;IAAA,CAChB;IAEA,MAAMK,YAAe,GAAAC,oBAAA,CAAqBC,KAAM,CAAAC,YAAA,EAAcb,WAAW;IAEzE,MAAMc,YAAe,GAAAH,oBAAA,CAAqBC,KAAM,CAAAG,YAAA,EAAcjB,OAAO;IAErE,MAAMkB,WAAc,GAAAL,oBAAA,CAAqBC,KAAM,CAAAK,WAAA,EAAa,MAAM;MACxDnB,OAAA;MACMO,WAAA;MACda,QAAA,CAASC,gBAAA,CAAiB,SAAW,EAAAV,SAAA,EAAW;QAAEW,IAAA,EAAM;MAAA,CAAM;IAAA,CAC/D;IAED,MAAMC,OAAU,GAAAV,oBAAA,CAAqBC,KAAM,CAAAS,OAAA,EAAS,MAAM;MACpD,KAAChB,WAAA,EACNN,MAAA;IAED;IAEA,MAAMuB,MAAU,GAAAX,oBAAA,CAAAC,KAA2B,CAAAU,MAAA,EAAAxB,OAAgB;IACpD,MAAAyB,OAA4B,GAAAZ,oBAAW,CAAAC,KAAA,CAAAW,OAAA,EAAAC,CAAA;MAC7C,IAAAA,CAAA,CAAAC,MAAA,QAED3B,OAAe;IAAA,EACb;IAAM,MACC4B,MAAA;MACPC,IAAO,EAAAL,MAAA;MACPM,KAAW,EAAAL,OAAA;MACXM,KAAY,EAAAR,OAAA;MACZS,SAAY,EAAAd,WAAA;MACde,UAAA,EAAArB,YAAA;MAEAsB,UAAkB,EAAAlB;IAKhB;IACS,MAAAmB,SAAA,GAAAA,CAAQ1B,EAAA,EAAA2B,OAAQ,EAAAC,IAAQ,KAAE;MAC5B,IAAA5B,EAAA;QACL6B,MAAC,CAAAC,OAAA,CAAAH,OAAA,EAAAI,OAAA,GAAAzC,IAAA,EAAA0C,OAAA;UACHhC,EAAA,CAAA4B,IAAA,EAAAtC,IAAA,EAAA0C,OAAA;QAAA,CACF;MAEA;IACE,CAAU;IACAC,KAAA,CAAAvC,UAAA,GAAAwC,SAAA,EAAAC,iBAAgD;MAE1DT,SAAe,CAAAQ,SAAA,EAAAf,MAAA;MACHO,SAAA,CAAAS,iBAAiC,EAAAhB,MAAA,uBAAe;MAC5D,IAAAe,SAAA;QACDA,SAAA,CAAAE,YAAA,qBAAAzC,SAAA,CAAAM,KAAA;MAED;IACE,CAAU;IACDoC,eAAA;MACVX,SAAA,CAAAhC,UAAA,CAAAO,KAAA,EAAAkB,MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}