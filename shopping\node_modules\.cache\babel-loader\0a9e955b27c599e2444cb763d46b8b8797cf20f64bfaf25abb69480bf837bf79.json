{"ast": null, "code": "import baseRest from './_baseRest.js';\nimport createWrap from './_createWrap.js';\nimport getHolder from './_getHolder.js';\nimport replaceHolders from './_replaceHolders.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_PARTIAL_FLAG = 32;\n\n/**\n * Creates a function that invokes `func` with `partials` prepended to the\n * arguments it receives. This method is like `_.bind` except it does **not**\n * alter the `this` binding.\n *\n * The `_.partial.placeholder` value, which defaults to `_` in monolithic\n * builds, may be used as a placeholder for partially applied arguments.\n *\n * **Note:** This method doesn't set the \"length\" property of partially\n * applied functions.\n *\n * @static\n * @memberOf _\n * @since 0.2.0\n * @category Function\n * @param {Function} func The function to partially apply arguments to.\n * @param {...*} [partials] The arguments to be partially applied.\n * @returns {Function} Returns the new partially applied function.\n * @example\n *\n * function greet(greeting, name) {\n *   return greeting + ' ' + name;\n * }\n *\n * var sayHelloTo = _.partial(greet, 'hello');\n * sayHelloTo('fred');\n * // => 'hello fred'\n *\n * // Partially applied with placeholders.\n * var greetFred = _.partial(greet, _, 'fred');\n * greetFred('hi');\n * // => 'hi fred'\n */\nvar partial = baseRest(function (func, partials) {\n  var holders = replaceHolders(partials, getHolder(partial));\n  return createWrap(func, WRAP_PARTIAL_FLAG, undefined, partials, holders);\n});\n\n// Assign default placeholders.\npartial.placeholder = {};\nexport default partial;", "map": {"version": 3, "names": ["baseRest", "createWrap", "getHolder", "replaceHolders", "WRAP_PARTIAL_FLAG", "partial", "func", "partials", "holders", "undefined", "placeholder"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/partial.js"], "sourcesContent": ["import baseRest from './_baseRest.js';\nimport createWrap from './_createWrap.js';\nimport getHolder from './_getHolder.js';\nimport replaceHolders from './_replaceHolders.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_PARTIAL_FLAG = 32;\n\n/**\n * Creates a function that invokes `func` with `partials` prepended to the\n * arguments it receives. This method is like `_.bind` except it does **not**\n * alter the `this` binding.\n *\n * The `_.partial.placeholder` value, which defaults to `_` in monolithic\n * builds, may be used as a placeholder for partially applied arguments.\n *\n * **Note:** This method doesn't set the \"length\" property of partially\n * applied functions.\n *\n * @static\n * @memberOf _\n * @since 0.2.0\n * @category Function\n * @param {Function} func The function to partially apply arguments to.\n * @param {...*} [partials] The arguments to be partially applied.\n * @returns {Function} Returns the new partially applied function.\n * @example\n *\n * function greet(greeting, name) {\n *   return greeting + ' ' + name;\n * }\n *\n * var sayHelloTo = _.partial(greet, 'hello');\n * sayHelloTo('fred');\n * // => 'hello fred'\n *\n * // Partially applied with placeholders.\n * var greetFred = _.partial(greet, _, 'fred');\n * greetFred('hi');\n * // => 'hi fred'\n */\nvar partial = baseRest(function(func, partials) {\n  var holders = replaceHolders(partials, getHolder(partial));\n  return createWrap(func, WRAP_PARTIAL_FLAG, undefined, partials, holders);\n});\n\n// Assign default placeholders.\npartial.placeholder = {};\n\nexport default partial;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,cAAc,MAAM,sBAAsB;;AAEjD;AACA,IAAIC,iBAAiB,GAAG,EAAE;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAGL,QAAQ,CAAC,UAASM,IAAI,EAAEC,QAAQ,EAAE;EAC9C,IAAIC,OAAO,GAAGL,cAAc,CAACI,QAAQ,EAAEL,SAAS,CAACG,OAAO,CAAC,CAAC;EAC1D,OAAOJ,UAAU,CAACK,IAAI,EAAEF,iBAAiB,EAAEK,SAAS,EAAEF,QAAQ,EAAEC,OAAO,CAAC;AAC1E,CAAC,CAAC;;AAEF;AACAH,OAAO,CAACK,WAAW,GAAG,CAAC,CAAC;AAExB,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}