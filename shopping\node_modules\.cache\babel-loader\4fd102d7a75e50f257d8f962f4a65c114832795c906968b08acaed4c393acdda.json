{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport ElementPlus from 'element-plus';\nimport 'element-plus/dist/index.css';\n\n// 创建应用实例\nconst app = createApp(App);\n\n// 注册路由\napp.use(router);\n\n// 注册 Element Plus\napp.use(ElementPlus);\n\n// 挂载到 #app（只挂载一次！）\napp.mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "router", "ElementPlus", "app", "use", "mount"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport ElementPlus from 'element-plus'\nimport 'element-plus/dist/index.css'\n\n// 创建应用实例\nconst app = createApp(App)\n\n// 注册路由\napp.use(router)\n\n// 注册 Element Plus\napp.use(ElementPlus)\n\n// 挂载到 #app（只挂载一次！）\napp.mount('#app')"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAO,6BAA6B;;AAEpC;AACA,MAAMC,GAAG,GAAGJ,SAAS,CAACC,GAAG,CAAC;;AAE1B;AACAG,GAAG,CAACC,GAAG,CAACH,MAAM,CAAC;;AAEf;AACAE,GAAG,CAACC,GAAG,CAACF,WAAW,CAAC;;AAEpB;AACAC,GAAG,CAACE,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}