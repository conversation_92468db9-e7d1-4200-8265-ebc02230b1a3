{"ast": null, "code": "import { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nconst basicCellProps = buildProps({\n  cell: {\n    type: definePropType(Object)\n  }\n});\nexport { basicCellProps };", "map": {"version": 3, "names": ["basicCellProps", "buildProps", "cell", "type", "definePropType", "Object"], "sources": ["../../../../../../../packages/components/date-picker-panel/src/props/basic-cell.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { DateCell } from '../types'\n\nexport const basicCellProps = buildProps({\n  cell: {\n    type: definePropType<DateCell>(Object),\n  },\n} as const)\n\nexport type BasicCellProps = ExtractPropTypes<typeof basicCellProps>\nexport type BasicCellPropsPublic = __ExtractPublicPropTypes<\n  typeof basicCellProps\n>\n"], "mappings": ";AACY,MAACA,cAAc,GAAGC,UAAU,CAAC;EACvCC,IAAI,EAAE;IACJC,IAAI,EAAEC,cAAc,CAACC,MAAM;EAC/B;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}