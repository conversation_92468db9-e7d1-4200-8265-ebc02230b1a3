package com.cn.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.entity.User;
import com.cn.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserService extends ServiceImpl<UserMapper, User> {

    @Autowired
    private  UserMapper userMapper;

    public List<User> queryALL(){
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        return  userMapper.selectList(queryWrapper);
    }


}
