package com.cn.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.entity.User;
import com.cn.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
public class UserService extends ServiceImpl<UserMapper, User> {

    @Autowired
    private  UserMapper userMapper;

    public List<User> queryALL(){
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        return  userMapper.selectList(queryWrapper);
    }

    /**
     * 用户登录验证
     * @param username
     * @param password
     * @return
     */
    public User login(String username, String password) {
        if (!StringUtils.hasLength(username) || !StringUtils.hasLength(password)) {
            System.out.println("登录失败：用户名或密码为空");
            return null;
        }

        System.out.println("尝试登录 - 用户名: " + username + ", 密码: " + password);

        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        queryWrapper.eq("password", password);

        User result = userMapper.selectOne(queryWrapper);
        System.out.println("数据库查询结果: " + (result != null ? "找到用户" : "未找到用户"));

        return result;
    }

    /**
     * 添加用户
     * @param user
     */
    public void addUser(User user) {
        if (!StringUtils.hasLength(user.getUsername()) || !StringUtils.hasLength(user.getPassword())) {
            throw new RuntimeException("用户名和密码不能为空");
        }

        // 检查用户名是否已存在
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", user.getUsername());
        User existUser = userMapper.selectOne(queryWrapper);
        if (existUser != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 设置默认角色
        if (!StringUtils.hasLength(user.getRole())) {
            user.setRole("user");
        }

        userMapper.insert(user);
    }

    /**
     * 更新用户
     * @param user
     */
    public void updateUser(User user) {
        if (user.getId() == null) {
            throw new RuntimeException("用户ID不能为空");
        }
        userMapper.updateById(user);
    }

    /**
     * 删除用户
     * @param id
     */
    public void deleteUser(Integer id) {
        if (id == null) {
            throw new RuntimeException("用户ID不能为空");
        }
        userMapper.deleteById(id);
    }

}
