{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive, onMounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage, ElNotification } from 'element-plus';\n\n// 表单引用\n\nexport default {\n  __name: 'login',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const loginFormRef = ref();\n    const registerFormRef = ref();\n\n    // 路由实例\n    const router = useRouter();\n\n    // 登录表单数据\n    const loginForm = reactive({\n      username: '',\n      password: ''\n    });\n\n    // 注册表单数据\n    const registerForm = reactive({\n      username: '',\n      password: '',\n      confirmPassword: '',\n      nickname: '',\n      email: '',\n      phone: '',\n      address: ''\n    });\n\n    // 记住密码\n    const rememberMe = ref(false);\n\n    // 加载状态\n    const loading = ref(false);\n    const registerLoading = ref(false);\n\n    // 注册对话框显示状态\n    const registerDialogVisible = ref(false);\n\n    // 表单验证规则\n    const loginRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }, {\n        min: 2,\n        max: 20,\n        message: '用户名长度为2-20个字符',\n        trigger: 'blur'\n      }],\n      password: [{\n        required: true,\n        message: '请输入密码',\n        trigger: 'blur'\n      }, {\n        min: 1,\n        max: 20,\n        message: '密码长度为1-20个字符',\n        trigger: 'blur'\n      }]\n    };\n\n    // 注册表单验证规则\n    const registerRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }, {\n        min: 3,\n        max: 20,\n        message: '用户名长度为3-20个字符',\n        trigger: 'blur'\n      }],\n      password: [{\n        required: true,\n        message: '请输入密码',\n        trigger: 'blur'\n      }, {\n        min: 6,\n        max: 20,\n        message: '密码长度为6-20个字符',\n        trigger: 'blur'\n      }],\n      confirmPassword: [{\n        required: true,\n        message: '请确认密码',\n        trigger: 'blur'\n      }, {\n        validator: (rule, value, callback) => {\n          if (value !== registerForm.password) {\n            callback(new Error('两次输入的密码不一致'));\n          } else {\n            callback();\n          }\n        },\n        trigger: 'blur'\n      }],\n      nickname: [{\n        required: true,\n        message: '请输入昵称',\n        trigger: 'blur'\n      }],\n      email: [{\n        type: 'email',\n        message: '请输入正确的邮箱地址',\n        trigger: 'blur'\n      }]\n    };\n\n    // 处理登录\n    const handleLogin = async () => {\n      if (!loginFormRef.value) return;\n      await loginFormRef.value.validate(async valid => {\n        if (valid) {\n          // 调用后端登录接口\n          loading.value = true;\n          try {\n            const response = await fetch('http://localhost:9192/userAPI/login', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                username: loginForm.username,\n                password: loginForm.password\n              })\n            });\n            const result = await response.json();\n            if (result.code === '200') {\n              const {\n                user,\n                token\n              } = result.data;\n              ElNotification({\n                title: '登录成功',\n                message: `欢迎回来，${user.username}！`,\n                type: 'success',\n                duration: 2000\n              });\n\n              // 保存登录状态和用户信息\n              localStorage.setItem('isLoggedIn', 'true');\n              localStorage.setItem('username', user.username);\n              localStorage.setItem('userRole', user.role);\n              localStorage.setItem('token', token);\n              localStorage.setItem('userId', user.id);\n\n              // 根据用户角色跳转\n              if (user.role === 'admin') {\n                router.push('/admin');\n              } else {\n                router.push('/');\n              }\n            } else {\n              ElMessage.error(result.msg || '登录失败');\n            }\n          } catch (error) {\n            console.error('登录请求失败:', error);\n            ElMessage.error('网络错误，请稍后重试');\n          } finally {\n            loading.value = false;\n          }\n        } else {\n          ElMessage.error('请填写正确的登录信息');\n          return false;\n        }\n      });\n    };\n\n    // 忘记密码\n    const handleForgetPassword = () => {\n      ElMessage.info('忘记密码功能开发中...');\n    };\n\n    // 显示注册对话框\n    const showRegisterDialog = () => {\n      registerDialogVisible.value = true;\n      // 重置注册表单\n      registerForm.username = '';\n      registerForm.password = '';\n      registerForm.confirmPassword = '';\n      registerForm.nickname = '';\n      registerForm.email = '';\n      registerForm.phone = '';\n      registerForm.address = '';\n    };\n\n    // 处理注册\n    const handleRegister = async () => {\n      if (!registerFormRef.value) return;\n      await registerFormRef.value.validate(async valid => {\n        if (valid) {\n          registerLoading.value = true;\n          try {\n            const response = await fetch('http://localhost:9192/userAPI/register', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                username: registerForm.username,\n                password: registerForm.password,\n                nickname: registerForm.nickname,\n                email: registerForm.email,\n                phone: registerForm.phone,\n                address: registerForm.address,\n                role: 'user' // 默认为普通用户\n              })\n            });\n            const result = await response.json();\n            if (result.code === '200') {\n              ElNotification({\n                title: '注册成功',\n                message: '账号注册成功，请使用新账号登录！',\n                type: 'success',\n                duration: 3000\n              });\n              registerDialogVisible.value = false;\n\n              // 将注册的用户名填入登录表单\n              loginForm.username = registerForm.username;\n            } else {\n              ElMessage.error(result.msg || '注册失败');\n            }\n          } catch (error) {\n            console.error('注册请求失败:', error);\n            ElMessage.error('网络错误，请稍后重试');\n          } finally {\n            registerLoading.value = false;\n          }\n        } else {\n          ElMessage.error('请填写正确的注册信息');\n          return false;\n        }\n      });\n    };\n\n    // 组件挂载时的初始化\n    onMounted(() => {\n      // 检查是否记住密码\n      const savedUsername = localStorage.getItem('savedUsername');\n      if (savedUsername) {\n        loginForm.username = savedUsername;\n        rememberMe.value = true;\n      }\n    });\n    const __returned__ = {\n      loginFormRef,\n      registerFormRef,\n      router,\n      loginForm,\n      registerForm,\n      rememberMe,\n      loading,\n      registerLoading,\n      registerDialogVisible,\n      loginRules,\n      registerRules,\n      handleLogin,\n      handleForgetPassword,\n      showRegisterDialog,\n      handleRegister,\n      ref,\n      reactive,\n      onMounted,\n      get useRouter() {\n        return useRouter;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElNotification() {\n        return ElNotification;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "useRouter", "ElMessage", "ElNotification", "loginFormRef", "registerFormRef", "router", "loginForm", "username", "password", "registerForm", "confirmPassword", "nickname", "email", "phone", "address", "rememberMe", "loading", "registerLoading", "registerDialogVisible", "loginRules", "required", "message", "trigger", "min", "max", "registerRules", "validator", "rule", "value", "callback", "Error", "type", "handleLogin", "validate", "valid", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "result", "json", "code", "user", "token", "data", "title", "duration", "localStorage", "setItem", "role", "id", "push", "error", "msg", "console", "handleForgetPassword", "info", "showRegisterDialog", "handleRegister", "savedUsername", "getItem"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/src/components/login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-box\">\n      <div class=\"login-header\">\n        <h2>🛒 购物商城</h2>\n        <p>欢迎登录</p>\n      </div>\n      \n      <el-form\n        ref=\"loginFormRef\"\n        :model=\"loginForm\"\n        :rules=\"loginRules\"\n        class=\"login-form\"\n        @keyup.enter=\"handleLogin\"\n      >\n        <!-- 用户名输入框 -->\n        <el-form-item prop=\"username\">\n          <el-input\n            v-model=\"loginForm.username\"\n            placeholder=\"请输入用户名\"\n            prefix-icon=\"User\"\n            clearable\n            size=\"large\"\n          />\n        </el-form-item>\n        \n        <!-- 密码输入框 -->\n        <el-form-item prop=\"password\">\n          <el-input\n            v-model=\"loginForm.password\"\n            type=\"password\"\n            placeholder=\"请输入密码\"\n            prefix-icon=\"Lock\"\n            show-password\n            size=\"large\"\n          />\n        </el-form-item>\n        \n\n        \n        <!-- 记住密码和忘记密码 -->\n        <el-form-item>\n          <div class=\"login-options\">\n            <el-checkbox v-model=\"rememberMe\">记住密码</el-checkbox>\n            <el-link type=\"primary\" @click=\"handleForgetPassword\">忘记密码？</el-link>\n          </div>\n        </el-form-item>\n        \n        <!-- 登录按钮 -->\n        <el-form-item>\n          <el-button\n            type=\"primary\"\n            size=\"large\"\n            class=\"login-button\"\n            :loading=\"loading\"\n            @click=\"handleLogin\"\n            block\n          >\n            {{ loading ? '登录中...' : '登录' }}\n          </el-button>\n        </el-form-item>\n\n        <!-- 注册链接 -->\n        <el-form-item>\n          <div class=\"register-link\">\n            还没有账号？\n            <el-link type=\"primary\" @click=\"showRegisterDialog\">立即注册</el-link>\n          </div>\n        </el-form-item>\n\n      </el-form>\n    </div>\n\n    <!-- 注册对话框 -->\n    <el-dialog\n      title=\"用户注册\"\n      v-model=\"registerDialogVisible\"\n      width=\"500px\"\n    >\n      <el-form\n        ref=\"registerFormRef\"\n        :model=\"registerForm\"\n        :rules=\"registerRules\"\n        label-width=\"80px\"\n      >\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"registerForm.username\" placeholder=\"请输入用户名\" />\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <el-input v-model=\"registerForm.password\" type=\"password\" placeholder=\"请输入密码\" />\n        </el-form-item>\n        <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\n          <el-input v-model=\"registerForm.confirmPassword\" type=\"password\" placeholder=\"请再次输入密码\" />\n        </el-form-item>\n        <el-form-item label=\"昵称\" prop=\"nickname\">\n          <el-input v-model=\"registerForm.nickname\" placeholder=\"请输入昵称\" />\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"registerForm.email\" placeholder=\"请输入邮箱\" />\n        </el-form-item>\n        <el-form-item label=\"电话\" prop=\"phone\">\n          <el-input v-model=\"registerForm.phone\" placeholder=\"请输入电话号码\" />\n        </el-form-item>\n        <el-form-item label=\"地址\" prop=\"address\">\n          <el-input v-model=\"registerForm.address\" placeholder=\"请输入地址\" />\n        </el-form-item>\n      </el-form>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"registerDialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"handleRegister\" :loading=\"registerLoading\">\n            {{ registerLoading ? '注册中...' : '注册' }}\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage, ElNotification } from 'element-plus'\n\n// 表单引用\nconst loginFormRef = ref()\nconst registerFormRef = ref()\n\n// 路由实例\nconst router = useRouter()\n\n// 登录表单数据\nconst loginForm = reactive({\n  username: '',\n  password: ''\n})\n\n// 注册表单数据\nconst registerForm = reactive({\n  username: '',\n  password: '',\n  confirmPassword: '',\n  nickname: '',\n  email: '',\n  phone: '',\n  address: ''\n})\n\n// 记住密码\nconst rememberMe = ref(false)\n\n// 加载状态\nconst loading = ref(false)\nconst registerLoading = ref(false)\n\n// 注册对话框显示状态\nconst registerDialogVisible = ref(false)\n\n// 表单验证规则\nconst loginRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 2, max: 20, message: '用户名长度为2-20个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' },\n    { min: 1, max: 20, message: '密码长度为1-20个字符', trigger: 'blur' }\n  ]\n}\n\n// 注册表单验证规则\nconst registerRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' },\n    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }\n  ],\n  confirmPassword: [\n    { required: true, message: '请确认密码', trigger: 'blur' },\n    {\n      validator: (rule, value, callback) => {\n        if (value !== registerForm.password) {\n          callback(new Error('两次输入的密码不一致'))\n        } else {\n          callback()\n        }\n      },\n      trigger: 'blur'\n    }\n  ],\n  nickname: [\n    { required: true, message: '请输入昵称', trigger: 'blur' }\n  ],\n  email: [\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\n  ]\n}\n\n\n\n// 处理登录\nconst handleLogin = async () => {\n  if (!loginFormRef.value) return\n\n  await loginFormRef.value.validate(async (valid) => {\n    if (valid) {\n      // 调用后端登录接口\n      loading.value = true\n\n      try {\n        const response = await fetch('http://localhost:9192/userAPI/login', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            username: loginForm.username,\n            password: loginForm.password\n          })\n        })\n\n        const result = await response.json()\n\n        if (result.code === '200') {\n          const { user, token } = result.data\n\n          ElNotification({\n            title: '登录成功',\n            message: `欢迎回来，${user.username}！`,\n            type: 'success',\n            duration: 2000\n          })\n\n          // 保存登录状态和用户信息\n          localStorage.setItem('isLoggedIn', 'true')\n          localStorage.setItem('username', user.username)\n          localStorage.setItem('userRole', user.role)\n          localStorage.setItem('token', token)\n          localStorage.setItem('userId', user.id)\n\n          // 根据用户角色跳转\n          if (user.role === 'admin') {\n            router.push('/admin')\n          } else {\n            router.push('/')\n          }\n        } else {\n          ElMessage.error(result.msg || '登录失败')\n        }\n      } catch (error) {\n        console.error('登录请求失败:', error)\n        ElMessage.error('网络错误，请稍后重试')\n      } finally {\n        loading.value = false\n      }\n    } else {\n      ElMessage.error('请填写正确的登录信息')\n      return false\n    }\n  })\n}\n\n// 忘记密码\nconst handleForgetPassword = () => {\n  ElMessage.info('忘记密码功能开发中...')\n}\n\n// 显示注册对话框\nconst showRegisterDialog = () => {\n  registerDialogVisible.value = true\n  // 重置注册表单\n  registerForm.username = ''\n  registerForm.password = ''\n  registerForm.confirmPassword = ''\n  registerForm.nickname = ''\n  registerForm.email = ''\n  registerForm.phone = ''\n  registerForm.address = ''\n}\n\n// 处理注册\nconst handleRegister = async () => {\n  if (!registerFormRef.value) return\n\n  await registerFormRef.value.validate(async (valid) => {\n    if (valid) {\n      registerLoading.value = true\n\n      try {\n        const response = await fetch('http://localhost:9192/userAPI/register', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            username: registerForm.username,\n            password: registerForm.password,\n            nickname: registerForm.nickname,\n            email: registerForm.email,\n            phone: registerForm.phone,\n            address: registerForm.address,\n            role: 'user' // 默认为普通用户\n          })\n        })\n\n        const result = await response.json()\n\n        if (result.code === '200') {\n          ElNotification({\n            title: '注册成功',\n            message: '账号注册成功，请使用新账号登录！',\n            type: 'success',\n            duration: 3000\n          })\n\n          registerDialogVisible.value = false\n\n          // 将注册的用户名填入登录表单\n          loginForm.username = registerForm.username\n        } else {\n          ElMessage.error(result.msg || '注册失败')\n        }\n      } catch (error) {\n        console.error('注册请求失败:', error)\n        ElMessage.error('网络错误，请稍后重试')\n      } finally {\n        registerLoading.value = false\n      }\n    } else {\n      ElMessage.error('请填写正确的注册信息')\n      return false\n    }\n  })\n}\n\n// 组件挂载时的初始化\nonMounted(() => {\n  // 检查是否记住密码\n  const savedUsername = localStorage.getItem('savedUsername')\n  if (savedUsername) {\n    loginForm.username = savedUsername\n    rememberMe.value = true\n  }\n})\n</script>\n\n<style scoped>\n.login-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n}\n\n.login-box {\n  width: 100%;\n  max-width: 400px;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 10px;\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  padding: 40px 30px;\n  backdrop-filter: blur(10px);\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.login-header h2 {\n  color: #333;\n  margin-bottom: 10px;\n  font-size: 24px;\n}\n\n.login-header p {\n  color: #666;\n  font-size: 14px;\n}\n\n.login-form {\n  width: 100%;\n}\n\n.login-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.login-button {\n  margin-top: 10px;\n}\n\n:deep(.el-input__wrapper) {\n  border-radius: 20px;\n}\n\n:deep(.el-button) {\n  border-radius: 20px;\n}\n\n@media (max-width: 480px) {\n  .login-box {\n    padding: 30px 20px;\n    margin: 10px;\n  }\n  \n\n}\n</style>"], "mappings": ";AAyHA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,KAAI;AAC7C,SAASC,SAAS,QAAQ,YAAW;AACrC,SAASC,SAAS,EAAEC,cAAc,QAAQ,cAAa;;AAEvD;;;;;;;;IACA,MAAMC,YAAY,GAAGN,GAAG,CAAC;IACzB,MAAMO,eAAe,GAAGP,GAAG,CAAC;;IAE5B;IACA,MAAMQ,MAAM,GAAGL,SAAS,CAAC;;IAEzB;IACA,MAAMM,SAAS,GAAGR,QAAQ,CAAC;MACzBS,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC;;IAED;IACA,MAAMC,YAAY,GAAGX,QAAQ,CAAC;MAC5BS,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZE,eAAe,EAAE,EAAE;MACnBC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;IACX,CAAC;;IAED;IACA,MAAMC,UAAU,GAAGlB,GAAG,CAAC,KAAK;;IAE5B;IACA,MAAMmB,OAAO,GAAGnB,GAAG,CAAC,KAAK;IACzB,MAAMoB,eAAe,GAAGpB,GAAG,CAAC,KAAK;;IAEjC;IACA,MAAMqB,qBAAqB,GAAGrB,GAAG,CAAC,KAAK;;IAEvC;IACA,MAAMsB,UAAU,GAAG;MACjBZ,QAAQ,EAAE,CACR;QAAEa,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,eAAe;QAAEC,OAAO,EAAE;MAAO,EAC9D;MACDd,QAAQ,EAAE,CACR;QAAEY,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,cAAc;QAAEC,OAAO,EAAE;MAAO;IAEhE;;IAEA;IACA,MAAMG,aAAa,GAAG;MACpBlB,QAAQ,EAAE,CACR;QAAEa,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,eAAe;QAAEC,OAAO,EAAE;MAAO,EAC9D;MACDd,QAAQ,EAAE,CACR;QAAEY,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,cAAc;QAAEC,OAAO,EAAE;MAAO,EAC7D;MACDZ,eAAe,EAAE,CACf;QAAEU,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QACEI,SAAS,EAAEA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,KAAK;UACpC,IAAID,KAAK,KAAKnB,YAAY,CAACD,QAAQ,EAAE;YACnCqB,QAAQ,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC;UAClC,CAAC,MAAM;YACLD,QAAQ,CAAC;UACX;QACF,CAAC;QACDP,OAAO,EAAE;MACX,EACD;MACDX,QAAQ,EAAE,CACR;QAAES,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,EACrD;MACDV,KAAK,EAAE,CACL;QAAEmB,IAAI,EAAE,OAAO;QAAEV,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO;IAE5D;;IAIA;IACA,MAAMU,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI,CAAC7B,YAAY,CAACyB,KAAK,EAAE;MAEzB,MAAMzB,YAAY,CAACyB,KAAK,CAACK,QAAQ,CAAC,MAAOC,KAAK,IAAK;QACjD,IAAIA,KAAK,EAAE;UACT;UACAlB,OAAO,CAACY,KAAK,GAAG,IAAG;UAEnB,IAAI;YACF,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,EAAE;cAClEC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE;gBACP,cAAc,EAAE;cAClB,CAAC;cACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;gBACnBlC,QAAQ,EAAED,SAAS,CAACC,QAAQ;gBAC5BC,QAAQ,EAAEF,SAAS,CAACE;cACtB,CAAC;YACH,CAAC;YAED,MAAMkC,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC;YAEnC,IAAID,MAAM,CAACE,IAAI,KAAK,KAAK,EAAE;cACzB,MAAM;gBAAEC,IAAI;gBAAEC;cAAM,CAAC,GAAGJ,MAAM,CAACK,IAAG;cAElC7C,cAAc,CAAC;gBACb8C,KAAK,EAAE,MAAM;gBACb3B,OAAO,EAAE,QAAQwB,IAAI,CAACtC,QAAQ,GAAG;gBACjCwB,IAAI,EAAE,SAAS;gBACfkB,QAAQ,EAAE;cACZ,CAAC;;cAED;cACAC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,MAAM;cACzCD,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEN,IAAI,CAACtC,QAAQ;cAC9C2C,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEN,IAAI,CAACO,IAAI;cAC1CF,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEL,KAAK;cACnCI,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAEN,IAAI,CAACQ,EAAE;;cAEtC;cACA,IAAIR,IAAI,CAACO,IAAI,KAAK,OAAO,EAAE;gBACzB/C,MAAM,CAACiD,IAAI,CAAC,QAAQ;cACtB,CAAC,MAAM;gBACLjD,MAAM,CAACiD,IAAI,CAAC,GAAG;cACjB;YACF,CAAC,MAAM;cACLrD,SAAS,CAACsD,KAAK,CAACb,MAAM,CAACc,GAAG,IAAI,MAAM;YACtC;UACF,CAAC,CAAC,OAAOD,KAAK,EAAE;YACdE,OAAO,CAACF,KAAK,CAAC,SAAS,EAAEA,KAAK;YAC9BtD,SAAS,CAACsD,KAAK,CAAC,YAAY;UAC9B,CAAC,SAAS;YACRvC,OAAO,CAACY,KAAK,GAAG,KAAI;UACtB;QACF,CAAC,MAAM;UACL3B,SAAS,CAACsD,KAAK,CAAC,YAAY;UAC5B,OAAO,KAAI;QACb;MACF,CAAC;IACH;;IAEA;IACA,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;MACjCzD,SAAS,CAAC0D,IAAI,CAAC,cAAc;IAC/B;;IAEA;IACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;MAC/B1C,qBAAqB,CAACU,KAAK,GAAG,IAAG;MACjC;MACAnB,YAAY,CAACF,QAAQ,GAAG,EAAC;MACzBE,YAAY,CAACD,QAAQ,GAAG,EAAC;MACzBC,YAAY,CAACC,eAAe,GAAG,EAAC;MAChCD,YAAY,CAACE,QAAQ,GAAG,EAAC;MACzBF,YAAY,CAACG,KAAK,GAAG,EAAC;MACtBH,YAAY,CAACI,KAAK,GAAG,EAAC;MACtBJ,YAAY,CAACK,OAAO,GAAG,EAAC;IAC1B;;IAEA;IACA,MAAM+C,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI,CAACzD,eAAe,CAACwB,KAAK,EAAE;MAE5B,MAAMxB,eAAe,CAACwB,KAAK,CAACK,QAAQ,CAAC,MAAOC,KAAK,IAAK;QACpD,IAAIA,KAAK,EAAE;UACTjB,eAAe,CAACW,KAAK,GAAG,IAAG;UAE3B,IAAI;YACF,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwC,EAAE;cACrEC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE;gBACP,cAAc,EAAE;cAClB,CAAC;cACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;gBACnBlC,QAAQ,EAAEE,YAAY,CAACF,QAAQ;gBAC/BC,QAAQ,EAAEC,YAAY,CAACD,QAAQ;gBAC/BG,QAAQ,EAAEF,YAAY,CAACE,QAAQ;gBAC/BC,KAAK,EAAEH,YAAY,CAACG,KAAK;gBACzBC,KAAK,EAAEJ,YAAY,CAACI,KAAK;gBACzBC,OAAO,EAAEL,YAAY,CAACK,OAAO;gBAC7BsC,IAAI,EAAE,MAAM,CAAC;cACf,CAAC;YACH,CAAC;YAED,MAAMV,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC;YAEnC,IAAID,MAAM,CAACE,IAAI,KAAK,KAAK,EAAE;cACzB1C,cAAc,CAAC;gBACb8C,KAAK,EAAE,MAAM;gBACb3B,OAAO,EAAE,kBAAkB;gBAC3BU,IAAI,EAAE,SAAS;gBACfkB,QAAQ,EAAE;cACZ,CAAC;cAED/B,qBAAqB,CAACU,KAAK,GAAG,KAAI;;cAElC;cACAtB,SAAS,CAACC,QAAQ,GAAGE,YAAY,CAACF,QAAO;YAC3C,CAAC,MAAM;cACLN,SAAS,CAACsD,KAAK,CAACb,MAAM,CAACc,GAAG,IAAI,MAAM;YACtC;UACF,CAAC,CAAC,OAAOD,KAAK,EAAE;YACdE,OAAO,CAACF,KAAK,CAAC,SAAS,EAAEA,KAAK;YAC9BtD,SAAS,CAACsD,KAAK,CAAC,YAAY;UAC9B,CAAC,SAAS;YACRtC,eAAe,CAACW,KAAK,GAAG,KAAI;UAC9B;QACF,CAAC,MAAM;UACL3B,SAAS,CAACsD,KAAK,CAAC,YAAY;UAC5B,OAAO,KAAI;QACb;MACF,CAAC;IACH;;IAEA;IACAxD,SAAS,CAAC,MAAM;MACd;MACA,MAAM+D,aAAa,GAAGZ,YAAY,CAACa,OAAO,CAAC,eAAe;MAC1D,IAAID,aAAa,EAAE;QACjBxD,SAAS,CAACC,QAAQ,GAAGuD,aAAY;QACjC/C,UAAU,CAACa,KAAK,GAAG,IAAG;MACxB;IACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}