{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport { defineComponent, inject, createVNode, Comment } from 'vue';\nimport { CASCADER_PANEL_INJECTION_KEY } from './types.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isArray } from '@vue/shared';\nfunction isVNodeEmpty(vnodes) {\n  return !!(isArray(vnodes) ? vnodes.every(({\n    type\n  }) => type === Comment) : (vnodes == null ? void 0 : vnodes.type) === Comment);\n}\nvar NodeContent = defineComponent({\n  name: \"NodeContent\",\n  props: {\n    node: {\n      type: Object,\n      required: true\n    }\n  },\n  setup(props) {\n    const ns = useNamespace(\"cascader-node\");\n    const {\n      renderLabelFn\n    } = inject(CASCADER_PANEL_INJECTION_KEY);\n    const {\n      node\n    } = props;\n    const {\n      data,\n      label: nodeLabel\n    } = node;\n    const label = () => {\n      const renderLabel = renderLabelFn == null ? void 0 : renderLabelFn({\n        node,\n        data\n      });\n      return isVNodeEmpty(renderLabel) ? nodeLabel : renderLabel != null ? renderLabel : nodeLabel;\n    };\n    return () => createVNode(\"span\", {\n      \"class\": ns.e(\"label\")\n    }, [label()]);\n  }\n});\nexport { NodeContent as default };", "map": {"version": 3, "names": ["isArray", "vnodes", "every", "type", "Comment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defineComponent", "props", "node", "Object", "required", "setup", "ns", "useNamespace", "renderLabelFn", "inject", "CASCADER_PANEL_INJECTION_KEY", "data", "label", "nodeLabel", "renderLabel", "isVNodeEmpty", "createVNode", "e"], "sources": ["../../../../../../packages/components/cascader-panel/src/node-content.tsx"], "sourcesContent": ["import { Comment, defineComponent, inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isArray } from '@element-plus/utils'\nimport { CASCADER_PANEL_INJECTION_KEY } from './types'\n\nimport type { PropType, VNode } from 'vue'\nimport type { CascaderNode } from './types'\n\nfunction isVNodeEmpty(vnodes?: VNode[] | VNode) {\n  return !!(isArray(vnodes)\n    ? vnodes.every(({ type }) => type === Comment)\n    : vnodes?.type === Comment)\n}\n\nexport default defineComponent({\n  name: 'NodeContent',\n  props: {\n    node: {\n      type: Object as PropType<CascaderNode>,\n      required: true,\n    },\n  },\n  setup(props) {\n    const ns = useNamespace('cascader-node')\n    const { renderLabelFn } = inject(CASCADER_PANEL_INJECTION_KEY)!\n    const { node } = props\n    const { data, label: nodeLabel } = node\n\n    const label = () => {\n      const renderLabel = renderLabelFn?.({ node, data })\n      return isVNodeEmpty(renderLabel) ? nodeLabel : renderLabel ?? nodeLabel\n    }\n    return () => <span class={ns.e('label')}>{label()}</span>\n  },\n})\n"], "mappings": ";;;;;;;EAQS,UAAAA,OAAA,CAAAC,MAAa,IAA0BA,MAAA,CAAAC,KAAA;IACvCC;EACa,MAAAA,IAAA,KAAAC,OAAA,KAAAH,MAAA,oBAAAA,MAAA,CAAAE,IAAA,MAAAC,OAAA;;AAErB,IAAAC,WAAA,GAAAC,eAAA;;EAEDC,KAAA;IACEC,IAAM,EADuB;MAE7BL,IAAO,EAAAM,MAAA;MACLC,QAAM;IACJ;EACA;EAFIC,MAAAJ,KAAA;IAHqB,MAAAK,EAAA,GAAAC,YAAA;;MAQxBC;IACH,IAAAC,MAAQ,CAAGC,4BAAY,CAAvB;IACA,MAAM;MAAER;KAAkB,GAAAD,KAAA;IAC1B,MAAM;MAAEU,IAAA;MAAFC,KAAN,EAAAC;KACM,GAAAX,IAAA;UAAAU,KAAA,GAAAA,CAAA;MAAQ,MAAOE,WAAA,GAAAN,aAAA,oBAAAA,aAAA;QAAcN,IAAnC;;OAEM;MACJ,OAAMa,YAAc,CAAAD,WAAA,IAAgBD,SAAA,GAAAC,WAAA,WAAAA,WAAA,GAAAD,SAAA;;IAAQ,aAAAG,WAAA;MAAR,OAApC,EAAAV,EAAA,CAAAW,CAAA;OACO,CAAAL,KAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}