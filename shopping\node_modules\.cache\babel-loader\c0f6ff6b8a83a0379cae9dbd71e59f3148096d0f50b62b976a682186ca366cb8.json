{"ast": null, "code": "import baseDelay from './_baseDelay.js';\nimport baseRest from './_baseRest.js';\nimport toNumber from './toNumber.js';\n\n/**\n * Invokes `func` after `wait` milliseconds. Any additional arguments are\n * provided to `func` when it's invoked.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to delay.\n * @param {number} wait The number of milliseconds to delay invocation.\n * @param {...*} [args] The arguments to invoke `func` with.\n * @returns {number} Returns the timer id.\n * @example\n *\n * _.delay(function(text) {\n *   console.log(text);\n * }, 1000, 'later');\n * // => Logs 'later' after one second.\n */\nvar delay = baseRest(function (func, wait, args) {\n  return baseDelay(func, toNumber(wait) || 0, args);\n});\nexport default delay;", "map": {"version": 3, "names": ["baseDelay", "baseRest", "toNumber", "delay", "func", "wait", "args"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/delay.js"], "sourcesContent": ["import baseDelay from './_baseDelay.js';\nimport baseRest from './_baseRest.js';\nimport toNumber from './toNumber.js';\n\n/**\n * Invokes `func` after `wait` milliseconds. Any additional arguments are\n * provided to `func` when it's invoked.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to delay.\n * @param {number} wait The number of milliseconds to delay invocation.\n * @param {...*} [args] The arguments to invoke `func` with.\n * @returns {number} Returns the timer id.\n * @example\n *\n * _.delay(function(text) {\n *   console.log(text);\n * }, 1000, 'later');\n * // => Logs 'later' after one second.\n */\nvar delay = baseRest(function(func, wait, args) {\n  return baseDelay(func, toNumber(wait) || 0, args);\n});\n\nexport default delay;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,GAAGF,QAAQ,CAAC,UAASG,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC9C,OAAON,SAAS,CAACI,IAAI,EAAEF,QAAQ,CAACG,IAAI,CAAC,IAAI,CAAC,EAAEC,IAAI,CAAC;AACnD,CAAC,CAAC;AAEF,eAAeH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}