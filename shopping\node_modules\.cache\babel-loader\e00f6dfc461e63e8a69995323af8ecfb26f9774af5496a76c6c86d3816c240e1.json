{"ast": null, "code": "import baseRest from './_baseRest.js';\nimport unzipWith from './unzipWith.js';\n\n/**\n * This method is like `_.zip` except that it accepts `iteratee` to specify\n * how grouped values should be combined. The iteratee is invoked with the\n * elements of each group: (...group).\n *\n * @static\n * @memberOf _\n * @since 3.8.0\n * @category Array\n * @param {...Array} [arrays] The arrays to process.\n * @param {Function} [iteratee=_.identity] The function to combine\n *  grouped values.\n * @returns {Array} Returns the new array of grouped elements.\n * @example\n *\n * _.zipWith([1, 2], [10, 20], [100, 200], function(a, b, c) {\n *   return a + b + c;\n * });\n * // => [111, 222]\n */\nvar zipWith = baseRest(function (arrays) {\n  var length = arrays.length,\n    iteratee = length > 1 ? arrays[length - 1] : undefined;\n  iteratee = typeof iteratee == 'function' ? (arrays.pop(), iteratee) : undefined;\n  return unzipWith(arrays, iteratee);\n});\nexport default zipWith;", "map": {"version": 3, "names": ["baseRest", "unzipWith", "zipWith", "arrays", "length", "iteratee", "undefined", "pop"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/zipWith.js"], "sourcesContent": ["import baseRest from './_baseRest.js';\nimport unzipWith from './unzipWith.js';\n\n/**\n * This method is like `_.zip` except that it accepts `iteratee` to specify\n * how grouped values should be combined. The iteratee is invoked with the\n * elements of each group: (...group).\n *\n * @static\n * @memberOf _\n * @since 3.8.0\n * @category Array\n * @param {...Array} [arrays] The arrays to process.\n * @param {Function} [iteratee=_.identity] The function to combine\n *  grouped values.\n * @returns {Array} Returns the new array of grouped elements.\n * @example\n *\n * _.zipWith([1, 2], [10, 20], [100, 200], function(a, b, c) {\n *   return a + b + c;\n * });\n * // => [111, 222]\n */\nvar zipWith = baseRest(function(arrays) {\n  var length = arrays.length,\n      iteratee = length > 1 ? arrays[length - 1] : undefined;\n\n  iteratee = typeof iteratee == 'function' ? (arrays.pop(), iteratee) : undefined;\n  return unzipWith(arrays, iteratee);\n});\n\nexport default zipWith;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAGF,QAAQ,CAAC,UAASG,MAAM,EAAE;EACtC,IAAIC,MAAM,GAAGD,MAAM,CAACC,MAAM;IACtBC,QAAQ,GAAGD,MAAM,GAAG,CAAC,GAAGD,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,GAAGE,SAAS;EAE1DD,QAAQ,GAAG,OAAOA,QAAQ,IAAI,UAAU,IAAIF,MAAM,CAACI,GAAG,CAAC,CAAC,EAAEF,QAAQ,IAAIC,SAAS;EAC/E,OAAOL,SAAS,CAACE,MAAM,EAAEE,QAAQ,CAAC;AACpC,CAAC,CAAC;AAEF,eAAeH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}