{"ast": null, "code": "/**\n * The base implementation of `_.conformsTo` which accepts `props` to check.\n *\n * @private\n * @param {Object} object The object to inspect.\n * @param {Object} source The object of property predicates to conform to.\n * @returns {boolean} Returns `true` if `object` conforms, else `false`.\n */\nfunction baseConformsTo(object, source, props) {\n  var length = props.length;\n  if (object == null) {\n    return !length;\n  }\n  object = Object(object);\n  while (length--) {\n    var key = props[length],\n      predicate = source[key],\n      value = object[key];\n    if (value === undefined && !(key in object) || !predicate(value)) {\n      return false;\n    }\n  }\n  return true;\n}\nexport default baseConformsTo;", "map": {"version": 3, "names": ["baseConformsTo", "object", "source", "props", "length", "Object", "key", "predicate", "value", "undefined"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_baseConformsTo.js"], "sourcesContent": ["/**\n * The base implementation of `_.conformsTo` which accepts `props` to check.\n *\n * @private\n * @param {Object} object The object to inspect.\n * @param {Object} source The object of property predicates to conform to.\n * @returns {boolean} Returns `true` if `object` conforms, else `false`.\n */\nfunction baseConformsTo(object, source, props) {\n  var length = props.length;\n  if (object == null) {\n    return !length;\n  }\n  object = Object(object);\n  while (length--) {\n    var key = props[length],\n        predicate = source[key],\n        value = object[key];\n\n    if ((value === undefined && !(key in object)) || !predicate(value)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default baseConformsTo;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;EAC7C,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;EACzB,IAAIH,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,CAACG,MAAM;EAChB;EACAH,MAAM,GAAGI,MAAM,CAACJ,MAAM,CAAC;EACvB,OAAOG,MAAM,EAAE,EAAE;IACf,IAAIE,GAAG,GAAGH,KAAK,CAACC,MAAM,CAAC;MACnBG,SAAS,GAAGL,MAAM,CAACI,GAAG,CAAC;MACvBE,KAAK,GAAGP,MAAM,CAACK,GAAG,CAAC;IAEvB,IAAKE,KAAK,KAAKC,SAAS,IAAI,EAAEH,GAAG,IAAIL,MAAM,CAAC,IAAK,CAACM,SAAS,CAACC,KAAK,CAAC,EAAE;MAClE,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAEA,eAAeR,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}