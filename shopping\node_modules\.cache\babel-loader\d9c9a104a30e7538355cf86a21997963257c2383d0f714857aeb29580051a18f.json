{"ast": null, "code": "import deburrLetter from './_deburrLetter.js';\nimport toString from './toString.js';\n\n/** Used to match Latin Unicode letters (excluding mathematical operators). */\nvar reLatin = /[\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\xff\\u0100-\\u017f]/g;\n\n/** Used to compose unicode character classes. */\nvar rsComboMarksRange = '\\\\u0300-\\\\u036f',\n  reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n  rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n  rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange;\n\n/** Used to compose unicode capture groups. */\nvar rsCombo = '[' + rsComboRange + ']';\n\n/**\n * Used to match [combining diacritical marks](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks) and\n * [combining diacritical marks for symbols](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks_for_Symbols).\n */\nvar reComboMark = RegExp(rsCombo, 'g');\n\n/**\n * Deburrs `string` by converting\n * [Latin-1 Supplement](https://en.wikipedia.org/wiki/Latin-1_Supplement_(Unicode_block)#Character_table)\n * and [Latin Extended-A](https://en.wikipedia.org/wiki/Latin_Extended-A)\n * letters to basic Latin letters and removing\n * [combining diacritical marks](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to deburr.\n * @returns {string} Returns the deburred string.\n * @example\n *\n * _.deburr('déjà vu');\n * // => 'deja vu'\n */\nfunction deburr(string) {\n  string = toString(string);\n  return string && string.replace(reLatin, deburrLetter).replace(reComboMark, '');\n}\nexport default deburr;", "map": {"version": 3, "names": ["deburrLetter", "toString", "reLatin", "rsComboMarksRange", "reComboHalfMarksRange", "rsComboSymbolsRange", "rsComboRange", "rsCombo", "reComboMark", "RegExp", "deburr", "string", "replace"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/deburr.js"], "sourcesContent": ["import deburrLetter from './_deburrLetter.js';\nimport toString from './toString.js';\n\n/** Used to match Latin Unicode letters (excluding mathematical operators). */\nvar reLatin = /[\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\xff\\u0100-\\u017f]/g;\n\n/** Used to compose unicode character classes. */\nvar rsComboMarksRange = '\\\\u0300-\\\\u036f',\n    reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n    rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n    rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange;\n\n/** Used to compose unicode capture groups. */\nvar rsCombo = '[' + rsComboRange + ']';\n\n/**\n * Used to match [combining diacritical marks](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks) and\n * [combining diacritical marks for symbols](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks_for_Symbols).\n */\nvar reComboMark = RegExp(rsCombo, 'g');\n\n/**\n * Deburrs `string` by converting\n * [Latin-1 Supplement](https://en.wikipedia.org/wiki/Latin-1_Supplement_(Unicode_block)#Character_table)\n * and [Latin Extended-A](https://en.wikipedia.org/wiki/Latin_Extended-A)\n * letters to basic Latin letters and removing\n * [combining diacritical marks](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to deburr.\n * @returns {string} Returns the deburred string.\n * @example\n *\n * _.deburr('déjà vu');\n * // => 'deja vu'\n */\nfunction deburr(string) {\n  string = toString(string);\n  return string && string.replace(reLatin, deburrLetter).replace(reComboMark, '');\n}\n\nexport default deburr;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,OAAO,GAAG,6CAA6C;;AAE3D;AACA,IAAIC,iBAAiB,GAAG,iBAAiB;EACrCC,qBAAqB,GAAG,iBAAiB;EACzCC,mBAAmB,GAAG,iBAAiB;EACvCC,YAAY,GAAGH,iBAAiB,GAAGC,qBAAqB,GAAGC,mBAAmB;;AAElF;AACA,IAAIE,OAAO,GAAG,GAAG,GAAGD,YAAY,GAAG,GAAG;;AAEtC;AACA;AACA;AACA;AACA,IAAIE,WAAW,GAAGC,MAAM,CAACF,OAAO,EAAE,GAAG,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,MAAMA,CAACC,MAAM,EAAE;EACtBA,MAAM,GAAGV,QAAQ,CAACU,MAAM,CAAC;EACzB,OAAOA,MAAM,IAAIA,MAAM,CAACC,OAAO,CAACV,OAAO,EAAEF,YAAY,CAAC,CAACY,OAAO,CAACJ,WAAW,EAAE,EAAE,CAAC;AACjF;AAEA,eAAeE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}