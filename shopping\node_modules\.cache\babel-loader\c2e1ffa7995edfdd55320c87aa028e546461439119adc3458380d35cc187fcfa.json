{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport Goods from '../views/goods.vue';\nimport Category from '../views/category.vue';\nimport Login from '../views/Login.vue';\nconst routes = [{\n  path: '/login',\n  name: 'Login',\n  component: Login\n}, {\n  path: '/',\n  name: 'Goods',\n  component: Goods,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/goods',\n  name: 'GoodsPage',\n  component: Goods,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/category',\n  name: 'Category',\n  component: Category,\n  meta: {\n    requiresAuth: true\n  }\n}];\nconst router = createRouter({\n  history: createWebHistory(),\n  routes\n});\n\n// 路由守卫\nrouter.beforeEach((to, from, next) => {\n  const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';\n  const userRole = localStorage.getItem('userRole');\n\n  // 如果访问登录页面且已登录，重定向到首页\n  if (to.name === 'Login' && isLoggedIn) {\n    next({\n      name: 'Goods'\n    });\n    return;\n  }\n\n  // 如果需要登录但未登录，重定向到登录页\n  if (to.meta.requiresAuth && !isLoggedIn) {\n    next({\n      name: 'Login'\n    });\n    return;\n  }\n\n  // 如果需要管理员权限但不是管理员，重定向到首页\n  if (to.meta.requiresAdmin && userRole !== 'admin') {\n    next({\n      name: 'Goods'\n    });\n    return;\n  }\n  next();\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "Goods", "Category", "<PERSON><PERSON>", "routes", "path", "name", "component", "meta", "requiresAuth", "router", "history", "beforeEach", "to", "from", "next", "isLoggedIn", "localStorage", "getItem", "userRole", "requiresAdmin"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\r\nimport Goods from '../views/goods.vue'\r\nimport Category from '../views/category.vue'\r\nimport Login from '../views/Login.vue'\r\n\r\nconst routes = [\r\n  {\r\n    path: '/login',\r\n    name: 'Login',\r\n    component: Login\r\n  },\r\n  {\r\n    path: '/',\r\n    name: 'Goods',\r\n    component: Goods,\r\n    meta: { requiresAuth: true }\r\n  },\r\n  {\r\n    path: '/goods',\r\n    name: 'GoodsPage',\r\n    component: Goods,\r\n    meta: { requiresAuth: true }\r\n  },\r\n  {\r\n    path: '/category',\r\n    name: 'Category',\r\n    component: Category,\r\n    meta: { requiresAuth: true }\r\n  },\r\n\r\n]\r\n\r\nconst router = createRouter({\r\n  history: createWebHistory(),\r\n  routes\r\n})\r\n\r\n// 路由守卫\r\nrouter.beforeEach((to, from, next) => {\r\n  const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true'\r\n  const userRole = localStorage.getItem('userRole')\r\n\r\n  // 如果访问登录页面且已登录，重定向到首页\r\n  if (to.name === 'Login' && isLoggedIn) {\r\n    next({ name: 'Goods' })\r\n    return\r\n  }\r\n\r\n  // 如果需要登录但未登录，重定向到登录页\r\n  if (to.meta.requiresAuth && !isLoggedIn) {\r\n    next({ name: 'Login' })\r\n    return\r\n  }\r\n\r\n  // 如果需要管理员权限但不是管理员，重定向到首页\r\n  if (to.meta.requiresAdmin && userRole !== 'admin') {\r\n    next({ name: 'Goods' })\r\n    return\r\n  }\r\n\r\n  next()\r\n})\r\n\r\nexport default router"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,KAAK,MAAM,oBAAoB;AAEtC,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEJ;AACb,CAAC,EACD;EACEE,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEN,KAAK;EAChBO,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEN,KAAK;EAChBO,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEL,QAAQ;EACnBM,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,CAEF;AAED,MAAMC,MAAM,GAAGX,YAAY,CAAC;EAC1BY,OAAO,EAAEX,gBAAgB,CAAC,CAAC;EAC3BI;AACF,CAAC,CAAC;;AAEF;AACAM,MAAM,CAACE,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,KAAK,MAAM;EAChE,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;;EAEjD;EACA,IAAIL,EAAE,CAACP,IAAI,KAAK,OAAO,IAAIU,UAAU,EAAE;IACrCD,IAAI,CAAC;MAAET,IAAI,EAAE;IAAQ,CAAC,CAAC;IACvB;EACF;;EAEA;EACA,IAAIO,EAAE,CAACL,IAAI,CAACC,YAAY,IAAI,CAACO,UAAU,EAAE;IACvCD,IAAI,CAAC;MAAET,IAAI,EAAE;IAAQ,CAAC,CAAC;IACvB;EACF;;EAEA;EACA,IAAIO,EAAE,CAACL,IAAI,CAACY,aAAa,IAAID,QAAQ,KAAK,OAAO,EAAE;IACjDJ,IAAI,CAAC;MAAET,IAAI,EAAE;IAAQ,CAAC,CAAC;IACvB;EACF;EAEAS,IAAI,CAAC,CAAC;AACR,CAAC,CAAC;AAEF,eAAeL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}