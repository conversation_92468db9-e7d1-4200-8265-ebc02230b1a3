{"ast": null, "code": "import { computed } from 'vue';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nconst useMonthRangeHeader = ({\n  unlinkPanels,\n  leftDate,\n  rightDate\n}) => {\n  const {\n    t\n  } = useLocale();\n  const leftPrevYear = () => {\n    leftDate.value = leftDate.value.subtract(1, \"year\");\n    if (!unlinkPanels.value) {\n      rightDate.value = rightDate.value.subtract(1, \"year\");\n    }\n  };\n  const rightNextYear = () => {\n    if (!unlinkPanels.value) {\n      leftDate.value = leftDate.value.add(1, \"year\");\n    }\n    rightDate.value = rightDate.value.add(1, \"year\");\n  };\n  const leftNextYear = () => {\n    leftDate.value = leftDate.value.add(1, \"year\");\n  };\n  const rightPrevYear = () => {\n    rightDate.value = rightDate.value.subtract(1, \"year\");\n  };\n  const leftLabel = computed(() => {\n    return `${leftDate.value.year()} ${t(\"el.datepicker.year\")}`;\n  });\n  const rightLabel = computed(() => {\n    return `${rightDate.value.year()} ${t(\"el.datepicker.year\")}`;\n  });\n  const leftYear = computed(() => {\n    return leftDate.value.year();\n  });\n  const rightYear = computed(() => {\n    return rightDate.value.year() === leftDate.value.year() ? leftDate.value.year() + 1 : rightDate.value.year();\n  });\n  return {\n    leftPrevYear,\n    rightNextYear,\n    leftNextYear,\n    rightPrevYear,\n    leftLabel,\n    rightLabel,\n    leftYear,\n    rightYear\n  };\n};\nexport { useMonthRangeHeader };", "map": {"version": 3, "names": ["useMonthRangeHeader", "unlinkPanels", "leftDate", "rightDate", "t", "useLocale", "leftPrevYear", "value", "subtract", "rightNextYear", "add", "leftNextYear", "rightPrevYear", "leftLabel", "computed", "year", "<PERSON><PERSON><PERSON><PERSON>", "leftYear", "rightYear"], "sources": ["../../../../../../../packages/components/date-picker-panel/src/composables/use-month-range-header.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { useLocale } from '@element-plus/hooks'\n\nimport type { Ref, ToRef } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const useMonthRangeHeader = ({\n  unlinkPanels,\n  leftDate,\n  rightDate,\n}: {\n  unlinkPanels: ToRef<boolean>\n  leftDate: Ref<Dayjs>\n  rightDate: Ref<Dayjs>\n}) => {\n  const { t } = useLocale()\n  const leftPrevYear = () => {\n    leftDate.value = leftDate.value.subtract(1, 'year')\n    if (!unlinkPanels.value) {\n      rightDate.value = rightDate.value.subtract(1, 'year')\n    }\n  }\n\n  const rightNextYear = () => {\n    if (!unlinkPanels.value) {\n      leftDate.value = leftDate.value.add(1, 'year')\n    }\n    rightDate.value = rightDate.value.add(1, 'year')\n  }\n\n  const leftNextYear = () => {\n    leftDate.value = leftDate.value.add(1, 'year')\n  }\n\n  const rightPrevYear = () => {\n    rightDate.value = rightDate.value.subtract(1, 'year')\n  }\n  const leftLabel = computed(() => {\n    return `${leftDate.value.year()} ${t('el.datepicker.year')}`\n  })\n\n  const rightLabel = computed(() => {\n    return `${rightDate.value.year()} ${t('el.datepicker.year')}`\n  })\n\n  const leftYear = computed(() => {\n    return leftDate.value.year()\n  })\n\n  const rightYear = computed(() => {\n    return rightDate.value.year() === leftDate.value.year()\n      ? leftDate.value.year() + 1\n      : rightDate.value.year()\n  })\n\n  return {\n    leftPrevYear,\n    rightNextYear,\n    leftNextYear,\n    rightPrevYear,\n    leftLabel,\n    rightLabel,\n    leftYear,\n    rightYear,\n  }\n}\n"], "mappings": ";;AAEY,MAACA,mBAAmB,GAAGA,CAAC;EAClCC,YAAY;EACZC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,MAAM;IAAEC;EAAC,CAAE,GAAGC,SAAS,EAAE;EACzB,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBJ,QAAQ,CAACK,KAAK,GAAGL,QAAQ,CAACK,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;IACnD,IAAI,CAACP,YAAY,CAACM,KAAK,EAAE;MACvBJ,SAAS,CAACI,KAAK,GAAGJ,SAAS,CAACI,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;IAC3D;EACA,CAAG;EACD,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACR,YAAY,CAACM,KAAK,EAAE;MACvBL,QAAQ,CAACK,KAAK,GAAGL,QAAQ,CAACK,KAAK,CAACG,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;IACpD;IACIP,SAAS,CAACI,KAAK,GAAGJ,SAAS,CAACI,KAAK,CAACG,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;EACpD,CAAG;EACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBT,QAAQ,CAACK,KAAK,GAAGL,QAAQ,CAACK,KAAK,CAACG,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;EAClD,CAAG;EACD,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1BT,SAAS,CAACI,KAAK,GAAGJ,SAAS,CAACI,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;EACzD,CAAG;EACD,MAAMK,SAAS,GAAGC,QAAQ,CAAC,MAAM;IAC/B,OAAO,GAAGZ,QAAQ,CAACK,KAAK,CAACQ,IAAI,EAAE,IAAIX,CAAC,CAAC,oBAAoB,CAAC,EAAE;EAChE,CAAG,CAAC;EACF,MAAMY,UAAU,GAAGF,QAAQ,CAAC,MAAM;IAChC,OAAO,GAAGX,SAAS,CAACI,KAAK,CAACQ,IAAI,EAAE,IAAIX,CAAC,CAAC,oBAAoB,CAAC,EAAE;EACjE,CAAG,CAAC;EACF,MAAMa,QAAQ,GAAGH,QAAQ,CAAC,MAAM;IAC9B,OAAOZ,QAAQ,CAACK,KAAK,CAACQ,IAAI,EAAE;EAChC,CAAG,CAAC;EACF,MAAMG,SAAS,GAAGJ,QAAQ,CAAC,MAAM;IAC/B,OAAOX,SAAS,CAACI,KAAK,CAACQ,IAAI,EAAE,KAAKb,QAAQ,CAACK,KAAK,CAACQ,IAAI,EAAE,GAAGb,QAAQ,CAACK,KAAK,CAACQ,IAAI,EAAE,GAAG,CAAC,GAAGZ,SAAS,CAACI,KAAK,CAACQ,IAAI,EAAE;EAChH,CAAG,CAAC;EACF,OAAO;IACLT,YAAY;IACZG,aAAa;IACbE,YAAY;IACZC,aAAa;IACbC,SAAS;IACTG,UAAU;IACVC,QAAQ;IACRC;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}