{"ast": null, "code": "import arrayPush from './_arrayPush.js';\nimport isFlattenable from './_isFlattenable.js';\n\n/**\n * The base implementation of `_.flatten` with support for restricting flattening.\n *\n * @private\n * @param {Array} array The array to flatten.\n * @param {number} depth The maximum recursion depth.\n * @param {boolean} [predicate=isFlattenable] The function invoked per iteration.\n * @param {boolean} [isStrict] Restrict to values that pass `predicate` checks.\n * @param {Array} [result=[]] The initial result value.\n * @returns {Array} Returns the new flattened array.\n */\nfunction baseFlatten(array, depth, predicate, isStrict, result) {\n  var index = -1,\n    length = array.length;\n  predicate || (predicate = isFlattenable);\n  result || (result = []);\n  while (++index < length) {\n    var value = array[index];\n    if (depth > 0 && predicate(value)) {\n      if (depth > 1) {\n        // Recursively flatten arrays (susceptible to call stack limits).\n        baseFlatten(value, depth - 1, predicate, isStrict, result);\n      } else {\n        arrayPush(result, value);\n      }\n    } else if (!isStrict) {\n      result[result.length] = value;\n    }\n  }\n  return result;\n}\nexport default baseFlatten;", "map": {"version": 3, "names": ["arrayPush", "isFlattenable", "baseFlatten", "array", "depth", "predicate", "isStrict", "result", "index", "length", "value"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_baseFlatten.js"], "sourcesContent": ["import arrayPush from './_arrayPush.js';\nimport isFlattenable from './_isFlattenable.js';\n\n/**\n * The base implementation of `_.flatten` with support for restricting flattening.\n *\n * @private\n * @param {Array} array The array to flatten.\n * @param {number} depth The maximum recursion depth.\n * @param {boolean} [predicate=isFlattenable] The function invoked per iteration.\n * @param {boolean} [isStrict] Restrict to values that pass `predicate` checks.\n * @param {Array} [result=[]] The initial result value.\n * @returns {Array} Returns the new flattened array.\n */\nfunction baseFlatten(array, depth, predicate, isStrict, result) {\n  var index = -1,\n      length = array.length;\n\n  predicate || (predicate = isFlattenable);\n  result || (result = []);\n\n  while (++index < length) {\n    var value = array[index];\n    if (depth > 0 && predicate(value)) {\n      if (depth > 1) {\n        // Recursively flatten arrays (susceptible to call stack limits).\n        baseFlatten(value, depth - 1, predicate, isStrict, result);\n      } else {\n        arrayPush(result, value);\n      }\n    } else if (!isStrict) {\n      result[result.length] = value;\n    }\n  }\n  return result;\n}\n\nexport default baseFlatten;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,aAAa,MAAM,qBAAqB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EAC9D,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGN,KAAK,CAACM,MAAM;EAEzBJ,SAAS,KAAKA,SAAS,GAAGJ,aAAa,CAAC;EACxCM,MAAM,KAAKA,MAAM,GAAG,EAAE,CAAC;EAEvB,OAAO,EAAEC,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIC,KAAK,GAAGP,KAAK,CAACK,KAAK,CAAC;IACxB,IAAIJ,KAAK,GAAG,CAAC,IAAIC,SAAS,CAACK,KAAK,CAAC,EAAE;MACjC,IAAIN,KAAK,GAAG,CAAC,EAAE;QACb;QACAF,WAAW,CAACQ,KAAK,EAAEN,KAAK,GAAG,CAAC,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,CAAC;MAC5D,CAAC,MAAM;QACLP,SAAS,CAACO,MAAM,EAAEG,KAAK,CAAC;MAC1B;IACF,CAAC,MAAM,IAAI,CAACJ,QAAQ,EAAE;MACpBC,MAAM,CAACA,MAAM,CAACE,MAAM,CAAC,GAAGC,KAAK;IAC/B;EACF;EACA,OAAOH,MAAM;AACf;AAEA,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}