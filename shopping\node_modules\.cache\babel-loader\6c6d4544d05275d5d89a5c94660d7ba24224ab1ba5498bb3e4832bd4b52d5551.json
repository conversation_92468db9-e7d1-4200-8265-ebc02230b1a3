{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createVNode as _createVNode, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"goods-container\"\n};\nconst _hoisted_2 = {\n  class: \"header\"\n};\nconst _hoisted_3 = {\n  class: \"user-info\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"loading\"\n};\nconst _hoisted_5 = {\n  class: \"goods-list\"\n};\nconst _hoisted_6 = {\n  class: \"goods-card-header\"\n};\nconst _hoisted_7 = [\"onClick\"];\nconst _hoisted_8 = {\n  class: \"goods-card-description\"\n};\nconst _hoisted_9 = {\n  class: \"goods-card-price\"\n};\nconst _hoisted_10 = {\n  class: \"goods-card-actions\"\n};\nconst _hoisted_11 = [\"onClick\"];\nconst _hoisted_12 = [\"onClick\"];\nconst _hoisted_13 = [\"onClick\"];\nconst _hoisted_14 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_15 = {\n  key: 0,\n  class: \"good-detail\"\n};\nconst _hoisted_16 = {\n  class: \"detail-item\"\n};\nconst _hoisted_17 = {\n  class: \"detail-item\"\n};\nconst _hoisted_18 = {\n  class: \"detail-item\"\n};\nconst _hoisted_19 = {\n  class: \"detail-item\"\n};\nconst _hoisted_20 = {\n  class: \"detail-item\"\n};\nconst _hoisted_21 = {\n  class: \"detail-item\"\n};\nconst _hoisted_22 = {\n  class: \"detail-item\"\n};\nconst _hoisted_23 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_input_number = _resolveComponent(\"el-input-number\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_switch = _resolveComponent(\"el-switch\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[14] || (_cache[14] = _createElementVNode(\"h2\", {\n    class: \"page-title\"\n  }, \"在线购物商城\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"span\", null, \"欢迎，\" + _toDisplayString($data.username), 1 /* TEXT */), $data.userRole === 'admin' ? (_openBlock(), _createBlock(_component_el_button, {\n    key: 0,\n    type: \"success\",\n    onClick: $options.showAddGoodDialog\n  }, {\n    default: _withCtx(() => [...(_cache[11] || (_cache[11] = [_createTextVNode(\"添加商品\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), $data.userRole === 'admin' ? (_openBlock(), _createBlock(_component_el_button, {\n    key: 1,\n    type: \"primary\",\n    onClick: $options.goToAdmin\n  }, {\n    default: _withCtx(() => [...(_cache[12] || (_cache[12] = [_createTextVNode(\"管理后台\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n    type: \"danger\",\n    onClick: $options.logout\n  }, {\n    default: _withCtx(() => [...(_cache[13] || (_cache[13] = [_createTextVNode(\"退出登录\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createCommentVNode(\" 加载状态 \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, \"加载中...\")) : $data.goodsList.length > 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 商品列表（卡片样式） \"), _createElementVNode(\"div\", _hoisted_5, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.goodsList, good => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"goods-card\",\n      key: good.id\n    }, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString(good.categoryName || '未分类'), 1 /* TEXT */), _createElementVNode(\"div\", {\n      class: \"goods-card-title\",\n      onClick: $event => $options.showGoodDetail(good)\n    }, _toDisplayString(good.name), 9 /* TEXT, PROPS */, _hoisted_7), _createElementVNode(\"div\", _hoisted_8, _toDisplayString(good.description || '暂无描述'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_9, \"¥\" + _toDisplayString($options.formatPrice(good.price)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"button\", {\n      class: \"btn-add-cart\",\n      onClick: $event => $options.addToCart(good)\n    }, \"加入购物车\", 8 /* PROPS */, _hoisted_11), $data.userRole === 'admin' ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 0,\n      class: \"btn-edit\",\n      onClick: $event => $options.editGood(good)\n    }, \"编辑\", 8 /* PROPS */, _hoisted_12)) : _createCommentVNode(\"v-if\", true), $data.userRole === 'admin' ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 1,\n      class: \"btn-delete\",\n      onClick: $event => $options.deleteGood(good)\n    }, \"删除\", 8 /* PROPS */, _hoisted_13)) : _createCommentVNode(\"v-if\", true)])]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" 无数据 \"), _cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n    class: \"no-data\"\n  }, \" 暂无商品 \", -1 /* CACHED */))], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 添加/编辑商品对话框 \"), _createVNode(_component_el_dialog, {\n    title: $data.goodDialogTitle,\n    modelValue: $data.goodDialogVisible,\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.goodDialogVisible = $event),\n    width: \"600px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_14, [_createVNode(_component_el_button, {\n      onClick: _cache[6] || (_cache[6] = $event => $data.goodDialogVisible = false)\n    }, {\n      default: _withCtx(() => [...(_cache[16] || (_cache[16] = [_createTextVNode(\"取消\", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $options.saveGood,\n      loading: $data.goodSaveLoading\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($data.isEditGood ? '更新' : '添加'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      ref: \"goodFormRef\",\n      model: $data.goodForm,\n      rules: $options.goodRules,\n      \"label-width\": \"100px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"商品名称\",\n        prop: \"name\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $data.goodForm.name,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.goodForm.name = $event),\n          placeholder: \"请输入商品名称\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"商品描述\",\n        prop: \"description\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $data.goodForm.description,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.goodForm.description = $event),\n          type: \"textarea\",\n          rows: \"3\",\n          placeholder: \"请输入商品描述\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"价格\",\n        prop: \"saleMoney\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input_number, {\n          modelValue: $data.goodForm.saleMoney,\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.goodForm.saleMoney = $event),\n          min: 0,\n          precision: 2,\n          placeholder: \"请输入价格\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"折扣\",\n        prop: \"discount\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input_number, {\n          modelValue: $data.goodForm.discount,\n          \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.goodForm.discount = $event),\n          min: 0,\n          max: 1,\n          step: 0.1,\n          precision: 2,\n          placeholder: \"请输入折扣(0-1)\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"商品分类\",\n        prop: \"categoryId\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $data.goodForm.categoryId,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.goodForm.categoryId = $event),\n          placeholder: \"请选择分类\",\n          style: {\n            \"width\": \"100%\"\n          }\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.categories, category => {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: category.id,\n              label: category.name,\n              value: category.id\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"是否推荐\",\n        prop: \"recommend\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_switch, {\n          modelValue: $data.goodForm.recommend,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.goodForm.recommend = $event)\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"title\", \"modelValue\"]), _createCommentVNode(\" 商品详情对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"商品详情\",\n    modelValue: $data.detailDialogVisible,\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.detailDialogVisible = $event),\n    width: \"500px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_23, [_createVNode(_component_el_button, {\n      onClick: _cache[8] || (_cache[8] = $event => $data.detailDialogVisible = false)\n    }, {\n      default: _withCtx(() => [...(_cache[24] || (_cache[24] = [_createTextVNode(\"关闭\", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[9] || (_cache[9] = $event => $options.addToCart($data.selectedGood))\n    }, {\n      default: _withCtx(() => [...(_cache[25] || (_cache[25] = [_createTextVNode(\"加入购物车\", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    })])]),\n    default: _withCtx(() => [$data.selectedGood ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_cache[17] || (_cache[17] = _createElementVNode(\"label\", null, \"商品名称：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($data.selectedGood.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_17, [_cache[18] || (_cache[18] = _createElementVNode(\"label\", null, \"商品描述：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($data.selectedGood.description || '暂无描述'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_18, [_cache[19] || (_cache[19] = _createElementVNode(\"label\", null, \"价格：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, \"¥\" + _toDisplayString($options.formatPrice($data.selectedGood.price)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_19, [_cache[20] || (_cache[20] = _createElementVNode(\"label\", null, \"折扣：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($data.selectedGood.discount ? ($data.selectedGood.discount * 100).toFixed(0) + '%' : '无折扣'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_20, [_cache[21] || (_cache[21] = _createElementVNode(\"label\", null, \"分类：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($data.selectedGood.categoryName || '未分类'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_21, [_cache[22] || (_cache[22] = _createElementVNode(\"label\", null, \"销量：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($data.selectedGood.sales || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_22, [_cache[23] || (_cache[23] = _createElementVNode(\"label\", null, \"是否推荐：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($data.selectedGood.recommend ? '是' : '否'), 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "$data", "username", "userRole", "_createBlock", "_component_el_button", "type", "onClick", "$options", "showAddGoodDialog", "_cache", "goToAdmin", "_createVNode", "logout", "_createCommentVNode", "loading", "_hoisted_4", "goodsList", "length", "_Fragment", "key", "_hoisted_5", "_renderList", "good", "id", "_hoisted_6", "categoryName", "$event", "showGoodDetail", "name", "_hoisted_7", "_hoisted_8", "description", "_hoisted_9", "formatPrice", "price", "_hoisted_10", "addToCart", "_hoisted_11", "editGood", "_hoisted_12", "deleteGood", "_hoisted_13", "_component_el_dialog", "title", "goodDialogTitle", "goodDialogVisible", "width", "footer", "_withCtx", "_hoisted_14", "saveGood", "goodSaveLoading", "isEditGood", "_component_el_form", "ref", "model", "goodForm", "rules", "goodRules", "_component_el_form_item", "label", "prop", "_component_el_input", "placeholder", "rows", "_component_el_input_number", "saleMoney", "min", "precision", "discount", "max", "step", "_component_el_select", "categoryId", "style", "categories", "category", "_component_el_option", "value", "_component_el_switch", "recommend", "detailDialogVisible", "_hoisted_23", "<PERSON><PERSON><PERSON>", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "toFixed", "_hoisted_20", "_hoisted_21", "sales", "_hoisted_22"], "sources": ["D:\\2025_down\\project\\shoppingOnline-20250826-sfl\\shoppingOnline-20250826\\shopping\\src\\views\\goods.vue"], "sourcesContent": ["<template>\r\n  <div class=\"goods-container\">\r\n    <div class=\"header\">\r\n      <h2 class=\"page-title\">在线购物商城</h2>\r\n      <div class=\"user-info\">\r\n        <span>欢迎，{{ username }}</span>\r\n        <el-button v-if=\"userRole === 'admin'\" type=\"success\" @click=\"showAddGoodDialog\">添加商品</el-button>\r\n        <el-button v-if=\"userRole === 'admin'\" type=\"primary\" @click=\"goToAdmin\">管理后台</el-button>\r\n        <el-button type=\"danger\" @click=\"logout\">退出登录</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n\r\n    <!-- 商品列表（卡片样式） -->\r\n    <div v-else-if=\"goodsList.length > 0\" class=\"goods-list\">\r\n      <div class=\"goods-card\" v-for=\"good in goodsList\" :key=\"good.id\">\r\n        <div class=\"goods-card-header\">{{ good.categoryName || '未分类' }}</div>\r\n        <div class=\"goods-card-title\" @click=\"showGoodDetail(good)\">{{ good.name }}</div>\r\n        <div class=\"goods-card-description\">{{ good.description || '暂无描述' }}</div>\r\n        <div class=\"goods-card-price\">¥{{ formatPrice(good.price) }}</div>\r\n        <div class=\"goods-card-actions\">\r\n          <button class=\"btn-add-cart\" @click=\"addToCart(good)\">加入购物车</button>\r\n          <button v-if=\"userRole === 'admin'\" class=\"btn-edit\" @click=\"editGood(good)\">编辑</button>\r\n          <button v-if=\"userRole === 'admin'\" class=\"btn-delete\" @click=\"deleteGood(good)\">删除</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 无数据 -->\r\n    <div v-else class=\"no-data\">\r\n      暂无商品\r\n    </div>\r\n\r\n    <!-- 添加/编辑商品对话框 -->\r\n    <el-dialog\r\n      :title=\"goodDialogTitle\"\r\n      v-model=\"goodDialogVisible\"\r\n      width=\"600px\"\r\n    >\r\n      <el-form\r\n        ref=\"goodFormRef\"\r\n        :model=\"goodForm\"\r\n        :rules=\"goodRules\"\r\n        label-width=\"100px\"\r\n      >\r\n        <el-form-item label=\"商品名称\" prop=\"name\">\r\n          <el-input v-model=\"goodForm.name\" placeholder=\"请输入商品名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"商品描述\" prop=\"description\">\r\n          <el-input v-model=\"goodForm.description\" type=\"textarea\" rows=\"3\" placeholder=\"请输入商品描述\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"价格\" prop=\"saleMoney\">\r\n          <el-input-number v-model=\"goodForm.saleMoney\" :min=\"0\" :precision=\"2\" placeholder=\"请输入价格\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"折扣\" prop=\"discount\">\r\n          <el-input-number v-model=\"goodForm.discount\" :min=\"0\" :max=\"1\" :step=\"0.1\" :precision=\"2\" placeholder=\"请输入折扣(0-1)\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"商品分类\" prop=\"categoryId\">\r\n          <el-select v-model=\"goodForm.categoryId\" placeholder=\"请选择分类\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"category in categories\"\r\n              :key=\"category.id\"\r\n              :label=\"category.name\"\r\n              :value=\"category.id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否推荐\" prop=\"recommend\">\r\n          <el-switch v-model=\"goodForm.recommend\" />\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"goodDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"saveGood\" :loading=\"goodSaveLoading\">\r\n            {{ isEditGood ? '更新' : '添加' }}\r\n          </el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- 商品详情对话框 -->\r\n    <el-dialog\r\n      title=\"商品详情\"\r\n      v-model=\"detailDialogVisible\"\r\n      width=\"500px\"\r\n    >\r\n      <div v-if=\"selectedGood\" class=\"good-detail\">\r\n        <div class=\"detail-item\">\r\n          <label>商品名称：</label>\r\n          <span>{{ selectedGood.name }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>商品描述：</label>\r\n          <span>{{ selectedGood.description || '暂无描述' }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>价格：</label>\r\n          <span>¥{{ formatPrice(selectedGood.price) }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>折扣：</label>\r\n          <span>{{ selectedGood.discount ? (selectedGood.discount * 100).toFixed(0) + '%' : '无折扣' }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>分类：</label>\r\n          <span>{{ selectedGood.categoryName || '未分类' }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>销量：</label>\r\n          <span>{{ selectedGood.sales || 0 }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>是否推荐：</label>\r\n          <span>{{ selectedGood.recommend ? '是' : '否' }}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"detailDialogVisible = false\">关闭</el-button>\r\n          <el-button type=\"primary\" @click=\"addToCart(selectedGood)\">加入购物车</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import axios from 'axios'\r\n  import { ElMessage } from 'element-plus'\r\n\r\n  export default {\r\n    name: 'GoodsView',\r\n\r\n    data() {\r\n      return {\r\n        name: '',   // 初始为空\r\n        awesome: false,\r\n        loading: true,\r\n        goodsList: [],\r\n        categoryMap: {},\r\n        categories: [], // 商品分类列表\r\n        username: localStorage.getItem('username') || '',\r\n        userRole: localStorage.getItem('userRole') || '',\r\n\r\n        // 商品管理相关\r\n        goodDialogVisible: false,\r\n        goodDialogTitle: '添加商品',\r\n        isEditGood: false,\r\n        goodSaveLoading: false,\r\n        goodForm: {\r\n          id: null,\r\n          name: '',\r\n          description: '',\r\n          saleMoney: 0,\r\n          discount: 0,\r\n          categoryId: null,\r\n          recommend: false\r\n        },\r\n\r\n        // 商品详情相关\r\n        detailDialogVisible: false,\r\n        selectedGood: null\r\n      };\r\n    },\r\n\r\n    computed: {\r\n      // 示例：计算商品总数\r\n      totalGoods() {\r\n        return this.goodsList.length;\r\n      },\r\n\r\n      // 商品表单验证规则\r\n      goodRules() {\r\n        return {\r\n          name: [\r\n            { required: true, message: '请输入商品名称', trigger: 'blur' },\r\n            { min: 2, max: 50, message: '商品名称长度为2-50个字符', trigger: 'blur' }\r\n          ],\r\n          saleMoney: [\r\n            { required: true, message: '请输入商品价格', trigger: 'blur' },\r\n            { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }\r\n          ],\r\n          categoryId: [\r\n            { required: true, message: '请选择商品分类', trigger: 'change' }\r\n          ]\r\n        }\r\n      }\r\n    },\r\n\r\n    created() {\r\n      const BASE = 'http://localhost:9192'\r\n      this.loading = true\r\n      Promise.all([\r\n        axios.get(BASE + '/goodapi/list'),\r\n        axios.get(BASE + '/categoryapi/list')\r\n      ])\r\n              .then(([goodsResp, categoryResp]) => {\r\n                const goodsData = (goodsResp.data && goodsResp.data.data) || []\r\n                const categories = (categoryResp.data && categoryResp.data.data) || []\r\n                const map = {}\r\n                categories.forEach(c => { map[c.id] = c.name })\r\n                this.categoryMap = map\r\n                this.categories = categories\r\n                this.goodsList = goodsData.map(g => ({\r\n                  id: g.id,\r\n                  name: g.name,\r\n                  description: g.description,\r\n                  // 后端 imgs 形如 /file/xxx.jpg，需要拼接服务器前缀\r\n                  image: g.imgs ? (BASE + g.imgs) : '',\r\n                  // 暂用 saleMoney 做展示价格（后端无单价字段时）\r\n                  price: g.saleMoney,\r\n                  categoryId: g.categoryId,\r\n                  categoryName: map[g.categoryId]\r\n                }))\r\n              })\r\n              .catch(err => {\r\n                console.error('加载商品/分类失败', err)\r\n              })\r\n              .finally(() => {\r\n                this.loading = false\r\n              })\r\n    },\r\n\r\n    methods: {\r\n      formatPrice(v){\r\n        if(v === null || v === undefined) return '-'\r\n        const n = Number(v)\r\n        return Number.isNaN(n) ? v : n.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 2 })\r\n      },\r\n      addToCart(good) {\r\n        // TODO: 实际项目中调用 Vuex 或 API\r\n        console.log('加入购物车:', good.name);\r\n        this.$emit('add-to-cart', good); // 可用于父组件监听\r\n        alert(`已加入购物车：${good.name}`);\r\n      },\r\n      clear() {\r\n        this.name = '';\r\n      },\r\n      logout() {\r\n        localStorage.clear()\r\n        ElMessage.success('已退出登录')\r\n        this.$router.push('/login')\r\n      },\r\n      goToAdmin() {\r\n        this.$router.push('/admin')\r\n      },\r\n\r\n      // 显示商品详情\r\n      showGoodDetail(good) {\r\n        this.selectedGood = good\r\n        this.detailDialogVisible = true\r\n      },\r\n\r\n      // 显示添加商品对话框\r\n      showAddGoodDialog() {\r\n        this.goodDialogTitle = '添加商品'\r\n        this.isEditGood = false\r\n        this.goodForm = {\r\n          id: null,\r\n          name: '',\r\n          description: '',\r\n          saleMoney: 0,\r\n          discount: 0,\r\n          categoryId: null,\r\n          recommend: false\r\n        }\r\n        this.goodDialogVisible = true\r\n      },\r\n\r\n      // 编辑商品\r\n      editGood(good) {\r\n        this.goodDialogTitle = '编辑商品'\r\n        this.isEditGood = true\r\n        this.goodForm = {\r\n          id: good.id,\r\n          name: good.name,\r\n          description: good.description,\r\n          saleMoney: good.saleMoney,\r\n          discount: good.discount,\r\n          categoryId: good.categoryId,\r\n          recommend: good.recommend\r\n        }\r\n        this.goodDialogVisible = true\r\n      },\r\n\r\n      // 保存商品\r\n      async saveGood() {\r\n        if (!this.$refs.goodFormRef) return\r\n\r\n        this.$refs.goodFormRef.validate(async (valid) => {\r\n          if (valid) {\r\n            this.goodSaveLoading = true\r\n\r\n            try {\r\n              const url = this.isEditGood\r\n                ? 'http://localhost:9192/goodapi/update'\r\n                : 'http://localhost:9192/goodapi/add'\r\n\r\n              const method = this.isEditGood ? 'PUT' : 'POST'\r\n\r\n              const response = await fetch(url, {\r\n                method,\r\n                headers: {\r\n                  'Content-Type': 'application/json',\r\n                  'token': localStorage.getItem('token')\r\n                },\r\n                body: JSON.stringify(this.goodForm)\r\n              })\r\n\r\n              const result = await response.json()\r\n\r\n              if (result.code === '200') {\r\n                this.$message.success(this.isEditGood ? '商品更新成功' : '商品添加成功')\r\n                this.goodDialogVisible = false\r\n                this.refreshGoods()\r\n              } else {\r\n                this.$message.error(result.msg || '操作失败')\r\n              }\r\n            } catch (error) {\r\n              console.error('保存商品失败:', error)\r\n              this.$message.error('网络错误，请稍后重试')\r\n            } finally {\r\n              this.goodSaveLoading = false\r\n            }\r\n          }\r\n        })\r\n      },\r\n\r\n      // 删除商品\r\n      async deleteGood(good) {\r\n        try {\r\n          await this.$confirm(`确定要删除商品 \"${good.name}\" 吗？`, '确认删除', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning',\r\n          })\r\n\r\n          const response = await fetch(`http://localhost:9192/goodapi/delete/${good.id}`, {\r\n            method: 'DELETE',\r\n            headers: {\r\n              'token': localStorage.getItem('token')\r\n            }\r\n          })\r\n\r\n          const result = await response.json()\r\n\r\n          if (result.code === '200') {\r\n            this.$message.success('商品删除成功')\r\n            this.refreshGoods()\r\n          } else {\r\n            this.$message.error(result.msg || '删除失败')\r\n          }\r\n        } catch (error) {\r\n          if (error !== 'cancel') {\r\n            console.error('删除商品失败:', error)\r\n            this.$message.error('网络错误，请稍后重试')\r\n          }\r\n        }\r\n      },\r\n\r\n      // 刷新商品列表\r\n      refreshGoods() {\r\n        this.loading = true\r\n        const BASE = 'http://localhost:9192'\r\n        axios.get(BASE + '/goodapi/list')\r\n          .then(response => {\r\n            const goodsData = (response.data && response.data.data) || []\r\n            this.goodsList = goodsData.map(good => ({\r\n              ...good,\r\n              categoryName: this.categoryMap[good.categoryId] || '未分类',\r\n              price: good.saleMoney * (good.discount || 1)\r\n            }))\r\n          })\r\n          .catch(error => {\r\n            console.error('获取商品列表失败:', error)\r\n            this.$message.error('获取商品列表失败')\r\n          })\r\n          .finally(() => {\r\n            this.loading = false\r\n          })\r\n      }\r\n    },\r\n\r\n    mounted() {\r\n      console.log('商品页面已挂载');\r\n    },\r\n\r\n    beforeUnmount() {\r\n      console.log('商品页面即将卸载');\r\n    }\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .goods-container {\r\n    width: 100%;\r\n    min-height: 100vh;\r\n    padding: 20px;\r\n    background-color: #f5f5f5;\r\n  }\r\n\r\n  .header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 30px;\r\n    padding: 20px;\r\n    background: white;\r\n    border-radius: 12px;\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.1);\r\n    max-width: 1400px;\r\n    margin: 0 auto 30px auto;\r\n  }\r\n\r\n  .page-title {\r\n    color: #333;\r\n    margin: 0;\r\n    font-size: 28px;\r\n  }\r\n\r\n  .user-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 15px;\r\n  }\r\n\r\n  .user-info span {\r\n    color: #666;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .loading,\r\n  .no-data {\r\n    text-align: center;\r\n    color: #999;\r\n    font-size: 16px;\r\n    padding: 40px 0;\r\n  }\r\n\r\n  .goods-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n    gap: 24px;\r\n    max-width: 1400px;\r\n    margin: 0 auto;\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .goods-card {\r\n    border: 1px solid #e5e5e5;\r\n    border-radius: 8px;\r\n    background: #fff;\r\n    padding: 16px 16px 12px;\r\n    text-align: center;\r\n  }\r\n\r\n  .goods-card-header {\r\n    color: #888;\r\n    font-size: 14px;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .goods-card-title {\r\n    font-weight: 700;\r\n    color: #222;\r\n    margin-bottom: 8px;\r\n    cursor: pointer;\r\n    transition: color 0.3s;\r\n  }\r\n\r\n  .goods-card-title:hover {\r\n    color: #409EFF;\r\n  }\r\n\r\n  .goods-card-description {\r\n    font-size: 14px;\r\n    color: #666;\r\n    margin-bottom: 8px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n\r\n  .goods-card-price {\r\n    color: #e60000;\r\n    font-weight: 700;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .btn-add-cart {\r\n    width: 60%;\r\n    margin: 0 auto;\r\n    padding: 10px 12px;\r\n    background-color: #42b983;\r\n    color: #fff;\r\n    border: none;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .btn-add-cart:hover {\r\n    background-color: #369a6e;\r\n  }\r\n\r\n  .goods-card-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .btn-edit, .btn-delete {\r\n    padding: 8px 12px;\r\n    border: none;\r\n    border-radius: 4px;\r\n    cursor: pointer;\r\n    transition: background-color 0.3s;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .btn-edit {\r\n    background-color: #409EFF;\r\n    color: #fff;\r\n  }\r\n\r\n  .btn-edit:hover {\r\n    background-color: #337ecc;\r\n  }\r\n\r\n  .btn-delete {\r\n    background-color: #F56C6C;\r\n    color: #fff;\r\n  }\r\n\r\n  .btn-delete:hover {\r\n    background-color: #dd6161;\r\n  }\r\n\r\n  .good-detail {\r\n    padding: 20px 0;\r\n  }\r\n\r\n  .detail-item {\r\n    display: flex;\r\n    margin-bottom: 15px;\r\n    align-items: center;\r\n  }\r\n\r\n  .detail-item label {\r\n    font-weight: bold;\r\n    width: 100px;\r\n    color: #333;\r\n  }\r\n\r\n  .detail-item span {\r\n    color: #666;\r\n    flex: 1;\r\n  }\r\n\r\n  .dialog-footer {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    gap: 10px;\r\n  }\r\n\r\n  /* 响应式：手机适配 */\r\n  @media (max-width: 768px) {\r\n    .goods-list {\r\n      grid-template-columns: 1fr 1fr;\r\n      padding: 0 8px;\r\n    }\r\n\r\n    .page-title {\r\n      font-size: 24px;\r\n    }\r\n  }\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAQ;;EAEZA,KAAK,EAAC;AAAW;;;EASJA,KAAK,EAAC;;;EAGYA,KAAK,EAAC;AAAY;;EAE/CA,KAAK,EAAC;AAAmB;;;EAEzBA,KAAK,EAAC;AAAwB;;EAC9BA,KAAK,EAAC;AAAkB;;EACxBA,KAAK,EAAC;AAAoB;;;;;EAqDzBA,KAAK,EAAC;AAAe;;;EAeJA,KAAK,EAAC;;;EACxBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAOlBA,KAAK,EAAC;AAAe;;;;;;;;;;;uBAzHjCC,mBAAA,CA+HM,OA/HNC,UA+HM,GA9HJC,mBAAA,CAQM,OARNC,UAQM,G,4BAPJD,mBAAA,CAAkC;IAA9BH,KAAK,EAAC;EAAY,GAAC,QAAM,qBAC7BG,mBAAA,CAKM,OALNE,UAKM,GAJJF,mBAAA,CAA8B,cAAxB,KAAG,GAAAG,gBAAA,CAAGC,KAAA,CAAAC,QAAQ,kBACHD,KAAA,CAAAE,QAAQ,gB,cAAzBC,YAAA,CAAiGC,oBAAA;;IAA1DC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEC,QAAA,CAAAC;;sBAAmB,MAAI,KAAAC,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,mB;;uEACpET,KAAA,CAAAE,QAAQ,gB,cAAzBC,YAAA,CAAyFC,oBAAA;;IAAlDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEC,QAAA,CAAAG;;sBAAW,MAAI,KAAAD,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,mB;;uEAC7EE,YAAA,CAAyDP,oBAAA;IAA9CC,IAAI,EAAC,QAAQ;IAAEC,OAAK,EAAEC,QAAA,CAAAK;;sBAAQ,MAAI,KAAAH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,mB;;sCAIjDI,mBAAA,UAAa,EACFb,KAAA,CAAAc,OAAO,I,cAAlBpB,mBAAA,CAAgD,OAAhDqB,UAAgD,EAAZ,QAAM,KAG1Bf,KAAA,CAAAgB,SAAS,CAACC,MAAM,Q,cAAhCvB,mBAAA,CAYMwB,SAAA;IAAAC,GAAA;EAAA,IAbNN,mBAAA,gBAAmB,EACnBjB,mBAAA,CAYM,OAZNwB,UAYM,I,kBAXJ1B,mBAAA,CAUMwB,SAAA,QAAAG,WAAA,CAViCrB,KAAA,CAAAgB,SAAS,EAAjBM,IAAI;yBAAnC5B,mBAAA,CAUM;MAVDD,KAAK,EAAC,YAAY;MAA4B0B,GAAG,EAAEG,IAAI,CAACC;QAC3D3B,mBAAA,CAAqE,OAArE4B,UAAqE,EAAAzB,gBAAA,CAAnCuB,IAAI,CAACG,YAAY,2BACnD7B,mBAAA,CAAiF;MAA5EH,KAAK,EAAC,kBAAkB;MAAEa,OAAK,EAAAoB,MAAA,IAAEnB,QAAA,CAAAoB,cAAc,CAACL,IAAI;wBAAMA,IAAI,CAACM,IAAI,wBAAAC,UAAA,GACxEjC,mBAAA,CAA0E,OAA1EkC,UAA0E,EAAA/B,gBAAA,CAAnCuB,IAAI,CAACS,WAAW,4BACvDnC,mBAAA,CAAkE,OAAlEoC,UAAkE,EAApC,GAAC,GAAAjC,gBAAA,CAAGQ,QAAA,CAAA0B,WAAW,CAACX,IAAI,CAACY,KAAK,mBACxDtC,mBAAA,CAIM,OAJNuC,WAIM,GAHJvC,mBAAA,CAAoE;MAA5DH,KAAK,EAAC,cAAc;MAAEa,OAAK,EAAAoB,MAAA,IAAEnB,QAAA,CAAA6B,SAAS,CAACd,IAAI;OAAG,OAAK,iBAAAe,WAAA,GAC7CrC,KAAA,CAAAE,QAAQ,gB,cAAtBR,mBAAA,CAAwF;;MAApDD,KAAK,EAAC,UAAU;MAAEa,OAAK,EAAAoB,MAAA,IAAEnB,QAAA,CAAA+B,QAAQ,CAAChB,IAAI;OAAG,IAAE,iBAAAiB,WAAA,K,mCACjEvC,KAAA,CAAAE,QAAQ,gB,cAAtBR,mBAAA,CAA4F;;MAAxDD,KAAK,EAAC,YAAY;MAAEa,OAAK,EAAAoB,MAAA,IAAEnB,QAAA,CAAAiC,UAAU,CAAClB,IAAI;OAAG,IAAE,iBAAAmB,WAAA,K;sGAMzF/C,mBAAA,CAEMwB,SAAA;IAAAC,GAAA;EAAA,IAHNN,mBAAA,SAAY,E,4BACZjB,mBAAA,CAEM;IAFMH,KAAK,EAAC;EAAS,GAAC,QAE5B,oB,mDAEAoB,mBAAA,gBAAmB,EACnBF,YAAA,CA8CY+B,oBAAA;IA7CTC,KAAK,EAAE3C,KAAA,CAAA4C,eAAe;gBACd5C,KAAA,CAAA6C,iBAAiB;+DAAjB7C,KAAA,CAAA6C,iBAAiB,GAAAnB,MAAA;IAC1BoB,KAAK,EAAC;;IAmCKC,MAAM,EAAAC,QAAA,CACf,MAKO,CALPpD,mBAAA,CAKO,QALPqD,WAKO,GAJLtC,YAAA,CAA4DP,oBAAA;MAAhDE,OAAK,EAAAG,MAAA,QAAAA,MAAA,MAAAiB,MAAA,IAAE1B,KAAA,CAAA6C,iBAAiB;;wBAAU,MAAE,KAAApC,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,mB;;QAChDE,YAAA,CAEYP,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEC,QAAA,CAAA2C,QAAQ;MAAGpC,OAAO,EAAEd,KAAA,CAAAmD;;wBACpD,MAA8B,C,kCAA3BnD,KAAA,CAAAoD,UAAU,+B;;;sBArCnB,MA+BU,CA/BVzC,YAAA,CA+BU0C,kBAAA;MA9BRC,GAAG,EAAC,aAAa;MAChBC,KAAK,EAAEvD,KAAA,CAAAwD,QAAQ;MACfC,KAAK,EAAElD,QAAA,CAAAmD,SAAS;MACjB,aAAW,EAAC;;wBAEZ,MAEe,CAFf/C,YAAA,CAEegD,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAA0D,CAA1DlD,YAAA,CAA0DmD,mBAAA;sBAAvC9D,KAAA,CAAAwD,QAAQ,CAAC5B,IAAI;qEAAb5B,KAAA,CAAAwD,QAAQ,CAAC5B,IAAI,GAAAF,MAAA;UAAEqC,WAAW,EAAC;;;UAEhDpD,YAAA,CAEegD,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAA0F,CAA1FlD,YAAA,CAA0FmD,mBAAA;sBAAvE9D,KAAA,CAAAwD,QAAQ,CAACzB,WAAW;qEAApB/B,KAAA,CAAAwD,QAAQ,CAACzB,WAAW,GAAAL,MAAA;UAAErB,IAAI,EAAC,UAAU;UAAC2D,IAAI,EAAC,GAAG;UAACD,WAAW,EAAC;;;UAEhFpD,YAAA,CAEegD,uBAAA;QAFDC,KAAK,EAAC,IAAI;QAACC,IAAI,EAAC;;0BAC5B,MAA4F,CAA5FlD,YAAA,CAA4FsD,0BAAA;sBAAlEjE,KAAA,CAAAwD,QAAQ,CAACU,SAAS;qEAAlBlE,KAAA,CAAAwD,QAAQ,CAACU,SAAS,GAAAxC,MAAA;UAAGyC,GAAG,EAAE,CAAC;UAAGC,SAAS,EAAE,CAAC;UAAEL,WAAW,EAAC;;;UAEpFpD,YAAA,CAEegD,uBAAA;QAFDC,KAAK,EAAC,IAAI;QAACC,IAAI,EAAC;;0BAC5B,MAAqH,CAArHlD,YAAA,CAAqHsD,0BAAA;sBAA3FjE,KAAA,CAAAwD,QAAQ,CAACa,QAAQ;qEAAjBrE,KAAA,CAAAwD,QAAQ,CAACa,QAAQ,GAAA3C,MAAA;UAAGyC,GAAG,EAAE,CAAC;UAAGG,GAAG,EAAE,CAAC;UAAGC,IAAI,EAAE,GAAG;UAAGH,SAAS,EAAE,CAAC;UAAEL,WAAW,EAAC;;;UAExGpD,YAAA,CASegD,uBAAA;QATDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAOY,CAPZlD,YAAA,CAOY6D,oBAAA;sBAPQxE,KAAA,CAAAwD,QAAQ,CAACiB,UAAU;qEAAnBzE,KAAA,CAAAwD,QAAQ,CAACiB,UAAU,GAAA/C,MAAA;UAAEqC,WAAW,EAAC,OAAO;UAACW,KAAmB,EAAnB;YAAA;UAAA;;4BAEzD,MAA8B,E,kBADhChF,mBAAA,CAKEwB,SAAA,QAAAG,WAAA,CAJmBrB,KAAA,CAAA2E,UAAU,EAAtBC,QAAQ;iCADjBzE,YAAA,CAKE0E,oBAAA;cAHC1D,GAAG,EAAEyD,QAAQ,CAACrD,EAAE;cAChBqC,KAAK,EAAEgB,QAAQ,CAAChD,IAAI;cACpBkD,KAAK,EAAEF,QAAQ,CAACrD;;;;;;UAIvBZ,YAAA,CAEegD,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAA0C,CAA1ClD,YAAA,CAA0CoE,oBAAA;sBAAtB/E,KAAA,CAAAwD,QAAQ,CAACwB,SAAS;qEAAlBhF,KAAA,CAAAwD,QAAQ,CAACwB,SAAS,GAAAtD,MAAA;;;;;;;8CAc5Cb,mBAAA,aAAgB,EAChBF,YAAA,CA0CY+B,oBAAA;IAzCVC,KAAK,EAAC,MAAM;gBACH3C,KAAA,CAAAiF,mBAAmB;iEAAnBjF,KAAA,CAAAiF,mBAAmB,GAAAvD,MAAA;IAC5BoB,KAAK,EAAC;;IAiCKC,MAAM,EAAAC,QAAA,CACf,MAGO,CAHPpD,mBAAA,CAGO,QAHPsF,WAGO,GAFLvE,YAAA,CAA8DP,oBAAA;MAAlDE,OAAK,EAAAG,MAAA,QAAAA,MAAA,MAAAiB,MAAA,IAAE1B,KAAA,CAAAiF,mBAAmB;;wBAAU,MAAE,KAAAxE,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,mB;;QAClDE,YAAA,CAA4EP,oBAAA;MAAjEC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAAG,MAAA,QAAAA,MAAA,MAAAiB,MAAA,IAAEnB,QAAA,CAAA6B,SAAS,CAACpC,KAAA,CAAAmF,YAAY;;wBAAG,MAAK,KAAA1E,MAAA,SAAAA,MAAA,Q,iBAAL,OAAK,mB;;;sBAlCpE,MA6BM,CA7BKT,KAAA,CAAAmF,YAAY,I,cAAvBzF,mBAAA,CA6BM,OA7BN0F,WA6BM,GA5BJxF,mBAAA,CAGM,OAHNyF,WAGM,G,4BAFJzF,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAAoC,cAAAG,gBAAA,CAA3BC,KAAA,CAAAmF,YAAY,CAACvD,IAAI,iB,GAE5BhC,mBAAA,CAGM,OAHN0F,WAGM,G,4BAFJ1F,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAAqD,cAAAG,gBAAA,CAA5CC,KAAA,CAAAmF,YAAY,CAACpD,WAAW,2B,GAEnCnC,mBAAA,CAGM,OAHN2F,WAGM,G,4BAFJ3F,mBAAA,CAAkB,eAAX,KAAG,qBACVA,mBAAA,CAAmD,cAA7C,GAAC,GAAAG,gBAAA,CAAGQ,QAAA,CAAA0B,WAAW,CAACjC,KAAA,CAAAmF,YAAY,CAACjD,KAAK,kB,GAE1CtC,mBAAA,CAGM,OAHN4F,WAGM,G,4BAFJ5F,mBAAA,CAAkB,eAAX,KAAG,qBACVA,mBAAA,CAAiG,cAAAG,gBAAA,CAAxFC,KAAA,CAAAmF,YAAY,CAACd,QAAQ,IAAIrE,KAAA,CAAAmF,YAAY,CAACd,QAAQ,QAAQoB,OAAO,kC,GAExE7F,mBAAA,CAGM,OAHN8F,WAGM,G,4BAFJ9F,mBAAA,CAAkB,eAAX,KAAG,qBACVA,mBAAA,CAAqD,cAAAG,gBAAA,CAA5CC,KAAA,CAAAmF,YAAY,CAAC1D,YAAY,0B,GAEpC7B,mBAAA,CAGM,OAHN+F,WAGM,G,4BAFJ/F,mBAAA,CAAkB,eAAX,KAAG,qBACVA,mBAAA,CAA0C,cAAAG,gBAAA,CAAjCC,KAAA,CAAAmF,YAAY,CAACS,KAAK,sB,GAE7BhG,mBAAA,CAGM,OAHNiG,WAGM,G,4BAFJjG,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAAqD,cAAAG,gBAAA,CAA5CC,KAAA,CAAAmF,YAAY,CAACH,SAAS,6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}