{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport isObject from './isObject.js';\nimport isPrototype from './_isPrototype.js';\nimport nativeKeysIn from './_nativeKeysIn.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.keysIn` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeysIn(object) {\n  if (!isObject(object)) {\n    return nativeKeysIn(object);\n  }\n  var isProto = isPrototype(object),\n    result = [];\n  for (var key in object) {\n    if (!(key == 'constructor' && (isProto || !hasOwnProperty.call(object, key)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\nexport default baseKeysIn;", "map": {"version": 3, "names": ["isObject", "isPrototype", "nativeKeysIn", "objectProto", "Object", "prototype", "hasOwnProperty", "baseKeysIn", "object", "isProto", "result", "key", "call", "push"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_baseKeysIn.js"], "sourcesContent": ["import isObject from './isObject.js';\nimport isPrototype from './_isPrototype.js';\nimport nativeKeysIn from './_nativeKeysIn.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.keysIn` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeysIn(object) {\n  if (!isObject(object)) {\n    return nativeKeysIn(object);\n  }\n  var isProto = isPrototype(object),\n      result = [];\n\n  for (var key in object) {\n    if (!(key == 'constructor' && (isProto || !hasOwnProperty.call(object, key)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\nexport default baseKeysIn;\n"], "mappings": ";AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,YAAY,MAAM,oBAAoB;;AAE7C;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,MAAM,EAAE;EAC1B,IAAI,CAACR,QAAQ,CAACQ,MAAM,CAAC,EAAE;IACrB,OAAON,YAAY,CAACM,MAAM,CAAC;EAC7B;EACA,IAAIC,OAAO,GAAGR,WAAW,CAACO,MAAM,CAAC;IAC7BE,MAAM,GAAG,EAAE;EAEf,KAAK,IAAIC,GAAG,IAAIH,MAAM,EAAE;IACtB,IAAI,EAAEG,GAAG,IAAI,aAAa,KAAKF,OAAO,IAAI,CAACH,cAAc,CAACM,IAAI,CAACJ,MAAM,EAAEG,GAAG,CAAC,CAAC,CAAC,EAAE;MAC7ED,MAAM,CAACG,IAAI,CAACF,GAAG,CAAC;IAClB;EACF;EACA,OAAOD,MAAM;AACf;AAEA,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}