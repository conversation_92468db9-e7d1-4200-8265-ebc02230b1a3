{"ast": null, "code": "import baseClamp from './_baseClamp.js';\nimport baseToString from './_baseToString.js';\nimport toInteger from './toInteger.js';\nimport toString from './toString.js';\n\n/**\n * Checks if `string` ends with the given target string.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to inspect.\n * @param {string} [target] The string to search for.\n * @param {number} [position=string.length] The position to search up to.\n * @returns {boolean} Returns `true` if `string` ends with `target`,\n *  else `false`.\n * @example\n *\n * _.endsWith('abc', 'c');\n * // => true\n *\n * _.endsWith('abc', 'b');\n * // => false\n *\n * _.endsWith('abc', 'b', 2);\n * // => true\n */\nfunction endsWith(string, target, position) {\n  string = toString(string);\n  target = baseToString(target);\n  var length = string.length;\n  position = position === undefined ? length : baseClamp(toInteger(position), 0, length);\n  var end = position;\n  position -= target.length;\n  return position >= 0 && string.slice(position, end) == target;\n}\nexport default endsWith;", "map": {"version": 3, "names": ["baseClamp", "baseToString", "toInteger", "toString", "endsWith", "string", "target", "position", "length", "undefined", "end", "slice"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/endsWith.js"], "sourcesContent": ["import baseClamp from './_baseClamp.js';\nimport baseToString from './_baseToString.js';\nimport toInteger from './toInteger.js';\nimport toString from './toString.js';\n\n/**\n * Checks if `string` ends with the given target string.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to inspect.\n * @param {string} [target] The string to search for.\n * @param {number} [position=string.length] The position to search up to.\n * @returns {boolean} Returns `true` if `string` ends with `target`,\n *  else `false`.\n * @example\n *\n * _.endsWith('abc', 'c');\n * // => true\n *\n * _.endsWith('abc', 'b');\n * // => false\n *\n * _.endsWith('abc', 'b', 2);\n * // => true\n */\nfunction endsWith(string, target, position) {\n  string = toString(string);\n  target = baseToString(target);\n\n  var length = string.length;\n  position = position === undefined\n    ? length\n    : baseClamp(toInteger(position), 0, length);\n\n  var end = position;\n  position -= target.length;\n  return position >= 0 && string.slice(position, end) == target;\n}\n\nexport default endsWith;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EAC1CF,MAAM,GAAGF,QAAQ,CAACE,MAAM,CAAC;EACzBC,MAAM,GAAGL,YAAY,CAACK,MAAM,CAAC;EAE7B,IAAIE,MAAM,GAAGH,MAAM,CAACG,MAAM;EAC1BD,QAAQ,GAAGA,QAAQ,KAAKE,SAAS,GAC7BD,MAAM,GACNR,SAAS,CAACE,SAAS,CAACK,QAAQ,CAAC,EAAE,CAAC,EAAEC,MAAM,CAAC;EAE7C,IAAIE,GAAG,GAAGH,QAAQ;EAClBA,QAAQ,IAAID,MAAM,CAACE,MAAM;EACzB,OAAOD,QAAQ,IAAI,CAAC,IAAIF,MAAM,CAACM,KAAK,CAACJ,QAAQ,EAAEG,GAAG,CAAC,IAAIJ,MAAM;AAC/D;AAEA,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}