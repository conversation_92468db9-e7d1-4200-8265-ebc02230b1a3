{"ast": null, "code": "import createMathOperation from './_createMathOperation.js';\n\n/**\n * Multiply two numbers.\n *\n * @static\n * @memberOf _\n * @since 4.7.0\n * @category Math\n * @param {number} multiplier The first number in a multiplication.\n * @param {number} multiplicand The second number in a multiplication.\n * @returns {number} Returns the product.\n * @example\n *\n * _.multiply(6, 4);\n * // => 24\n */\nvar multiply = createMathOperation(function (multiplier, multiplicand) {\n  return multiplier * multiplicand;\n}, 1);\nexport default multiply;", "map": {"version": 3, "names": ["createMathOperation", "multiply", "multiplier", "multiplicand"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/multiply.js"], "sourcesContent": ["import createMathOperation from './_createMathOperation.js';\n\n/**\n * Multiply two numbers.\n *\n * @static\n * @memberOf _\n * @since 4.7.0\n * @category Math\n * @param {number} multiplier The first number in a multiplication.\n * @param {number} multiplicand The second number in a multiplication.\n * @returns {number} Returns the product.\n * @example\n *\n * _.multiply(6, 4);\n * // => 24\n */\nvar multiply = createMathOperation(function(multiplier, multiplicand) {\n  return multiplier * multiplicand;\n}, 1);\n\nexport default multiply;\n"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,2BAA2B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAGD,mBAAmB,CAAC,UAASE,UAAU,EAAEC,YAAY,EAAE;EACpE,OAAOD,UAAU,GAAGC,YAAY;AAClC,CAAC,EAAE,CAAC,CAAC;AAEL,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}