{"ast": null, "code": "import baseRandom from './_baseRandom.js';\n\n/**\n * A specialized version of `_.shuffle` which mutates and sets the size of `array`.\n *\n * @private\n * @param {Array} array The array to shuffle.\n * @param {number} [size=array.length] The size of `array`.\n * @returns {Array} Returns `array`.\n */\nfunction shuffleSelf(array, size) {\n  var index = -1,\n    length = array.length,\n    lastIndex = length - 1;\n  size = size === undefined ? length : size;\n  while (++index < size) {\n    var rand = baseRandom(index, lastIndex),\n      value = array[rand];\n    array[rand] = array[index];\n    array[index] = value;\n  }\n  array.length = size;\n  return array;\n}\nexport default shuffleSelf;", "map": {"version": 3, "names": ["baseRandom", "shuffleSelf", "array", "size", "index", "length", "lastIndex", "undefined", "rand", "value"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_shuffleSelf.js"], "sourcesContent": ["import baseRandom from './_baseRandom.js';\n\n/**\n * A specialized version of `_.shuffle` which mutates and sets the size of `array`.\n *\n * @private\n * @param {Array} array The array to shuffle.\n * @param {number} [size=array.length] The size of `array`.\n * @returns {Array} Returns `array`.\n */\nfunction shuffleSelf(array, size) {\n  var index = -1,\n      length = array.length,\n      lastIndex = length - 1;\n\n  size = size === undefined ? length : size;\n  while (++index < size) {\n    var rand = baseRandom(index, lastIndex),\n        value = array[rand];\n\n    array[rand] = array[index];\n    array[index] = value;\n  }\n  array.length = size;\n  return array;\n}\n\nexport default shuffleSelf;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAChC,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,SAAS,GAAGD,MAAM,GAAG,CAAC;EAE1BF,IAAI,GAAGA,IAAI,KAAKI,SAAS,GAAGF,MAAM,GAAGF,IAAI;EACzC,OAAO,EAAEC,KAAK,GAAGD,IAAI,EAAE;IACrB,IAAIK,IAAI,GAAGR,UAAU,CAACI,KAAK,EAAEE,SAAS,CAAC;MACnCG,KAAK,GAAGP,KAAK,CAACM,IAAI,CAAC;IAEvBN,KAAK,CAACM,IAAI,CAAC,GAAGN,KAAK,CAACE,KAAK,CAAC;IAC1BF,KAAK,CAACE,KAAK,CAAC,GAAGK,KAAK;EACtB;EACAP,KAAK,CAACG,MAAM,GAAGF,IAAI;EACnB,OAAOD,KAAK;AACd;AAEA,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}