{"ast": null, "code": "import apply from './_apply.js';\nimport arrayMap from './_arrayMap.js';\nimport baseFlatten from './_baseFlatten.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\nimport baseUnary from './_baseUnary.js';\nimport castRest from './_castRest.js';\nimport isArray from './isArray.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min;\n\n/**\n * Creates a function that invokes `func` with its arguments transformed.\n *\n * @static\n * @since 4.0.0\n * @memberOf _\n * @category Function\n * @param {Function} func The function to wrap.\n * @param {...(Function|Function[])} [transforms=[_.identity]]\n *  The argument transforms.\n * @returns {Function} Returns the new function.\n * @example\n *\n * function doubled(n) {\n *   return n * 2;\n * }\n *\n * function square(n) {\n *   return n * n;\n * }\n *\n * var func = _.overArgs(function(x, y) {\n *   return [x, y];\n * }, [square, doubled]);\n *\n * func(9, 3);\n * // => [81, 6]\n *\n * func(10, 5);\n * // => [100, 10]\n */\nvar overArgs = castRest(function (func, transforms) {\n  transforms = transforms.length == 1 && isArray(transforms[0]) ? arrayMap(transforms[0], baseUnary(baseIteratee)) : arrayMap(baseFlatten(transforms, 1), baseUnary(baseIteratee));\n  var funcsLength = transforms.length;\n  return baseRest(function (args) {\n    var index = -1,\n      length = nativeMin(args.length, funcsLength);\n    while (++index < length) {\n      args[index] = transforms[index].call(this, args[index]);\n    }\n    return apply(func, this, args);\n  });\n});\nexport default overArgs;", "map": {"version": 3, "names": ["apply", "arrayMap", "baseFlatten", "baseIteratee", "baseRest", "baseUnary", "castRest", "isArray", "nativeMin", "Math", "min", "overArgs", "func", "transforms", "length", "funcsLength", "args", "index", "call"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/overArgs.js"], "sourcesContent": ["import apply from './_apply.js';\nimport arrayMap from './_arrayMap.js';\nimport baseFlatten from './_baseFlatten.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\nimport baseUnary from './_baseUnary.js';\nimport castRest from './_castRest.js';\nimport isArray from './isArray.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min;\n\n/**\n * Creates a function that invokes `func` with its arguments transformed.\n *\n * @static\n * @since 4.0.0\n * @memberOf _\n * @category Function\n * @param {Function} func The function to wrap.\n * @param {...(Function|Function[])} [transforms=[_.identity]]\n *  The argument transforms.\n * @returns {Function} Returns the new function.\n * @example\n *\n * function doubled(n) {\n *   return n * 2;\n * }\n *\n * function square(n) {\n *   return n * n;\n * }\n *\n * var func = _.overArgs(function(x, y) {\n *   return [x, y];\n * }, [square, doubled]);\n *\n * func(9, 3);\n * // => [81, 6]\n *\n * func(10, 5);\n * // => [100, 10]\n */\nvar overArgs = castRest(function(func, transforms) {\n  transforms = (transforms.length == 1 && isArray(transforms[0]))\n    ? arrayMap(transforms[0], baseUnary(baseIteratee))\n    : arrayMap(baseFlatten(transforms, 1), baseUnary(baseIteratee));\n\n  var funcsLength = transforms.length;\n  return baseRest(function(args) {\n    var index = -1,\n        length = nativeMin(args.length, funcsLength);\n\n    while (++index < length) {\n      args[index] = transforms[index].call(this, args[index]);\n    }\n    return apply(func, this, args);\n  });\n});\n\nexport default overArgs;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAGL,QAAQ,CAAC,UAASM,IAAI,EAAEC,UAAU,EAAE;EACjDA,UAAU,GAAIA,UAAU,CAACC,MAAM,IAAI,CAAC,IAAIP,OAAO,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC,GAC1DZ,QAAQ,CAACY,UAAU,CAAC,CAAC,CAAC,EAAER,SAAS,CAACF,YAAY,CAAC,CAAC,GAChDF,QAAQ,CAACC,WAAW,CAACW,UAAU,EAAE,CAAC,CAAC,EAAER,SAAS,CAACF,YAAY,CAAC,CAAC;EAEjE,IAAIY,WAAW,GAAGF,UAAU,CAACC,MAAM;EACnC,OAAOV,QAAQ,CAAC,UAASY,IAAI,EAAE;IAC7B,IAAIC,KAAK,GAAG,CAAC,CAAC;MACVH,MAAM,GAAGN,SAAS,CAACQ,IAAI,CAACF,MAAM,EAAEC,WAAW,CAAC;IAEhD,OAAO,EAAEE,KAAK,GAAGH,MAAM,EAAE;MACvBE,IAAI,CAACC,KAAK,CAAC,GAAGJ,UAAU,CAACI,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,EAAEF,IAAI,CAACC,KAAK,CAAC,CAAC;IACzD;IACA,OAAOjB,KAAK,CAACY,IAAI,EAAE,IAAI,EAAEI,IAAI,CAAC;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,eAAeL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}