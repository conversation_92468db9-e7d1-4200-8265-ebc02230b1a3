{"ast": null, "code": "import root from './_root.js';\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Built-in value references. */\nvar Buffer = moduleExports ? root.Buffer : undefined,\n  allocUnsafe = Buffer ? Buffer.allocUnsafe : undefined;\n\n/**\n * Creates a clone of  `buffer`.\n *\n * @private\n * @param {<PERSON>uffer} buffer The buffer to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Buffer} Returns the cloned buffer.\n */\nfunction cloneBuffer(buffer, isDeep) {\n  if (isDeep) {\n    return buffer.slice();\n  }\n  var length = buffer.length,\n    result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);\n  buffer.copy(result);\n  return result;\n}\nexport default cloneBuffer;", "map": {"version": 3, "names": ["root", "freeExports", "exports", "nodeType", "freeModule", "module", "moduleExports", "<PERSON><PERSON><PERSON>", "undefined", "allocUnsafe", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "isDeep", "slice", "length", "result", "constructor", "copy"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_cloneBuffer.js"], "sourcesContent": ["import root from './_root.js';\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Built-in value references. */\nvar Buffer = moduleExports ? root.Buffer : undefined,\n    allocUnsafe = Buffer ? Buffer.allocUnsafe : undefined;\n\n/**\n * Creates a clone of  `buffer`.\n *\n * @private\n * @param {<PERSON>uffer} buffer The buffer to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Buffer} Returns the cloned buffer.\n */\nfunction cloneBuffer(buffer, isDeep) {\n  if (isDeep) {\n    return buffer.slice();\n  }\n  var length = buffer.length,\n      result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);\n\n  buffer.copy(result);\n  return result;\n}\n\nexport default cloneBuffer;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;;AAE7B;AACA,IAAIC,WAAW,GAAG,OAAOC,OAAO,IAAI,QAAQ,IAAIA,OAAO,IAAI,CAACA,OAAO,CAACC,QAAQ,IAAID,OAAO;;AAEvF;AACA,IAAIE,UAAU,GAAGH,WAAW,IAAI,OAAOI,MAAM,IAAI,QAAQ,IAAIA,MAAM,IAAI,CAACA,MAAM,CAACF,QAAQ,IAAIE,MAAM;;AAEjG;AACA,IAAIC,aAAa,GAAGF,UAAU,IAAIA,UAAU,CAACF,OAAO,KAAKD,WAAW;;AAEpE;AACA,IAAIM,MAAM,GAAGD,aAAa,GAAGN,IAAI,CAACO,MAAM,GAAGC,SAAS;EAChDC,WAAW,GAAGF,MAAM,GAAGA,MAAM,CAACE,WAAW,GAAGD,SAAS;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACnC,IAAIA,MAAM,EAAE;IACV,OAAOD,MAAM,CAACE,KAAK,CAAC,CAAC;EACvB;EACA,IAAIC,MAAM,GAAGH,MAAM,CAACG,MAAM;IACtBC,MAAM,GAAGN,WAAW,GAAGA,WAAW,CAACK,MAAM,CAAC,GAAG,IAAIH,MAAM,CAACK,WAAW,CAACF,MAAM,CAAC;EAE/EH,MAAM,CAACM,IAAI,CAACF,MAAM,CAAC;EACnB,OAAOA,MAAM;AACf;AAEA,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}