{"ast": null, "code": "import createRound from './_createRound.js';\n\n/**\n * Computes `number` rounded down to `precision`.\n *\n * @static\n * @memberOf _\n * @since 3.10.0\n * @category Math\n * @param {number} number The number to round down.\n * @param {number} [precision=0] The precision to round down to.\n * @returns {number} Returns the rounded down number.\n * @example\n *\n * _.floor(4.006);\n * // => 4\n *\n * _.floor(0.046, 2);\n * // => 0.04\n *\n * _.floor(4060, -2);\n * // => 4000\n */\nvar floor = createRound('floor');\nexport default floor;", "map": {"version": 3, "names": ["createRound", "floor"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/floor.js"], "sourcesContent": ["import createRound from './_createRound.js';\n\n/**\n * Computes `number` rounded down to `precision`.\n *\n * @static\n * @memberOf _\n * @since 3.10.0\n * @category Math\n * @param {number} number The number to round down.\n * @param {number} [precision=0] The precision to round down to.\n * @returns {number} Returns the rounded down number.\n * @example\n *\n * _.floor(4.006);\n * // => 4\n *\n * _.floor(0.046, 2);\n * // => 0.04\n *\n * _.floor(4060, -2);\n * // => 4000\n */\nvar floor = createRound('floor');\n\nexport default floor;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,GAAGD,WAAW,CAAC,OAAO,CAAC;AAEhC,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}