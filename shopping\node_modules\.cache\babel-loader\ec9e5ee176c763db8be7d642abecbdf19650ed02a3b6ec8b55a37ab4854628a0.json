{"ast": null, "code": "import apply from './_apply.js';\nimport baseEach from './_baseEach.js';\nimport baseInvoke from './_baseInvoke.js';\nimport baseRest from './_baseRest.js';\nimport isArrayLike from './isArrayLike.js';\n\n/**\n * Invokes the method at `path` of each element in `collection`, returning\n * an array of the results of each invoked method. Any additional arguments\n * are provided to each invoked method. If `path` is a function, it's invoked\n * for, and `this` bound to, each element in `collection`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Array|Function|string} path The path of the method to invoke or\n *  the function invoked per iteration.\n * @param {...*} [args] The arguments to invoke each method with.\n * @returns {Array} Returns the array of results.\n * @example\n *\n * _.invokeMap([[5, 1, 7], [3, 2, 1]], 'sort');\n * // => [[1, 5, 7], [1, 2, 3]]\n *\n * _.invokeMap([123, 456], String.prototype.split, '');\n * // => [['1', '2', '3'], ['4', '5', '6']]\n */\nvar invokeMap = baseRest(function (collection, path, args) {\n  var index = -1,\n    isFunc = typeof path == 'function',\n    result = isArrayLike(collection) ? Array(collection.length) : [];\n  baseEach(collection, function (value) {\n    result[++index] = isFunc ? apply(path, value, args) : baseInvoke(value, path, args);\n  });\n  return result;\n});\nexport default invokeMap;", "map": {"version": 3, "names": ["apply", "baseEach", "baseInvoke", "baseRest", "isArrayLike", "invokeMap", "collection", "path", "args", "index", "isFunc", "result", "Array", "length", "value"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/invokeMap.js"], "sourcesContent": ["import apply from './_apply.js';\nimport baseEach from './_baseEach.js';\nimport baseInvoke from './_baseInvoke.js';\nimport baseRest from './_baseRest.js';\nimport isArrayLike from './isArrayLike.js';\n\n/**\n * Invokes the method at `path` of each element in `collection`, returning\n * an array of the results of each invoked method. Any additional arguments\n * are provided to each invoked method. If `path` is a function, it's invoked\n * for, and `this` bound to, each element in `collection`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Array|Function|string} path The path of the method to invoke or\n *  the function invoked per iteration.\n * @param {...*} [args] The arguments to invoke each method with.\n * @returns {Array} Returns the array of results.\n * @example\n *\n * _.invokeMap([[5, 1, 7], [3, 2, 1]], 'sort');\n * // => [[1, 5, 7], [1, 2, 3]]\n *\n * _.invokeMap([123, 456], String.prototype.split, '');\n * // => [['1', '2', '3'], ['4', '5', '6']]\n */\nvar invokeMap = baseRest(function(collection, path, args) {\n  var index = -1,\n      isFunc = typeof path == 'function',\n      result = isArrayLike(collection) ? Array(collection.length) : [];\n\n  baseEach(collection, function(value) {\n    result[++index] = isFunc ? apply(path, value, args) : baseInvoke(value, path, args);\n  });\n  return result;\n});\n\nexport default invokeMap;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,WAAW,MAAM,kBAAkB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAGF,QAAQ,CAAC,UAASG,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACxD,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAG,OAAOH,IAAI,IAAI,UAAU;IAClCI,MAAM,GAAGP,WAAW,CAACE,UAAU,CAAC,GAAGM,KAAK,CAACN,UAAU,CAACO,MAAM,CAAC,GAAG,EAAE;EAEpEZ,QAAQ,CAACK,UAAU,EAAE,UAASQ,KAAK,EAAE;IACnCH,MAAM,CAAC,EAAEF,KAAK,CAAC,GAAGC,MAAM,GAAGV,KAAK,CAACO,IAAI,EAAEO,KAAK,EAAEN,IAAI,CAAC,GAAGN,UAAU,CAACY,KAAK,EAAEP,IAAI,EAAEC,IAAI,CAAC;EACrF,CAAC,CAAC;EACF,OAAOG,MAAM;AACf,CAAC,CAAC;AAEF,eAAeN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}