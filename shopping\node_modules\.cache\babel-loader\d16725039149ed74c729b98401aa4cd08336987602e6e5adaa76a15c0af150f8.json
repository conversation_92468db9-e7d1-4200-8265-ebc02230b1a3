{"ast": null, "code": "import baseForOwn from './_baseForOwn.js';\n\n/**\n * The base implementation of `_.invert` and `_.invertBy` which inverts\n * `object` with values transformed by `iteratee` and set by `setter`.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform values.\n * @param {Object} accumulator The initial inverted object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction baseInverter(object, setter, iteratee, accumulator) {\n  baseForOwn(object, function (value, key, object) {\n    setter(accumulator, iteratee(value), key, object);\n  });\n  return accumulator;\n}\nexport default baseInverter;", "map": {"version": 3, "names": ["baseForOwn", "baseInverter", "object", "setter", "iteratee", "accumulator", "value", "key"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_baseInverter.js"], "sourcesContent": ["import baseForOwn from './_baseForOwn.js';\n\n/**\n * The base implementation of `_.invert` and `_.invertBy` which inverts\n * `object` with values transformed by `iteratee` and set by `setter`.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform values.\n * @param {Object} accumulator The initial inverted object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction baseInverter(object, setter, iteratee, accumulator) {\n  baseForOwn(object, function(value, key, object) {\n    setter(accumulator, iteratee(value), key, object);\n  });\n  return accumulator;\n}\n\nexport default baseInverter;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EAC3DL,UAAU,CAACE,MAAM,EAAE,UAASI,KAAK,EAAEC,GAAG,EAAEL,MAAM,EAAE;IAC9CC,MAAM,CAACE,WAAW,EAAED,QAAQ,CAACE,KAAK,CAAC,EAAEC,GAAG,EAAEL,MAAM,CAAC;EACnD,CAAC,CAAC;EACF,OAAOG,WAAW;AACpB;AAEA,eAAeJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}