{"ast": null, "code": "/** Used to match template delimiters. */\nvar reInterpolate = /<%=([\\s\\S]+?)%>/g;\nexport default reInterpolate;", "map": {"version": 3, "names": ["reInterpolate"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_reInterpolate.js"], "sourcesContent": ["/** Used to match template delimiters. */\nvar reInterpolate = /<%=([\\s\\S]+?)%>/g;\n\nexport default reInterpolate;\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAG,kBAAkB;AAEtC,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}