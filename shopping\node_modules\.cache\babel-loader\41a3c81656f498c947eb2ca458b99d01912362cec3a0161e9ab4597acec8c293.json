{"ast": null, "code": "import { defineComponent, ref, createVNode, Fragment } from 'vue';\nimport { ensureOnlyChild } from '../../../utils/vue/vnode.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { composeRefs } from '../../../utils/vue/refs.mjs';\nconst forwardRefProps = buildProps({\n  setRef: {\n    type: definePropType(Function),\n    required: true\n  },\n  onlyChild: Boolean\n});\nvar ForwardRef = defineComponent({\n  props: forwardRefProps,\n  setup(props, {\n    slots\n  }) {\n    const fragmentRef = ref();\n    const setRef = composeRefs(fragmentRef, el => {\n      if (el) {\n        props.setRef(el.nextElementSibling);\n      } else {\n        props.setRef(null);\n      }\n    });\n    return () => {\n      var _a;\n      const [firstChild] = ((_a = slots.default) == null ? void 0 : _a.call(slots)) || [];\n      const child = props.onlyChild ? ensureOnlyChild(firstChild.children) : firstChild.children;\n      return createVNode(Fragment, {\n        \"ref\": setRef\n      }, [child]);\n    };\n  }\n});\nexport { ForwardRef as default, forwardRefProps };", "map": {"version": 3, "names": ["forwardRefProps", "buildProps", "setRef", "type", "definePropType", "Function", "required", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "ForwardRef", "defineComponent", "props", "setup", "fragmentRef", "ref", "composeRefs", "el", "nextElement<PERSON><PERSON>ling", "_a", "<PERSON><PERSON><PERSON><PERSON>", "slots", "default", "call", "child", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "createVNode", "Fragment"], "sources": ["../../../../../../packages/components/tooltip-v2/src/forward-ref.tsx"], "sourcesContent": ["import { Fragment, defineComponent, ref } from 'vue'\nimport {\n  buildProps,\n  composeRefs,\n  definePropType,\n  ensureOnlyChild,\n} from '@element-plus/utils'\n\nimport type {\n  ExtractPropTypes,\n  VNodeArrayChildren,\n  __ExtractPublicPropTypes,\n} from 'vue'\n\nexport type RefSetter = (el: HTMLElement | null) => void\n\nexport const forwardRefProps = buildProps({\n  setRef: { type: definePropType<RefSetter>(Function), required: true },\n  onlyChild: Boolean,\n} as const)\n\nexport type ForwardRefProps = ExtractPropTypes<typeof forwardRefProps>\nexport type ForwardRefPropsPublic = __ExtractPublicPropTypes<\n  typeof forwardRefProps\n>\n\n// TODO: consider make this component a reusable component without the only child feature.\nexport default defineComponent({\n  props: forwardRefProps,\n  setup(props, { slots }) {\n    const fragmentRef = ref()\n    const setRef = composeRefs(fragmentRef, (el) => {\n      // vue fragments is represented as a text element.\n      // The first element sibling should be the first element children of fragment.\n      // This is how we get the element.\n      if (el) {\n        props.setRef(\n          (el as HTMLElement).nextElementSibling as HTMLElement | null\n        )\n      } else {\n        props.setRef(null)\n      }\n    })\n    return () => {\n      const [firstChild] = slots.default?.() || []\n      const child = props.onlyChild\n        ? ensureOnlyChild(firstChild.children as VNodeArrayChildren)\n        : firstChild.children\n      // Dunno why the ref for jsx complains about the typing issue which was not\n      // in template\n      return <Fragment ref={setRef as any}>{child}</Fragment>\n    }\n  },\n})\n"], "mappings": ";;;;AAgBa,MAAAA,eAAe,GAAGC,UAAU,CAAC;EACxCC,MAAM,EAAE;IAAEC,IAAI,EAAEC,cAAc,CAAYC,QAAZ,CAAtB;IAA6CC,QAAQ,EAAE;GADvB;EAExCC,SAAS,EAAEC;AAF6B,CAAD;AAUzC,IAAAC,UAAA,GAAAC,eAAA;EACAC,KAAA,EAAAX,eAAA;EACEY,KAAKD,MADwB;;GAExB;IAAU,MAAAE,WAAA,GAAAC,GAAA;IAAS,MAAAZ,MAAA,GAAAa,WAAA,CAAAF,WAAA,EAAAG,EAAA;MAChB,IAAAA,EAAA;QACNL,KAAA,CAAAT,MAAe,CAAAc,EAAA,CAAAC,kBAAW;MACxB;QACAN,KAAA,CAAAT,MAAA;MACA;IACA;IACE,aAAM;MAGP,IAAMgB,EAAA;YACA,CAAAC,UAAL,MAAAD,EAAA,GAAAE,KAAA,CAAAC,OAAA,qBAAAH,EAAA,CAAAI,IAAA,CAAAF,KAAA;MACD,MAAAG,KAAA,GAAAZ,KAAA,CAAAJ,SAAA,GAAAiB,eAAA,CAAAL,UAAA,CAAAM,QAAA,IAAAN,UAAA,CAAAM,QAAA;MAVH,OAAAC,WAAA,CAAAC,QAAA;QAYA,KAAa,EAAAzB;MACX,IAAAqB,KAAM;IACN;EAIA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}