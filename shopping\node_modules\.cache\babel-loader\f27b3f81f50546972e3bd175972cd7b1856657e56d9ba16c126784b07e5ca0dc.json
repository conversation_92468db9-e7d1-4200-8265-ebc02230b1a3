{"ast": null, "code": "/**\n * Gets the value at `key`, unless `key` is \"__proto__\" or \"constructor\".\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction safeGet(object, key) {\n  if (key === 'constructor' && typeof object[key] === 'function') {\n    return;\n  }\n  if (key == '__proto__') {\n    return;\n  }\n  return object[key];\n}\nexport default safeGet;", "map": {"version": 3, "names": ["safeGet", "object", "key"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_safeGet.js"], "sourcesContent": ["/**\n * Gets the value at `key`, unless `key` is \"__proto__\" or \"constructor\".\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction safeGet(object, key) {\n  if (key === 'constructor' && typeof object[key] === 'function') {\n    return;\n  }\n\n  if (key == '__proto__') {\n    return;\n  }\n\n  return object[key];\n}\n\nexport default safeGet;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAOA,CAACC,MAAM,EAAEC,GAAG,EAAE;EAC5B,IAAIA,GAAG,KAAK,aAAa,IAAI,OAAOD,MAAM,CAACC,GAAG,CAAC,KAAK,UAAU,EAAE;IAC9D;EACF;EAEA,IAAIA,GAAG,IAAI,WAAW,EAAE;IACtB;EACF;EAEA,OAAOD,MAAM,CAACC,GAAG,CAAC;AACpB;AAEA,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}