{"ast": null, "code": "import arrayMap from './_arrayMap.js';\n\n/**\n * The base implementation of `_.toPairs` and `_.toPairsIn` which creates an array\n * of key-value pairs for `object` corresponding to the property names of `props`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} props The property names to get values for.\n * @returns {Object} Returns the key-value pairs.\n */\nfunction baseToPairs(object, props) {\n  return arrayMap(props, function (key) {\n    return [key, object[key]];\n  });\n}\nexport default baseToPairs;", "map": {"version": 3, "names": ["arrayMap", "baseToPairs", "object", "props", "key"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_baseToPairs.js"], "sourcesContent": ["import arrayMap from './_arrayMap.js';\n\n/**\n * The base implementation of `_.toPairs` and `_.toPairsIn` which creates an array\n * of key-value pairs for `object` corresponding to the property names of `props`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} props The property names to get values for.\n * @returns {Object} Returns the key-value pairs.\n */\nfunction baseToPairs(object, props) {\n  return arrayMap(props, function(key) {\n    return [key, object[key]];\n  });\n}\n\nexport default baseToPairs;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAClC,OAAOH,QAAQ,CAACG,KAAK,EAAE,UAASC,GAAG,EAAE;IACnC,OAAO,CAACA,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;EAC3B,CAAC,CAAC;AACJ;AAEA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}