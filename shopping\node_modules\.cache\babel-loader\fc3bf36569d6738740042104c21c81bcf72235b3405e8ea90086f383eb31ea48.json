{"ast": null, "code": "/** Used to match template delimiters. */\nvar reEvaluate = /<%([\\s\\S]+?)%>/g;\nexport default reEvaluate;", "map": {"version": 3, "names": ["reEvaluate"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_reEvaluate.js"], "sourcesContent": ["/** Used to match template delimiters. */\nvar reEvaluate = /<%([\\s\\S]+?)%>/g;\n\nexport default reEvaluate;\n"], "mappings": "AAAA;AACA,IAAIA,UAAU,GAAG,iBAAiB;AAElC,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}