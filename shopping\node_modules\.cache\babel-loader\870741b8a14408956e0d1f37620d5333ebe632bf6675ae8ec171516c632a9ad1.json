{"ast": null, "code": "import getTag from './_getTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar weakMapTag = '[object WeakMap]';\n\n/**\n * Checks if `value` is classified as a `WeakMap` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a weak map, else `false`.\n * @example\n *\n * _.isWeakMap(new WeakMap);\n * // => true\n *\n * _.isWeakMap(new Map);\n * // => false\n */\nfunction isWeakMap(value) {\n  return isObjectLike(value) && getTag(value) == weakMapTag;\n}\nexport default isWeakMap;", "map": {"version": 3, "names": ["getTag", "isObjectLike", "weakMapTag", "isWeakMap", "value"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/isWeakMap.js"], "sourcesContent": ["import getTag from './_getTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar weakMapTag = '[object WeakMap]';\n\n/**\n * Checks if `value` is classified as a `WeakMap` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a weak map, else `false`.\n * @example\n *\n * _.isWeakMap(new WeakMap);\n * // => true\n *\n * _.isWeakMap(new Map);\n * // => false\n */\nfunction isWeakMap(value) {\n  return isObjectLike(value) && getTag(value) == weakMapTag;\n}\n\nexport default isWeakMap;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,YAAY,MAAM,mBAAmB;;AAE5C;AACA,IAAIC,UAAU,GAAG,kBAAkB;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,OAAOH,YAAY,CAACG,KAAK,CAAC,IAAIJ,MAAM,CAACI,KAAK,CAAC,IAAIF,UAAU;AAC3D;AAEA,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}