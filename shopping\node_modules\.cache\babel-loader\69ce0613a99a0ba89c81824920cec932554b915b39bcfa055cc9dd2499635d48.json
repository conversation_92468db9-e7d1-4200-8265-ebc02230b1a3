{"ast": null, "code": "import baseSortedIndexBy from './_baseSortedIndexBy.js';\nimport identity from './identity.js';\nimport isSymbol from './isSymbol.js';\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295,\n  HALF_MAX_ARRAY_LENGTH = MAX_ARRAY_LENGTH >>> 1;\n\n/**\n * The base implementation of `_.sortedIndex` and `_.sortedLastIndex` which\n * performs a binary search of `array` to determine the index at which `value`\n * should be inserted into `array` in order to maintain its sort order.\n *\n * @private\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @param {boolean} [retHighest] Specify returning the highest qualified index.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n */\nfunction baseSortedIndex(array, value, retHighest) {\n  var low = 0,\n    high = array == null ? low : array.length;\n  if (typeof value == 'number' && value === value && high <= HALF_MAX_ARRAY_LENGTH) {\n    while (low < high) {\n      var mid = low + high >>> 1,\n        computed = array[mid];\n      if (computed !== null && !isSymbol(computed) && (retHighest ? computed <= value : computed < value)) {\n        low = mid + 1;\n      } else {\n        high = mid;\n      }\n    }\n    return high;\n  }\n  return baseSortedIndexBy(array, value, identity, retHighest);\n}\nexport default baseSortedIndex;", "map": {"version": 3, "names": ["baseSortedIndexBy", "identity", "isSymbol", "MAX_ARRAY_LENGTH", "HALF_MAX_ARRAY_LENGTH", "baseSortedIndex", "array", "value", "retHighest", "low", "high", "length", "mid", "computed"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_baseSortedIndex.js"], "sourcesContent": ["import baseSortedIndexBy from './_baseSortedIndexBy.js';\nimport identity from './identity.js';\nimport isSymbol from './isSymbol.js';\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295,\n    HALF_MAX_ARRAY_LENGTH = MAX_ARRAY_LENGTH >>> 1;\n\n/**\n * The base implementation of `_.sortedIndex` and `_.sortedLastIndex` which\n * performs a binary search of `array` to determine the index at which `value`\n * should be inserted into `array` in order to maintain its sort order.\n *\n * @private\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @param {boolean} [retHighest] Specify returning the highest qualified index.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n */\nfunction baseSortedIndex(array, value, retHighest) {\n  var low = 0,\n      high = array == null ? low : array.length;\n\n  if (typeof value == 'number' && value === value && high <= HALF_MAX_ARRAY_LENGTH) {\n    while (low < high) {\n      var mid = (low + high) >>> 1,\n          computed = array[mid];\n\n      if (computed !== null && !isSymbol(computed) &&\n          (retHighest ? (computed <= value) : (computed < value))) {\n        low = mid + 1;\n      } else {\n        high = mid;\n      }\n    }\n    return high;\n  }\n  return baseSortedIndexBy(array, value, identity, retHighest);\n}\n\nexport default baseSortedIndex;\n"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,gBAAgB,GAAG,UAAU;EAC7BC,qBAAqB,GAAGD,gBAAgB,KAAK,CAAC;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,eAAeA,CAACC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAE;EACjD,IAAIC,GAAG,GAAG,CAAC;IACPC,IAAI,GAAGJ,KAAK,IAAI,IAAI,GAAGG,GAAG,GAAGH,KAAK,CAACK,MAAM;EAE7C,IAAI,OAAOJ,KAAK,IAAI,QAAQ,IAAIA,KAAK,KAAKA,KAAK,IAAIG,IAAI,IAAIN,qBAAqB,EAAE;IAChF,OAAOK,GAAG,GAAGC,IAAI,EAAE;MACjB,IAAIE,GAAG,GAAIH,GAAG,GAAGC,IAAI,KAAM,CAAC;QACxBG,QAAQ,GAAGP,KAAK,CAACM,GAAG,CAAC;MAEzB,IAAIC,QAAQ,KAAK,IAAI,IAAI,CAACX,QAAQ,CAACW,QAAQ,CAAC,KACvCL,UAAU,GAAIK,QAAQ,IAAIN,KAAK,GAAKM,QAAQ,GAAGN,KAAM,CAAC,EAAE;QAC3DE,GAAG,GAAGG,GAAG,GAAG,CAAC;MACf,CAAC,MAAM;QACLF,IAAI,GAAGE,GAAG;MACZ;IACF;IACA,OAAOF,IAAI;EACb;EACA,OAAOV,iBAAiB,CAACM,KAAK,EAAEC,KAAK,EAAEN,QAAQ,EAAEO,UAAU,CAAC;AAC9D;AAEA,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}