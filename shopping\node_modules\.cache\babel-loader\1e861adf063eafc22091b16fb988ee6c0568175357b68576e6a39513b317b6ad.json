{"ast": null, "code": "import baseGet from './_baseGet.js';\n\n/**\n * A specialized version of `baseProperty` which supports deep paths.\n *\n * @private\n * @param {Array|string} path The path of the property to get.\n * @returns {Function} Returns the new accessor function.\n */\nfunction basePropertyDeep(path) {\n  return function (object) {\n    return baseGet(object, path);\n  };\n}\nexport default basePropertyDeep;", "map": {"version": 3, "names": ["baseGet", "basePropertyDeep", "path", "object"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_basePropertyDeep.js"], "sourcesContent": ["import baseGet from './_baseGet.js';\n\n/**\n * A specialized version of `baseProperty` which supports deep paths.\n *\n * @private\n * @param {Array|string} path The path of the property to get.\n * @returns {Function} Returns the new accessor function.\n */\nfunction basePropertyDeep(path) {\n  return function(object) {\n    return baseGet(object, path);\n  };\n}\n\nexport default basePropertyDeep;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,OAAO,UAASC,MAAM,EAAE;IACtB,OAAOH,OAAO,CAACG,MAAM,EAAED,IAAI,CAAC;EAC9B,CAAC;AACH;AAEA,eAAeD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}