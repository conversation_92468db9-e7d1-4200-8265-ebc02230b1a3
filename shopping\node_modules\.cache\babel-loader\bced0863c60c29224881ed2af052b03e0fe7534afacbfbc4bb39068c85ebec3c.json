{"ast": null, "code": "import isInteger from './isInteger.js';\n\n/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/**\n * Checks if `value` is a safe integer. An integer is safe if it's an IEEE-754\n * double precision number which isn't the result of a rounded unsafe integer.\n *\n * **Note:** This method is based on\n * [`Number.isSafeInteger`](https://mdn.io/Number/isSafeInteger).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a safe integer, else `false`.\n * @example\n *\n * _.isSafeInteger(3);\n * // => true\n *\n * _.isSafeInteger(Number.MIN_VALUE);\n * // => false\n *\n * _.isSafeInteger(Infinity);\n * // => false\n *\n * _.isSafeInteger('3');\n * // => false\n */\nfunction isSafeInteger(value) {\n  return isInteger(value) && value >= -MAX_SAFE_INTEGER && value <= MAX_SAFE_INTEGER;\n}\nexport default isSafeInteger;", "map": {"version": 3, "names": ["isInteger", "MAX_SAFE_INTEGER", "isSafeInteger", "value"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/isSafeInteger.js"], "sourcesContent": ["import isInteger from './isInteger.js';\n\n/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/**\n * Checks if `value` is a safe integer. An integer is safe if it's an IEEE-754\n * double precision number which isn't the result of a rounded unsafe integer.\n *\n * **Note:** This method is based on\n * [`Number.isSafeInteger`](https://mdn.io/Number/isSafeInteger).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a safe integer, else `false`.\n * @example\n *\n * _.isSafeInteger(3);\n * // => true\n *\n * _.isSafeInteger(Number.MIN_VALUE);\n * // => false\n *\n * _.isSafeInteger(Infinity);\n * // => false\n *\n * _.isSafeInteger('3');\n * // => false\n */\nfunction isSafeInteger(value) {\n  return isInteger(value) && value >= -MAX_SAFE_INTEGER && value <= MAX_SAFE_INTEGER;\n}\n\nexport default isSafeInteger;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;;AAEtC;AACA,IAAIC,gBAAgB,GAAG,gBAAgB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOH,SAAS,CAACG,KAAK,CAAC,IAAIA,KAAK,IAAI,CAACF,gBAAgB,IAAIE,KAAK,IAAIF,gBAAgB;AACpF;AAEA,eAAeC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}