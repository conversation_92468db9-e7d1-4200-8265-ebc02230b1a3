{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport arrayEach from './_arrayEach.js';\nimport arrayPush from './_arrayPush.js';\nimport baseFunctions from './_baseFunctions.js';\nimport copyArray from './_copyArray.js';\nimport isFunction from './isFunction.js';\nimport isObject from './isObject.js';\nimport keys from './keys.js';\n\n/**\n * Adds all own enumerable string keyed function properties of a source\n * object to the destination object. If `object` is a function, then methods\n * are added to its prototype as well.\n *\n * **Note:** Use `_.runInContext` to create a pristine `lodash` function to\n * avoid conflicts caused by modifying the original.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {Function|Object} [object=lodash] The destination object.\n * @param {Object} source The object of functions to add.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.chain=true] Specify whether mixins are chainable.\n * @returns {Function|Object} Returns `object`.\n * @example\n *\n * function vowels(string) {\n *   return _.filter(string, function(v) {\n *     return /[aeiou]/i.test(v);\n *   });\n * }\n *\n * _.mixin({ 'vowels': vowels });\n * _.vowels('fred');\n * // => ['e']\n *\n * _('fred').vowels().value();\n * // => ['e']\n *\n * _.mixin({ 'vowels': vowels }, { 'chain': false });\n * _('fred').vowels();\n * // => ['e']\n */\nfunction mixin(object, source, options) {\n  var props = keys(source),\n    methodNames = baseFunctions(source, props);\n  var chain = !(isObject(options) && 'chain' in options) || !!options.chain,\n    isFunc = isFunction(object);\n  arrayEach(methodNames, function (methodName) {\n    var func = source[methodName];\n    object[methodName] = func;\n    if (isFunc) {\n      object.prototype[methodName] = function () {\n        var chainAll = this.__chain__;\n        if (chain || chainAll) {\n          var result = object(this.__wrapped__),\n            actions = result.__actions__ = copyArray(this.__actions__);\n          actions.push({\n            'func': func,\n            'args': arguments,\n            'thisArg': object\n          });\n          result.__chain__ = chainAll;\n          return result;\n        }\n        return func.apply(object, arrayPush([this.value()], arguments));\n      };\n    }\n  });\n  return object;\n}\nexport default mixin;", "map": {"version": 3, "names": ["arrayEach", "arrayPush", "baseFunctions", "copyArray", "isFunction", "isObject", "keys", "mixin", "object", "source", "options", "props", "methodNames", "chain", "isFunc", "methodName", "func", "prototype", "chainAll", "__chain__", "result", "__wrapped__", "actions", "__actions__", "push", "arguments", "apply", "value"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/mixin.js"], "sourcesContent": ["import arrayEach from './_arrayEach.js';\nimport arrayPush from './_arrayPush.js';\nimport baseFunctions from './_baseFunctions.js';\nimport copyArray from './_copyArray.js';\nimport isFunction from './isFunction.js';\nimport isObject from './isObject.js';\nimport keys from './keys.js';\n\n/**\n * Adds all own enumerable string keyed function properties of a source\n * object to the destination object. If `object` is a function, then methods\n * are added to its prototype as well.\n *\n * **Note:** Use `_.runInContext` to create a pristine `lodash` function to\n * avoid conflicts caused by modifying the original.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {Function|Object} [object=lodash] The destination object.\n * @param {Object} source The object of functions to add.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.chain=true] Specify whether mixins are chainable.\n * @returns {Function|Object} Returns `object`.\n * @example\n *\n * function vowels(string) {\n *   return _.filter(string, function(v) {\n *     return /[aeiou]/i.test(v);\n *   });\n * }\n *\n * _.mixin({ 'vowels': vowels });\n * _.vowels('fred');\n * // => ['e']\n *\n * _('fred').vowels().value();\n * // => ['e']\n *\n * _.mixin({ 'vowels': vowels }, { 'chain': false });\n * _('fred').vowels();\n * // => ['e']\n */\nfunction mixin(object, source, options) {\n  var props = keys(source),\n      methodNames = baseFunctions(source, props);\n\n  var chain = !(isObject(options) && 'chain' in options) || !!options.chain,\n      isFunc = isFunction(object);\n\n  arrayEach(methodNames, function(methodName) {\n    var func = source[methodName];\n    object[methodName] = func;\n    if (isFunc) {\n      object.prototype[methodName] = function() {\n        var chainAll = this.__chain__;\n        if (chain || chainAll) {\n          var result = object(this.__wrapped__),\n              actions = result.__actions__ = copyArray(this.__actions__);\n\n          actions.push({ 'func': func, 'args': arguments, 'thisArg': object });\n          result.__chain__ = chainAll;\n          return result;\n        }\n        return func.apply(object, arrayPush([this.value()], arguments));\n      };\n    }\n  });\n\n  return object;\n}\n\nexport default mixin;\n"], "mappings": ";AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACtC,IAAIC,KAAK,GAAGL,IAAI,CAACG,MAAM,CAAC;IACpBG,WAAW,GAAGV,aAAa,CAACO,MAAM,EAAEE,KAAK,CAAC;EAE9C,IAAIE,KAAK,GAAG,EAAER,QAAQ,CAACK,OAAO,CAAC,IAAI,OAAO,IAAIA,OAAO,CAAC,IAAI,CAAC,CAACA,OAAO,CAACG,KAAK;IACrEC,MAAM,GAAGV,UAAU,CAACI,MAAM,CAAC;EAE/BR,SAAS,CAACY,WAAW,EAAE,UAASG,UAAU,EAAE;IAC1C,IAAIC,IAAI,GAAGP,MAAM,CAACM,UAAU,CAAC;IAC7BP,MAAM,CAACO,UAAU,CAAC,GAAGC,IAAI;IACzB,IAAIF,MAAM,EAAE;MACVN,MAAM,CAACS,SAAS,CAACF,UAAU,CAAC,GAAG,YAAW;QACxC,IAAIG,QAAQ,GAAG,IAAI,CAACC,SAAS;QAC7B,IAAIN,KAAK,IAAIK,QAAQ,EAAE;UACrB,IAAIE,MAAM,GAAGZ,MAAM,CAAC,IAAI,CAACa,WAAW,CAAC;YACjCC,OAAO,GAAGF,MAAM,CAACG,WAAW,GAAGpB,SAAS,CAAC,IAAI,CAACoB,WAAW,CAAC;UAE9DD,OAAO,CAACE,IAAI,CAAC;YAAE,MAAM,EAAER,IAAI;YAAE,MAAM,EAAES,SAAS;YAAE,SAAS,EAAEjB;UAAO,CAAC,CAAC;UACpEY,MAAM,CAACD,SAAS,GAAGD,QAAQ;UAC3B,OAAOE,MAAM;QACf;QACA,OAAOJ,IAAI,CAACU,KAAK,CAAClB,MAAM,EAAEP,SAAS,CAAC,CAAC,IAAI,CAAC0B,KAAK,CAAC,CAAC,CAAC,EAAEF,SAAS,CAAC,CAAC;MACjE,CAAC;IACH;EACF,CAAC,CAAC;EAEF,OAAOjB,MAAM;AACf;AAEA,eAAeD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}