<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>🛒 购物商城</h2>
        <p>欢迎登录</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @keyup.enter="handleLogin"
      >
        <!-- 用户名输入框 -->
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
            size="large"
          />
        </el-form-item>
        
        <!-- 密码输入框 -->
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            size="large"
          />
        </el-form-item>
        

        
        <!-- 记住密码和忘记密码 -->
        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="rememberMe">记住密码</el-checkbox>
            <el-link type="primary" @click="handleForgetPassword">忘记密码？</el-link>
          </div>
        </el-form-item>
        
        <!-- 登录按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
            block
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>

        <!-- 注册链接 -->
        <el-form-item>
          <div class="register-link">
            还没有账号？
            <el-link type="primary" @click="showRegisterDialog">立即注册</el-link>
          </div>
        </el-form-item>

      </el-form>
    </div>

    <!-- 注册对话框 -->
    <el-dialog
      title="用户注册"
      v-model="registerDialogVisible"
      width="500px"
    >
      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="registerForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="registerForm.password" type="password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="registerForm.confirmPassword" type="password" placeholder="请再次输入密码" />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="registerForm.nickname" placeholder="请输入昵称" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="registerForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="registerForm.phone" placeholder="请输入电话号码" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="registerForm.address" placeholder="请输入地址" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="registerDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleRegister" :loading="registerLoading">
            {{ registerLoading ? '注册中...' : '注册' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'

// 表单引用
const loginFormRef = ref()
const registerFormRef = ref()

// 路由实例
const router = useRouter()

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 注册表单数据
const registerForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  nickname: '',
  email: '',
  phone: '',
  address: ''
})

// 记住密码
const rememberMe = ref(false)

// 加载状态
const loading = ref(false)
const registerLoading = ref(false)

// 注册对话框显示状态
const registerDialogVisible = ref(false)

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度为2-20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

// 注册表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}



// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      // 调用后端登录接口
      loading.value = true

      try {
        const response = await fetch('http://localhost:9192/userAPI/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username: loginForm.username,
            password: loginForm.password
          })
        })

        const result = await response.json()

        if (result.code === '200') {
          const { user, token } = result.data

          ElNotification({
            title: '登录成功',
            message: `欢迎回来，${user.username}！`,
            type: 'success',
            duration: 2000
          })

          // 保存登录状态和用户信息
          localStorage.setItem('isLoggedIn', 'true')
          localStorage.setItem('username', user.username)
          localStorage.setItem('userRole', user.role)
          localStorage.setItem('token', token)
          localStorage.setItem('userId', user.id)

          // 根据用户角色跳转
          if (user.role === 'admin') {
            router.push('/admin')
          } else {
            router.push('/')
          }
        } else {
          ElMessage.error(result.msg || '登录失败')
        }
      } catch (error) {
        console.error('登录请求失败:', error)
        ElMessage.error('网络错误，请稍后重试')
      } finally {
        loading.value = false
      }
    } else {
      ElMessage.error('请填写正确的登录信息')
      return false
    }
  })
}

// 忘记密码
const handleForgetPassword = () => {
  ElMessage.info('忘记密码功能开发中...')
}

// 显示注册对话框
const showRegisterDialog = () => {
  registerDialogVisible.value = true
  // 重置注册表单
  registerForm.username = ''
  registerForm.password = ''
  registerForm.confirmPassword = ''
  registerForm.nickname = ''
  registerForm.email = ''
  registerForm.phone = ''
  registerForm.address = ''
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return

  await registerFormRef.value.validate(async (valid) => {
    if (valid) {
      registerLoading.value = true

      try {
        const response = await fetch('http://localhost:9192/userAPI/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username: registerForm.username,
            password: registerForm.password,
            nickname: registerForm.nickname,
            email: registerForm.email,
            phone: registerForm.phone,
            address: registerForm.address,
            role: 'user' // 默认为普通用户
          })
        })

        const result = await response.json()

        if (result.code === '200') {
          ElNotification({
            title: '注册成功',
            message: '账号注册成功，请使用新账号登录！',
            type: 'success',
            duration: 3000
          })

          registerDialogVisible.value = false

          // 将注册的用户名填入登录表单
          loginForm.username = registerForm.username
        } else {
          ElMessage.error(result.msg || '注册失败')
        }
      } catch (error) {
        console.error('注册请求失败:', error)
        ElMessage.error('网络错误，请稍后重试')
      } finally {
        registerLoading.value = false
      }
    } else {
      ElMessage.error('请填写正确的注册信息')
      return false
    }
  })
}

// 组件挂载时的初始化
onMounted(() => {
  // 检查是否记住密码
  const savedUsername = localStorage.getItem('savedUsername')
  if (savedUsername) {
    loginForm.username = savedUsername
    rememberMe.value = true
  }
})
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-box {
  width: 100%;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 40px 30px;
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
}

.login-header p {
  color: #666;
  font-size: 14px;
}

.login-form {
  width: 100%;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.login-button {
  margin-top: 10px;
}

.register-link {
  text-align: center;
  color: #666;
  font-size: 14px;
  margin-top: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
}

:deep(.el-button) {
  border-radius: 20px;
}

@media (max-width: 480px) {
  .login-box {
    padding: 30px 20px;
    margin: 10px;
  }
  

}
</style>