{"ast": null, "code": "import baseEach from './_baseEach.js';\n\n/**\n * Aggregates elements of `collection` on `accumulator` with keys transformed\n * by `iteratee` and values set by `setter`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform keys.\n * @param {Object} accumulator The initial aggregated object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction baseAggregator(collection, setter, iteratee, accumulator) {\n  baseEach(collection, function (value, key, collection) {\n    setter(accumulator, value, iteratee(value), collection);\n  });\n  return accumulator;\n}\nexport default baseAggregator;", "map": {"version": 3, "names": ["baseEach", "baseAggregator", "collection", "setter", "iteratee", "accumulator", "value", "key"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_baseAggregator.js"], "sourcesContent": ["import baseEach from './_baseEach.js';\n\n/**\n * Aggregates elements of `collection` on `accumulator` with keys transformed\n * by `iteratee` and values set by `setter`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform keys.\n * @param {Object} accumulator The initial aggregated object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction baseAggregator(collection, setter, iteratee, accumulator) {\n  baseEach(collection, function(value, key, collection) {\n    setter(accumulator, value, iteratee(value), collection);\n  });\n  return accumulator;\n}\n\nexport default baseAggregator;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EACjEL,QAAQ,CAACE,UAAU,EAAE,UAASI,KAAK,EAAEC,GAAG,EAAEL,UAAU,EAAE;IACpDC,MAAM,CAACE,WAAW,EAAEC,KAAK,EAAEF,QAAQ,CAACE,KAAK,CAAC,EAAEJ,UAAU,CAAC;EACzD,CAAC,CAAC;EACF,OAAOG,WAAW;AACpB;AAEA,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}