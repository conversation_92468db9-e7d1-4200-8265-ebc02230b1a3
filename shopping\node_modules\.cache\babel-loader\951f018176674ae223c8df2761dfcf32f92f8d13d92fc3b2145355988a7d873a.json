{"ast": null, "code": "import Symbol from './_Symbol.js';\nimport getRawTag from './_getRawTag.js';\nimport objectToString from './_objectToString.js';\n\n/** `Object#toString` result references. */\nvar nullTag = '[object Null]',\n  undefinedTag = '[object Undefined]';\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return symToStringTag && symToStringTag in Object(value) ? getRawTag(value) : objectToString(value);\n}\nexport default baseGetTag;", "map": {"version": 3, "names": ["Symbol", "getRawTag", "objectToString", "nullTag", "undefinedTag", "symToStringTag", "toStringTag", "undefined", "baseGetTag", "value", "Object"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_baseGetTag.js"], "sourcesContent": ["import Symbol from './_Symbol.js';\nimport getRawTag from './_getRawTag.js';\nimport objectToString from './_objectToString.js';\n\n/** `Object#toString` result references. */\nvar nullTag = '[object Null]',\n    undefinedTag = '[object Undefined]';\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return (symToStringTag && symToStringTag in Object(value))\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\nexport default baseGetTag;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,cAAc,MAAM,sBAAsB;;AAEjD;AACA,IAAIC,OAAO,GAAG,eAAe;EACzBC,YAAY,GAAG,oBAAoB;;AAEvC;AACA,IAAIC,cAAc,GAAGL,MAAM,GAAGA,MAAM,CAACM,WAAW,GAAGC,SAAS;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAIA,KAAK,IAAI,IAAI,EAAE;IACjB,OAAOA,KAAK,KAAKF,SAAS,GAAGH,YAAY,GAAGD,OAAO;EACrD;EACA,OAAQE,cAAc,IAAIA,cAAc,IAAIK,MAAM,CAACD,KAAK,CAAC,GACrDR,SAAS,CAACQ,KAAK,CAAC,GAChBP,cAAc,CAACO,KAAK,CAAC;AAC3B;AAEA,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}