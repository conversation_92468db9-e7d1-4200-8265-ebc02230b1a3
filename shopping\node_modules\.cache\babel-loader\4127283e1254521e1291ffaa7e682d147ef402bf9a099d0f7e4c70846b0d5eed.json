{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, with<PERSON><PERSON><PERSON> as _with<PERSON>ey<PERSON>, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"login-container\"\n};\nconst _hoisted_2 = {\n  class: \"login-box\"\n};\nconst _hoisted_3 = {\n  class: \"captcha-wrapper\"\n};\nconst _hoisted_4 = {\n  class: \"login-options\"\n};\nconst _hoisted_5 = {\n  class: \"other-login\"\n};\nconst _hoisted_6 = {\n  class: \"register-link\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_el_link = _resolveComponent(\"el-link\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_divider = _resolveComponent(\"el-divider\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n    class: \"login-header\"\n  }, [_createElementVNode(\"h2\", null, \"🛒 购物商城\"), _createElementVNode(\"p\", null, \"欢迎登录\")], -1 /* CACHED */)), _createVNode(_component_el_form, {\n    ref: \"loginFormRef\",\n    model: $setup.loginForm,\n    rules: $setup.loginRules,\n    class: \"login-form\",\n    onKeyup: _withKeys($setup.handleLogin, [\"enter\"])\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 用户名输入框 \"), _createVNode(_component_el_form_item, {\n      prop: \"username\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.loginForm.username,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.loginForm.username = $event),\n        placeholder: \"请输入用户名\",\n        \"prefix-icon\": \"User\",\n        clearable: \"\",\n        size: \"large\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 密码输入框 \"), _createVNode(_component_el_form_item, {\n      prop: \"password\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.loginForm.password,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.loginForm.password = $event),\n        type: \"password\",\n        placeholder: \"请输入密码\",\n        \"prefix-icon\": \"Lock\",\n        \"show-password\": \"\",\n        size: \"large\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 验证码 \"), _createVNode(_component_el_form_item, {\n      prop: \"captcha\",\n      class: \"captcha-item\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_input, {\n        modelValue: $setup.loginForm.captcha,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.loginForm.captcha = $event),\n        placeholder: \"请输入验证码\",\n        \"prefix-icon\": \"Key\",\n        size: \"large\",\n        class: \"captcha-input\"\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", {\n        class: \"captcha-image\",\n        onClick: $setup.refreshCaptcha\n      }, _toDisplayString($setup.captchaText), 1 /* TEXT */)])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 记住密码和忘记密码 \"), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_checkbox, {\n        modelValue: $setup.rememberMe,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.rememberMe = $event)\n      }, {\n        default: _withCtx(() => [...(_cache[4] || (_cache[4] = [_createTextVNode(\"记住密码\", -1 /* CACHED */)]))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_link, {\n        type: \"primary\",\n        onClick: $setup.handleForgetPassword\n      }, {\n        default: _withCtx(() => [...(_cache[5] || (_cache[5] = [_createTextVNode(\"忘记密码？\", -1 /* CACHED */)]))]),\n        _: 1 /* STABLE */\n      })])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 登录按钮 \"), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        size: \"large\",\n        class: \"login-button\",\n        loading: $setup.loading,\n        onClick: $setup.handleLogin,\n        block: \"\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.loading ? '登录中...' : '登录'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"loading\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 其他登录方式 \"), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_cache[8] || (_cache[8] = _createElementVNode(\"span\", null, \"其他登录方式：\", -1 /* CACHED */)), _createVNode(_component_el_link, {\n        type: \"primary\",\n        onClick: $setup.handleWechatLogin\n      }, {\n        default: _withCtx(() => [...(_cache[6] || (_cache[6] = [_createTextVNode(\"微信登录\", -1 /* CACHED */)]))]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_divider, {\n        direction: \"vertical\"\n      }), _createVNode(_component_el_link, {\n        type: \"primary\",\n        onClick: $setup.handleQQLogin\n      }, {\n        default: _withCtx(() => [...(_cache[7] || (_cache[7] = [_createTextVNode(\"QQ登录\", -1 /* CACHED */)]))]),\n        _: 1 /* STABLE */\n      })])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 注册链接 \"), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_cache[10] || (_cache[10] = _createTextVNode(\" 还没有账号？ \", -1 /* CACHED */)), _createVNode(_component_el_link, {\n        type: \"primary\",\n        onClick: $setup.handleRegister\n      }, {\n        default: _withCtx(() => [...(_cache[9] || (_cache[9] = [_createTextVNode(\"立即注册\", -1 /* CACHED */)]))]),\n        _: 1 /* STABLE */\n      })])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\"])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_form", "ref", "model", "$setup", "loginForm", "rules", "loginRules", "onKeyup", "_with<PERSON><PERSON><PERSON>", "handleLogin", "_createCommentVNode", "_component_el_form_item", "prop", "_component_el_input", "username", "$event", "placeholder", "clearable", "size", "password", "type", "_hoisted_3", "<PERSON><PERSON>a", "onClick", "refreshCaptcha", "captchaText", "_hoisted_4", "_component_el_checkbox", "rememberMe", "_cache", "_component_el_link", "handleForgetPassword", "_component_el_button", "loading", "block", "_hoisted_5", "handleWechatLogin", "_component_el_divider", "direction", "handleQQLogin", "_hoisted_6", "handleRegister"], "sources": ["D:\\2025_down\\project\\shoppingOnline-20250826-sfl\\shoppingOnline-20250826\\shopping\\src\\components\\login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-box\">\n      <div class=\"login-header\">\n        <h2>🛒 购物商城</h2>\n        <p>欢迎登录</p>\n      </div>\n      \n      <el-form\n        ref=\"loginFormRef\"\n        :model=\"loginForm\"\n        :rules=\"loginRules\"\n        class=\"login-form\"\n        @keyup.enter=\"handleLogin\"\n      >\n        <!-- 用户名输入框 -->\n        <el-form-item prop=\"username\">\n          <el-input\n            v-model=\"loginForm.username\"\n            placeholder=\"请输入用户名\"\n            prefix-icon=\"User\"\n            clearable\n            size=\"large\"\n          />\n        </el-form-item>\n        \n        <!-- 密码输入框 -->\n        <el-form-item prop=\"password\">\n          <el-input\n            v-model=\"loginForm.password\"\n            type=\"password\"\n            placeholder=\"请输入密码\"\n            prefix-icon=\"Lock\"\n            show-password\n            size=\"large\"\n          />\n        </el-form-item>\n        \n        <!-- 验证码 -->\n        <el-form-item prop=\"captcha\" class=\"captcha-item\">\n          <div class=\"captcha-wrapper\">\n            <el-input\n              v-model=\"loginForm.captcha\"\n              placeholder=\"请输入验证码\"\n              prefix-icon=\"Key\"\n              size=\"large\"\n              class=\"captcha-input\"\n            />\n            <div class=\"captcha-image\" @click=\"refreshCaptcha\">\n              {{ captchaText }}\n            </div>\n          </div>\n        </el-form-item>\n        \n        <!-- 记住密码和忘记密码 -->\n        <el-form-item>\n          <div class=\"login-options\">\n            <el-checkbox v-model=\"rememberMe\">记住密码</el-checkbox>\n            <el-link type=\"primary\" @click=\"handleForgetPassword\">忘记密码？</el-link>\n          </div>\n        </el-form-item>\n        \n        <!-- 登录按钮 -->\n        <el-form-item>\n          <el-button\n            type=\"primary\"\n            size=\"large\"\n            class=\"login-button\"\n            :loading=\"loading\"\n            @click=\"handleLogin\"\n            block\n          >\n            {{ loading ? '登录中...' : '登录' }}\n          </el-button>\n        </el-form-item>\n        \n        <!-- 其他登录方式 -->\n        <el-form-item>\n          <div class=\"other-login\">\n            <span>其他登录方式：</span>\n            <el-link type=\"primary\" @click=\"handleWechatLogin\">微信登录</el-link>\n            <el-divider direction=\"vertical\"></el-divider>\n            <el-link type=\"primary\" @click=\"handleQQLogin\">QQ登录</el-link>\n          </div>\n        </el-form-item>\n        \n        <!-- 注册链接 -->\n        <el-form-item>\n          <div class=\"register-link\">\n            还没有账号？\n            <el-link type=\"primary\" @click=\"handleRegister\">立即注册</el-link>\n          </div>\n        </el-form-item>\n      </el-form>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage, ElNotification } from 'element-plus'\n\n// 表单引用\nconst loginFormRef = ref()\n\n// 路由实例\nconst router = useRouter()\n\n// 登录表单数据\nconst loginForm = reactive({\n  username: '',\n  password: '',\n  captcha: ''\n})\n\n// 记住密码\nconst rememberMe = ref(false)\n\n// 加载状态\nconst loading = ref(false)\n\n// 验证码文本\nconst captchaText = ref('')\n\n// 表单验证规则\nconst loginRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' },\n    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }\n  ],\n  captcha: [\n    { required: true, message: '请输入验证码', trigger: 'blur' }\n  ]\n}\n\n// 生成随机验证码\nconst generateCaptcha = () => {\n  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789'\n  let result = ''\n  for (let i = 0; i < 4; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length))\n  }\n  captchaText.value = result\n  loginForm.captcha = ''\n}\n\n// 刷新验证码\nconst refreshCaptcha = () => {\n  generateCaptcha()\n}\n\n// 处理登录\nconst handleLogin = async () => {\n  if (!loginFormRef.value) return\n  \n  await loginFormRef.value.validate((valid) => {\n    if (valid) {\n      // 验证验证码\n      if (loginForm.captcha.toLowerCase() !== captchaText.value.toLowerCase()) {\n        ElMessage.error('验证码错误')\n        refreshCaptcha()\n        return\n      }\n      \n      // 模拟登录过程\n      loading.value = true\n      \n      setTimeout(() => {\n        loading.value = false\n        \n        // 模拟登录成功\n        if (loginForm.username === 'admin' && loginForm.password === '123456') {\n          ElNotification({\n            title: '登录成功',\n            message: `欢迎回来，${loginForm.username}！`,\n            type: 'success',\n            duration: 2000\n          })\n          \n          // 保存登录状态（实际项目中应该保存token）\n          localStorage.setItem('isLoggedIn', 'true')\n          localStorage.setItem('username', loginForm.username)\n          \n          // 跳转到首页\n          router.push('/')\n        } else {\n          ElMessage.error('用户名或密码错误')\n          refreshCaptcha()\n        }\n      }, 1500)\n    } else {\n      ElMessage.error('请填写正确的登录信息')\n      return false\n    }\n  })\n}\n\n// 忘记密码\nconst handleForgetPassword = () => {\n  ElMessage.info('忘记密码功能开发中...')\n}\n\n// 微信登录\nconst handleWechatLogin = () => {\n  ElMessage.info('微信登录功能开发中...')\n}\n\n// QQ登录\nconst handleQQLogin = () => {\n  ElMessage.info('QQ登录功能开发中...')\n}\n\n// 注册\nconst handleRegister = () => {\n  ElMessage.info('注册功能开发中...')\n  // router.push('/register')\n}\n\n// 组件挂载时生成验证码\nonMounted(() => {\n  generateCaptcha()\n  \n  // 检查是否记住密码\n  const savedUsername = localStorage.getItem('savedUsername')\n  if (savedUsername) {\n    loginForm.username = savedUsername\n    rememberMe.value = true\n  }\n})\n</script>\n\n<style scoped>\n.login-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n}\n\n.login-box {\n  width: 100%;\n  max-width: 400px;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 10px;\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  padding: 40px 30px;\n  backdrop-filter: blur(10px);\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.login-header h2 {\n  color: #333;\n  margin-bottom: 10px;\n  font-size: 24px;\n}\n\n.login-header p {\n  color: #666;\n  font-size: 14px;\n}\n\n.login-form {\n  width: 100%;\n}\n\n.captcha-item :deep(.el-form-item__content) {\n  display: block;\n}\n\n.captcha-wrapper {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.captcha-input {\n  flex: 1;\n}\n\n.captcha-image {\n  width: 100px;\n  height: 40px;\n  background: linear-gradient(45deg, #f0f0f0, #e0e0e0);\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 18px;\n  color: #333;\n  cursor: pointer;\n  user-select: none;\n  transition: all 0.3s;\n}\n\n.captcha-image:hover {\n  background: linear-gradient(45deg, #e0e0e0, #d0d0d0);\n}\n\n.login-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.login-button {\n  margin-top: 10px;\n}\n\n.other-login {\n  text-align: center;\n  color: #666;\n  font-size: 14px;\n}\n\n.other-login span {\n  margin-right: 10px;\n}\n\n.register-link {\n  text-align: center;\n  color: #666;\n  font-size: 14px;\n}\n\n:deep(.el-input__wrapper) {\n  border-radius: 20px;\n}\n\n:deep(.el-button) {\n  border-radius: 20px;\n}\n\n@media (max-width: 480px) {\n  .login-box {\n    padding: 30px 20px;\n    margin: 10px;\n  }\n  \n  .captcha-wrapper {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .captcha-image {\n    width: 100%;\n    margin-top: 10px;\n  }\n}\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAW;;EAsCXA,KAAK,EAAC;AAAiB;;EAgBvBA,KAAK,EAAC;AAAe;;EAsBrBA,KAAK,EAAC;AAAa;;EAUnBA,KAAK,EAAC;AAAe;;;;;;;;;uBAvFlCC,mBAAA,CA8FM,OA9FNC,UA8FM,GA7FJC,mBAAA,CA4FM,OA5FNC,UA4FM,G,4BA3FJD,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAc,IACvBG,mBAAA,CAAgB,YAAZ,SAAO,GACXA,mBAAA,CAAW,WAAR,MAAI,E,qBAGTE,YAAA,CAqFUC,kBAAA;IApFRC,GAAG,EAAC,cAAc;IACjBC,KAAK,EAAEC,MAAA,CAAAC,SAAS;IAChBC,KAAK,EAAEF,MAAA,CAAAG,UAAU;IAClBZ,KAAK,EAAC,YAAY;IACjBa,OAAK,EAAAC,SAAA,CAAQL,MAAA,CAAAM,WAAW;;sBAEzB,MAAe,CAAfC,mBAAA,YAAe,EACfX,YAAA,CAQeY,uBAAA;MARDC,IAAI,EAAC;IAAU;wBAC3B,MAME,CANFb,YAAA,CAMEc,mBAAA;oBALSV,MAAA,CAAAC,SAAS,CAACU,QAAQ;mEAAlBX,MAAA,CAAAC,SAAS,CAACU,QAAQ,GAAAC,MAAA;QAC3BC,WAAW,EAAC,QAAQ;QACpB,aAAW,EAAC,MAAM;QAClBC,SAAS,EAAT,EAAS;QACTC,IAAI,EAAC;;;QAITR,mBAAA,WAAc,EACdX,YAAA,CASeY,uBAAA;MATDC,IAAI,EAAC;IAAU;wBAC3B,MAOE,CAPFb,YAAA,CAOEc,mBAAA;oBANSV,MAAA,CAAAC,SAAS,CAACe,QAAQ;mEAAlBhB,MAAA,CAAAC,SAAS,CAACe,QAAQ,GAAAJ,MAAA;QAC3BK,IAAI,EAAC,UAAU;QACfJ,WAAW,EAAC,OAAO;QACnB,aAAW,EAAC,MAAM;QAClB,eAAa,EAAb,EAAa;QACbE,IAAI,EAAC;;;QAITR,mBAAA,SAAY,EACZX,YAAA,CAaeY,uBAAA;MAbDC,IAAI,EAAC,SAAS;MAAClB,KAAK,EAAC;;wBACjC,MAWM,CAXNG,mBAAA,CAWM,OAXNwB,UAWM,GAVJtB,YAAA,CAMEc,mBAAA;oBALSV,MAAA,CAAAC,SAAS,CAACkB,OAAO;mEAAjBnB,MAAA,CAAAC,SAAS,CAACkB,OAAO,GAAAP,MAAA;QAC1BC,WAAW,EAAC,QAAQ;QACpB,aAAW,EAAC,KAAK;QACjBE,IAAI,EAAC,OAAO;QACZxB,KAAK,EAAC;+CAERG,mBAAA,CAEM;QAFDH,KAAK,EAAC,eAAe;QAAE6B,OAAK,EAAEpB,MAAA,CAAAqB;0BAC9BrB,MAAA,CAAAsB,WAAW,iB;;QAKpBf,mBAAA,eAAkB,EAClBX,YAAA,CAKeY,uBAAA;wBAJb,MAGM,CAHNd,mBAAA,CAGM,OAHN6B,UAGM,GAFJ3B,YAAA,CAAoD4B,sBAAA;oBAA9BxB,MAAA,CAAAyB,UAAU;mEAAVzB,MAAA,CAAAyB,UAAU,GAAAb,MAAA;;0BAAE,MAAI,KAAAc,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,mB;;yCACtC9B,YAAA,CAAqE+B,kBAAA;QAA5DV,IAAI,EAAC,SAAS;QAAEG,OAAK,EAAEpB,MAAA,CAAA4B;;0BAAsB,MAAK,KAAAF,MAAA,QAAAA,MAAA,O,iBAAL,OAAK,mB;;;;QAI/DnB,mBAAA,UAAa,EACbX,YAAA,CAWeY,uBAAA;wBAVb,MASY,CATZZ,YAAA,CASYiC,oBAAA;QARVZ,IAAI,EAAC,SAAS;QACdF,IAAI,EAAC,OAAO;QACZxB,KAAK,EAAC,cAAc;QACnBuC,OAAO,EAAE9B,MAAA,CAAA8B,OAAO;QAChBV,OAAK,EAAEpB,MAAA,CAAAM,WAAW;QACnByB,KAAK,EAAL;;0BAEA,MAA+B,C,kCAA5B/B,MAAA,CAAA8B,OAAO,mC;;;;QAIdvB,mBAAA,YAAe,EACfX,YAAA,CAOeY,uBAAA;wBANb,MAKM,CALNd,mBAAA,CAKM,OALNsC,UAKM,G,0BAJJtC,mBAAA,CAAoB,cAAd,SAAO,qBACbE,YAAA,CAAiE+B,kBAAA;QAAxDV,IAAI,EAAC,SAAS;QAAEG,OAAK,EAAEpB,MAAA,CAAAiC;;0BAAmB,MAAI,KAAAP,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,mB;;UACvD9B,YAAA,CAA8CsC,qBAAA;QAAlCC,SAAS,EAAC;MAAU,IAChCvC,YAAA,CAA6D+B,kBAAA;QAApDV,IAAI,EAAC,SAAS;QAAEG,OAAK,EAAEpB,MAAA,CAAAoC;;0BAAe,MAAI,KAAAV,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,mB;;;;QAIvDnB,mBAAA,UAAa,EACbX,YAAA,CAKeY,uBAAA;wBAJb,MAGM,CAHNd,mBAAA,CAGM,OAHN2C,UAGM,G,6CAHqB,UAEzB,qBAAAzC,YAAA,CAA8D+B,kBAAA;QAArDV,IAAI,EAAC,SAAS;QAAEG,OAAK,EAAEpB,MAAA,CAAAsC;;0BAAgB,MAAI,KAAAZ,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}