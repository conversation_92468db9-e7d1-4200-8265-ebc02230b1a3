{"ast": null, "code": "import baseToNumber from './_baseToNumber.js';\nimport baseToString from './_baseToString.js';\n\n/**\n * Creates a function that performs a mathematical operation on two values.\n *\n * @private\n * @param {Function} operator The function to perform the operation.\n * @param {number} [defaultValue] The value used for `undefined` arguments.\n * @returns {Function} Returns the new mathematical operation function.\n */\nfunction createMathOperation(operator, defaultValue) {\n  return function (value, other) {\n    var result;\n    if (value === undefined && other === undefined) {\n      return defaultValue;\n    }\n    if (value !== undefined) {\n      result = value;\n    }\n    if (other !== undefined) {\n      if (result === undefined) {\n        return other;\n      }\n      if (typeof value == 'string' || typeof other == 'string') {\n        value = baseToString(value);\n        other = baseToString(other);\n      } else {\n        value = baseToNumber(value);\n        other = baseToNumber(other);\n      }\n      result = operator(value, other);\n    }\n    return result;\n  };\n}\nexport default createMathOperation;", "map": {"version": 3, "names": ["baseToNumber", "baseToString", "createMathOperation", "operator", "defaultValue", "value", "other", "result", "undefined"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/_createMathOperation.js"], "sourcesContent": ["import baseToNumber from './_baseToNumber.js';\nimport baseToString from './_baseToString.js';\n\n/**\n * Creates a function that performs a mathematical operation on two values.\n *\n * @private\n * @param {Function} operator The function to perform the operation.\n * @param {number} [defaultValue] The value used for `undefined` arguments.\n * @returns {Function} Returns the new mathematical operation function.\n */\nfunction createMathOperation(operator, defaultValue) {\n  return function(value, other) {\n    var result;\n    if (value === undefined && other === undefined) {\n      return defaultValue;\n    }\n    if (value !== undefined) {\n      result = value;\n    }\n    if (other !== undefined) {\n      if (result === undefined) {\n        return other;\n      }\n      if (typeof value == 'string' || typeof other == 'string') {\n        value = baseToString(value);\n        other = baseToString(other);\n      } else {\n        value = baseToNumber(value);\n        other = baseToNumber(other);\n      }\n      result = operator(value, other);\n    }\n    return result;\n  };\n}\n\nexport default createMathOperation;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,YAAY,MAAM,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,QAAQ,EAAEC,YAAY,EAAE;EACnD,OAAO,UAASC,KAAK,EAAEC,KAAK,EAAE;IAC5B,IAAIC,MAAM;IACV,IAAIF,KAAK,KAAKG,SAAS,IAAIF,KAAK,KAAKE,SAAS,EAAE;MAC9C,OAAOJ,YAAY;IACrB;IACA,IAAIC,KAAK,KAAKG,SAAS,EAAE;MACvBD,MAAM,GAAGF,KAAK;IAChB;IACA,IAAIC,KAAK,KAAKE,SAAS,EAAE;MACvB,IAAID,MAAM,KAAKC,SAAS,EAAE;QACxB,OAAOF,KAAK;MACd;MACA,IAAI,OAAOD,KAAK,IAAI,QAAQ,IAAI,OAAOC,KAAK,IAAI,QAAQ,EAAE;QACxDD,KAAK,GAAGJ,YAAY,CAACI,KAAK,CAAC;QAC3BC,KAAK,GAAGL,YAAY,CAACK,KAAK,CAAC;MAC7B,CAAC,MAAM;QACLD,KAAK,GAAGL,YAAY,CAACK,KAAK,CAAC;QAC3BC,KAAK,GAAGN,YAAY,CAACM,KAAK,CAAC;MAC7B;MACAC,MAAM,GAAGJ,QAAQ,CAACE,KAAK,EAAEC,KAAK,CAAC;IACjC;IACA,OAAOC,MAAM;EACf,CAAC;AACH;AAEA,eAAeL,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}