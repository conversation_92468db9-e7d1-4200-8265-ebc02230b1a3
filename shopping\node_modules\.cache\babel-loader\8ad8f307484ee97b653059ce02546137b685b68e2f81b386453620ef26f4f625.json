{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, onMounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage, ElMessageBox, ElNotification } from 'element-plus';\nexport default {\n  __name: 'Admin',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const router = useRouter();\n\n    // 响应式数据\n    const username = ref(localStorage.getItem('username') || '');\n    const users = ref([]);\n    const loading = ref(false);\n    const dialogVisible = ref(false);\n    const dialogTitle = ref('添加用户');\n    const isEdit = ref(false);\n    const saveLoading = ref(false);\n\n    // 表单数据\n    const userForm = ref({\n      id: null,\n      username: '',\n      password: '',\n      nickname: '',\n      email: '',\n      phone: '',\n      address: '',\n      role: 'user'\n    });\n    const userFormRef = ref();\n\n    // 表单验证规则\n    const userRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }, {\n        min: 3,\n        max: 20,\n        message: '用户名长度为3-20个字符',\n        trigger: 'blur'\n      }],\n      password: [{\n        required: true,\n        message: '请输入密码',\n        trigger: 'blur'\n      }, {\n        min: 6,\n        max: 20,\n        message: '密码长度为6-20个字符',\n        trigger: 'blur'\n      }],\n      nickname: [{\n        required: true,\n        message: '请输入昵称',\n        trigger: 'blur'\n      }],\n      email: [{\n        type: 'email',\n        message: '请输入正确的邮箱地址',\n        trigger: 'blur'\n      }],\n      role: [{\n        required: true,\n        message: '请选择角色',\n        trigger: 'change'\n      }]\n    };\n\n    // 获取token\n    const getToken = () => localStorage.getItem('token');\n\n    // 获取用户列表\n    const fetchUsers = async () => {\n      loading.value = true;\n      console.log('开始获取用户列表...');\n      try {\n        // 先尝试简单的用户接口\n        console.log('尝试简单用户接口...');\n        const simpleResponse = await fetch('http://localhost:9192/userAPI/users');\n        console.log('简单接口响应状态:', simpleResponse.status);\n        const simpleText = await simpleResponse.text();\n        console.log('简单接口原始响应:', simpleText);\n        if (simpleText && simpleText.length > 0) {\n          try {\n            const simpleResult = JSON.parse(simpleText);\n            console.log('简单接口解析结果:', simpleResult);\n            if (simpleResult.code === '200') {\n              users.value = simpleResult.data || [];\n              console.log('从简单接口获取用户列表:', users.value);\n              ElMessage.success(`成功加载 ${users.value.length} 个用户（简单接口）`);\n              return;\n            }\n          } catch (jsonError) {\n            console.error('简单接口JSON解析失败:', jsonError);\n          }\n        }\n\n        // 如果简单接口失败，尝试复杂接口\n        console.log('尝试复杂用户接口...');\n        const response = await fetch('http://localhost:9192/userAPI/queryALL', {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n            'token': getToken()\n          }\n        });\n        console.log('复杂接口响应状态:', response.status);\n\n        // 先获取原始文本\n        const responseText = await response.text();\n        console.log('复杂接口原始响应:', responseText);\n        console.log('复杂接口响应长度:', responseText.length);\n        if (!responseText || responseText.length === 0) {\n          throw new Error('服务器返回空响应');\n        }\n\n        // 尝试解析JSON\n        let result;\n        try {\n          result = JSON.parse(responseText);\n          console.log('复杂接口解析后的JSON:', result);\n        } catch (jsonError) {\n          console.error('复杂接口JSON解析失败:', jsonError);\n          console.log('无法解析的响应内容:', responseText);\n          throw new Error('服务器返回的数据格式错误');\n        }\n        if (result.code === '200') {\n          users.value = result.data || [];\n          console.log('从复杂接口获取用户列表:', users.value);\n          if (users.value.length === 0) {\n            ElMessage.info('暂无用户数据');\n          } else {\n            ElMessage.success(`成功加载 ${users.value.length} 个用户（复杂接口）`);\n          }\n        } else {\n          console.error('API返回错误:', result);\n          ElMessage.error(result.msg || '获取用户列表失败');\n        }\n      } catch (error) {\n        console.error('获取用户列表失败:', error);\n        ElMessage.error(`网络错误：${error.message}`);\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 刷新用户列表\n    const refreshUsers = () => {\n      fetchUsers();\n    };\n\n    // 显示添加用户对话框\n    const showAddDialog = () => {\n      dialogTitle.value = '添加用户';\n      isEdit.value = false;\n      userForm.value = {\n        id: null,\n        username: '',\n        password: '',\n        nickname: '',\n        email: '',\n        phone: '',\n        address: '',\n        role: 'user'\n      };\n      dialogVisible.value = true;\n    };\n\n    // 编辑用户\n    const editUser = user => {\n      dialogTitle.value = '编辑用户';\n      isEdit.value = true;\n      userForm.value = {\n        ...user\n      };\n      dialogVisible.value = true;\n    };\n\n    // 保存用户\n    const saveUser = async () => {\n      if (!userFormRef.value) return;\n      await userFormRef.value.validate(async valid => {\n        if (valid) {\n          saveLoading.value = true;\n          try {\n            const url = isEdit.value ? 'http://localhost:9192/userAPI/update' : 'http://localhost:9192/userAPI/add';\n            const method = isEdit.value ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n              method,\n              headers: {\n                'Content-Type': 'application/json',\n                'token': getToken()\n              },\n              body: JSON.stringify(userForm.value)\n            });\n            const result = await response.json();\n            if (result.code === '200') {\n              ElNotification({\n                title: '成功',\n                message: isEdit.value ? '用户更新成功' : '用户添加成功',\n                type: 'success'\n              });\n              dialogVisible.value = false;\n              fetchUsers();\n            } else {\n              ElMessage.error(result.msg || '操作失败');\n            }\n          } catch (error) {\n            console.error('保存用户失败:', error);\n            ElMessage.error('网络错误，请稍后重试');\n          } finally {\n            saveLoading.value = false;\n          }\n        }\n      });\n    };\n\n    // 删除用户\n    const deleteUser = async user => {\n      if (user.username === 'root') {\n        ElMessage.warning('不能删除root用户');\n        return;\n      }\n      try {\n        await ElMessageBox.confirm(`确定要删除用户 \"${user.username}\" 吗？`, '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        const response = await fetch(`http://localhost:9192/userAPI/delete/${user.id}`, {\n          method: 'DELETE',\n          headers: {\n            'token': getToken()\n          }\n        });\n        const result = await response.json();\n        if (result.code === '200') {\n          ElNotification({\n            title: '成功',\n            message: '用户删除成功',\n            type: 'success'\n          });\n          fetchUsers();\n        } else {\n          ElMessage.error(result.msg || '删除失败');\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除用户失败:', error);\n          ElMessage.error('网络错误，请稍后重试');\n        }\n      }\n    };\n\n    // 退出登录\n    const logout = () => {\n      localStorage.clear();\n      ElMessage.success('已退出登录');\n      router.push('/login');\n    };\n\n    // 测试后端连接\n    const testBackend = async () => {\n      try {\n        console.log('=== 测试后端连接 ===');\n\n        // 1. 测试最简单的字符串接口\n        console.log('1. 测试简单字符串接口...');\n        const simpleResponse = await fetch('http://localhost:9192/userAPI/simple');\n        console.log('简单接口状态:', simpleResponse.status);\n        const simpleText = await simpleResponse.text();\n        console.log('简单接口响应:', simpleText);\n\n        // 2. 测试JSON接口\n        console.log('2. 测试JSON接口...');\n        const jsonResponse = await fetch('http://localhost:9192/userAPI/jsontest');\n        console.log('JSON接口状态:', jsonResponse.status);\n        const jsonText = await jsonResponse.text();\n        console.log('JSON接口原始响应:', jsonText);\n        try {\n          const jsonResult = JSON.parse(jsonText);\n          console.log('JSON接口解析结果:', jsonResult);\n        } catch (e) {\n          console.error('JSON接口解析失败:', e);\n        }\n\n        // 3. 测试复杂的test接口\n        console.log('3. 测试复杂接口...');\n        const testResponse = await fetch('http://localhost:9192/userAPI/test');\n        console.log('复杂接口状态:', testResponse.status);\n        const testText = await testResponse.text();\n        console.log('复杂接口原始响应:', testText);\n        if (testText) {\n          try {\n            const testResult = JSON.parse(testText);\n            console.log('复杂接口解析结果:', testResult);\n            if (testResult.code === '200' && testResult.data && testResult.data.length > 0) {\n              console.log('后端连接正常，用户数据:', testResult.data.length, '条');\n              ElMessage.success('后端连接正常');\n            } else {\n              console.warn('后端连接正常但无用户数据');\n            }\n          } catch (e) {\n            console.error('复杂接口JSON解析失败:', e);\n          }\n        }\n      } catch (error) {\n        console.error('测试后端连接失败:', error);\n        ElMessage.error('无法连接到后端服务，请检查后端是否启动');\n      }\n    };\n\n    // 直接测试API\n    const testDirectAPI = async () => {\n      try {\n        console.log('=== 直接测试API ===');\n\n        // 1. 测试简单用户接口\n        console.log('1. 测试简单用户接口...');\n        const usersResponse = await fetch('http://localhost:9192/userAPI/users');\n        console.log('Users接口响应状态:', usersResponse.status);\n        const usersText = await usersResponse.text();\n        console.log('Users接口原始响应:', usersText);\n        if (usersText) {\n          try {\n            const usersResult = JSON.parse(usersText);\n            console.log('Users接口JSON结果:', usersResult);\n            if (usersResult.code === '200') {\n              users.value = usersResult.data || [];\n              ElMessage.success(`简单接口成功获取 ${users.value.length} 个用户`);\n              return; // 如果简单接口成功，就不需要测试复杂接口了\n            }\n          } catch (jsonError) {\n            console.error('Users接口JSON解析失败:', jsonError);\n          }\n        }\n\n        // 2. 测试queryALL接口\n        console.log('2. 测试queryALL接口...');\n        const queryResponse = await fetch('http://localhost:9192/userAPI/queryALL');\n        console.log('QueryALL接口响应状态:', queryResponse.status);\n        const queryText = await queryResponse.text();\n        console.log('QueryALL接口原始响应:', queryText);\n        console.log('QueryALL接口响应长度:', queryText.length);\n        if (queryText && queryText.length > 0) {\n          try {\n            const queryResult = JSON.parse(queryText);\n            console.log('QueryALL接口JSON结果:', queryResult);\n            if (queryResult.code === '200') {\n              users.value = queryResult.data || [];\n              ElMessage.success(`QueryALL接口成功获取 ${users.value.length} 个用户`);\n            } else {\n              ElMessage.error(`API返回错误: ${queryResult.msg}`);\n            }\n          } catch (jsonError) {\n            console.error('QueryALL接口JSON解析失败:', jsonError);\n            console.log('响应内容不是有效JSON:', queryText);\n            ElMessage.error('服务器返回的数据格式错误');\n          }\n        } else {\n          console.error('QueryALL接口返回空响应');\n          ElMessage.error('QueryALL接口返回空响应');\n        }\n      } catch (error) {\n        console.error('直接测试API失败:', error);\n        ElMessage.error(`API测试失败: ${error.message}`);\n      }\n    };\n\n    // 组件挂载时获取用户列表\n    onMounted(() => {\n      console.log('Admin组件已挂载');\n      testBackend();\n      fetchUsers();\n    });\n    const __returned__ = {\n      router,\n      username,\n      users,\n      loading,\n      dialogVisible,\n      dialogTitle,\n      isEdit,\n      saveLoading,\n      userForm,\n      userFormRef,\n      userRules,\n      getToken,\n      fetchUsers,\n      refreshUsers,\n      showAddDialog,\n      editUser,\n      saveUser,\n      deleteUser,\n      logout,\n      testBackend,\n      testDirectAPI,\n      ref,\n      onMounted,\n      get useRouter() {\n        return useRouter;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get ElNotification() {\n        return ElNotification;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "useRouter", "ElMessage", "ElMessageBox", "ElNotification", "router", "username", "localStorage", "getItem", "users", "loading", "dialogVisible", "dialogTitle", "isEdit", "saveLoading", "userForm", "id", "password", "nickname", "email", "phone", "address", "role", "userFormRef", "userRules", "required", "message", "trigger", "min", "max", "type", "getToken", "fetchUsers", "value", "console", "log", "simpleResponse", "fetch", "status", "simpleText", "text", "length", "simpleResult", "JSON", "parse", "code", "data", "success", "jsonError", "error", "response", "method", "headers", "responseText", "Error", "result", "info", "msg", "refreshUsers", "showAddDialog", "editUser", "user", "saveUser", "validate", "valid", "url", "body", "stringify", "json", "title", "deleteUser", "warning", "confirm", "confirmButtonText", "cancelButtonText", "logout", "clear", "push", "testBackend", "jsonResponse", "jsonText", "jsonResult", "e", "testResponse", "testText", "testResult", "warn", "testDirectAPI", "usersResponse", "usersText", "usersResult", "queryResponse", "queryText", "query<PERSON><PERSON>ult"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/src/views/Admin.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-container\">\n    <div class=\"admin-header\">\n      <h1>🛠️ 后台管理系统</h1>\n      <div class=\"header-actions\">\n        <span>欢迎，{{ username }}</span>\n        <el-button type=\"danger\" @click=\"logout\">退出登录</el-button>\n      </div>\n    </div>\n\n    <div class=\"admin-content\">\n      <div class=\"toolbar\">\n        <el-button type=\"primary\" @click=\"showAddDialog\">添加用户</el-button>\n        <el-button @click=\"refreshUsers\">刷新</el-button>\n        <el-button type=\"success\" @click=\"testDirectAPI\">直接测试API</el-button>\n      </div>\n\n      <!-- 用户列表表格 -->\n      <el-table :data=\"users\" style=\"width: 100%\" v-loading=\"loading\">\n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\" />\n        <el-table-column prop=\"username\" label=\"用户名\" width=\"120\" />\n        <el-table-column prop=\"nickname\" label=\"昵称\" width=\"120\" />\n        <el-table-column prop=\"email\" label=\"邮箱\" width=\"180\" />\n        <el-table-column prop=\"phone\" label=\"电话\" width=\"120\" />\n        <el-table-column prop=\"role\" label=\"角色\" width=\"100\">\n          <template #default=\"scope\">\n            <el-tag :type=\"scope.row.role === 'admin' ? 'danger' : 'primary'\">\n              {{ scope.row.role === 'admin' ? '管理员' : '普通用户' }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"address\" label=\"地址\" />\n        <el-table-column label=\"操作\" width=\"180\">\n          <template #default=\"scope\">\n            <el-button size=\"small\" @click=\"editUser(scope.row)\">编辑</el-button>\n            <el-button \n              size=\"small\" \n              type=\"danger\" \n              @click=\"deleteUser(scope.row)\"\n              :disabled=\"scope.row.username === 'root'\"\n            >\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <!-- 添加/编辑用户对话框 -->\n    <el-dialog\n      :title=\"dialogTitle\"\n      v-model=\"dialogVisible\"\n      width=\"500px\"\n    >\n      <el-form\n        ref=\"userFormRef\"\n        :model=\"userForm\"\n        :rules=\"userRules\"\n        label-width=\"80px\"\n      >\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"userForm.username\" :disabled=\"isEdit\" />\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\" v-if=\"!isEdit\">\n          <el-input v-model=\"userForm.password\" type=\"password\" />\n        </el-form-item>\n        <el-form-item label=\"昵称\" prop=\"nickname\">\n          <el-input v-model=\"userForm.nickname\" />\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"userForm.email\" />\n        </el-form-item>\n        <el-form-item label=\"电话\" prop=\"phone\">\n          <el-input v-model=\"userForm.phone\" />\n        </el-form-item>\n        <el-form-item label=\"地址\" prop=\"address\">\n          <el-input v-model=\"userForm.address\" />\n        </el-form-item>\n        <el-form-item label=\"角色\" prop=\"role\">\n          <el-select v-model=\"userForm.role\" style=\"width: 100%\">\n            <el-option label=\"普通用户\" value=\"user\" />\n            <el-option label=\"管理员\" value=\"admin\" />\n          </el-select>\n        </el-form-item>\n      </el-form>\n      \n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveUser\" :loading=\"saveLoading\">\n            {{ isEdit ? '更新' : '添加' }}\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage, ElMessageBox, ElNotification } from 'element-plus'\n\nconst router = useRouter()\n\n// 响应式数据\nconst username = ref(localStorage.getItem('username') || '')\nconst users = ref([])\nconst loading = ref(false)\nconst dialogVisible = ref(false)\nconst dialogTitle = ref('添加用户')\nconst isEdit = ref(false)\nconst saveLoading = ref(false)\n\n// 表单数据\nconst userForm = ref({\n  id: null,\n  username: '',\n  password: '',\n  nickname: '',\n  email: '',\n  phone: '',\n  address: '',\n  role: 'user'\n})\n\nconst userFormRef = ref()\n\n// 表单验证规则\nconst userRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' },\n    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }\n  ],\n  nickname: [\n    { required: true, message: '请输入昵称', trigger: 'blur' }\n  ],\n  email: [\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\n  ],\n  role: [\n    { required: true, message: '请选择角色', trigger: 'change' }\n  ]\n}\n\n// 获取token\nconst getToken = () => localStorage.getItem('token')\n\n// 获取用户列表\nconst fetchUsers = async () => {\n  loading.value = true\n  console.log('开始获取用户列表...')\n\n  try {\n    // 先尝试简单的用户接口\n    console.log('尝试简单用户接口...')\n    const simpleResponse = await fetch('http://localhost:9192/userAPI/users')\n    console.log('简单接口响应状态:', simpleResponse.status)\n\n    const simpleText = await simpleResponse.text()\n    console.log('简单接口原始响应:', simpleText)\n\n    if (simpleText && simpleText.length > 0) {\n      try {\n        const simpleResult = JSON.parse(simpleText)\n        console.log('简单接口解析结果:', simpleResult)\n\n        if (simpleResult.code === '200') {\n          users.value = simpleResult.data || []\n          console.log('从简单接口获取用户列表:', users.value)\n          ElMessage.success(`成功加载 ${users.value.length} 个用户（简单接口）`)\n          return\n        }\n      } catch (jsonError) {\n        console.error('简单接口JSON解析失败:', jsonError)\n      }\n    }\n\n    // 如果简单接口失败，尝试复杂接口\n    console.log('尝试复杂用户接口...')\n    const response = await fetch('http://localhost:9192/userAPI/queryALL', {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        'token': getToken()\n      }\n    })\n\n    console.log('复杂接口响应状态:', response.status)\n\n    // 先获取原始文本\n    const responseText = await response.text()\n    console.log('复杂接口原始响应:', responseText)\n    console.log('复杂接口响应长度:', responseText.length)\n\n    if (!responseText || responseText.length === 0) {\n      throw new Error('服务器返回空响应')\n    }\n\n    // 尝试解析JSON\n    let result\n    try {\n      result = JSON.parse(responseText)\n      console.log('复杂接口解析后的JSON:', result)\n    } catch (jsonError) {\n      console.error('复杂接口JSON解析失败:', jsonError)\n      console.log('无法解析的响应内容:', responseText)\n      throw new Error('服务器返回的数据格式错误')\n    }\n\n    if (result.code === '200') {\n      users.value = result.data || []\n      console.log('从复杂接口获取用户列表:', users.value)\n      if (users.value.length === 0) {\n        ElMessage.info('暂无用户数据')\n      } else {\n        ElMessage.success(`成功加载 ${users.value.length} 个用户（复杂接口）`)\n      }\n    } else {\n      console.error('API返回错误:', result)\n      ElMessage.error(result.msg || '获取用户列表失败')\n    }\n  } catch (error) {\n    console.error('获取用户列表失败:', error)\n    ElMessage.error(`网络错误：${error.message}`)\n  } finally {\n    loading.value = false\n  }\n}\n\n// 刷新用户列表\nconst refreshUsers = () => {\n  fetchUsers()\n}\n\n// 显示添加用户对话框\nconst showAddDialog = () => {\n  dialogTitle.value = '添加用户'\n  isEdit.value = false\n  userForm.value = {\n    id: null,\n    username: '',\n    password: '',\n    nickname: '',\n    email: '',\n    phone: '',\n    address: '',\n    role: 'user'\n  }\n  dialogVisible.value = true\n}\n\n// 编辑用户\nconst editUser = (user) => {\n  dialogTitle.value = '编辑用户'\n  isEdit.value = true\n  userForm.value = { ...user }\n  dialogVisible.value = true\n}\n\n// 保存用户\nconst saveUser = async () => {\n  if (!userFormRef.value) return\n  \n  await userFormRef.value.validate(async (valid) => {\n    if (valid) {\n      saveLoading.value = true\n      try {\n        const url = isEdit.value \n          ? 'http://localhost:9192/userAPI/update'\n          : 'http://localhost:9192/userAPI/add'\n        \n        const method = isEdit.value ? 'PUT' : 'POST'\n        \n        const response = await fetch(url, {\n          method,\n          headers: {\n            'Content-Type': 'application/json',\n            'token': getToken()\n          },\n          body: JSON.stringify(userForm.value)\n        })\n        \n        const result = await response.json()\n        \n        if (result.code === '200') {\n          ElNotification({\n            title: '成功',\n            message: isEdit.value ? '用户更新成功' : '用户添加成功',\n            type: 'success'\n          })\n          dialogVisible.value = false\n          fetchUsers()\n        } else {\n          ElMessage.error(result.msg || '操作失败')\n        }\n      } catch (error) {\n        console.error('保存用户失败:', error)\n        ElMessage.error('网络错误，请稍后重试')\n      } finally {\n        saveLoading.value = false\n      }\n    }\n  })\n}\n\n// 删除用户\nconst deleteUser = async (user) => {\n  if (user.username === 'root') {\n    ElMessage.warning('不能删除root用户')\n    return\n  }\n  \n  try {\n    await ElMessageBox.confirm(\n      `确定要删除用户 \"${user.username}\" 吗？`,\n      '确认删除',\n      {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning',\n      }\n    )\n    \n    const response = await fetch(`http://localhost:9192/userAPI/delete/${user.id}`, {\n      method: 'DELETE',\n      headers: {\n        'token': getToken()\n      }\n    })\n    \n    const result = await response.json()\n    \n    if (result.code === '200') {\n      ElNotification({\n        title: '成功',\n        message: '用户删除成功',\n        type: 'success'\n      })\n      fetchUsers()\n    } else {\n      ElMessage.error(result.msg || '删除失败')\n    }\n  } catch (error) {\n    if (error !== 'cancel') {\n      console.error('删除用户失败:', error)\n      ElMessage.error('网络错误，请稍后重试')\n    }\n  }\n}\n\n// 退出登录\nconst logout = () => {\n  localStorage.clear()\n  ElMessage.success('已退出登录')\n  router.push('/login')\n}\n\n// 测试后端连接\nconst testBackend = async () => {\n  try {\n    console.log('=== 测试后端连接 ===')\n\n    // 1. 测试最简单的字符串接口\n    console.log('1. 测试简单字符串接口...')\n    const simpleResponse = await fetch('http://localhost:9192/userAPI/simple')\n    console.log('简单接口状态:', simpleResponse.status)\n    const simpleText = await simpleResponse.text()\n    console.log('简单接口响应:', simpleText)\n\n    // 2. 测试JSON接口\n    console.log('2. 测试JSON接口...')\n    const jsonResponse = await fetch('http://localhost:9192/userAPI/jsontest')\n    console.log('JSON接口状态:', jsonResponse.status)\n    const jsonText = await jsonResponse.text()\n    console.log('JSON接口原始响应:', jsonText)\n\n    try {\n      const jsonResult = JSON.parse(jsonText)\n      console.log('JSON接口解析结果:', jsonResult)\n    } catch (e) {\n      console.error('JSON接口解析失败:', e)\n    }\n\n    // 3. 测试复杂的test接口\n    console.log('3. 测试复杂接口...')\n    const testResponse = await fetch('http://localhost:9192/userAPI/test')\n    console.log('复杂接口状态:', testResponse.status)\n    const testText = await testResponse.text()\n    console.log('复杂接口原始响应:', testText)\n\n    if (testText) {\n      try {\n        const testResult = JSON.parse(testText)\n        console.log('复杂接口解析结果:', testResult)\n\n        if (testResult.code === '200' && testResult.data && testResult.data.length > 0) {\n          console.log('后端连接正常，用户数据:', testResult.data.length, '条')\n          ElMessage.success('后端连接正常')\n        } else {\n          console.warn('后端连接正常但无用户数据')\n        }\n      } catch (e) {\n        console.error('复杂接口JSON解析失败:', e)\n      }\n    }\n\n  } catch (error) {\n    console.error('测试后端连接失败:', error)\n    ElMessage.error('无法连接到后端服务，请检查后端是否启动')\n  }\n}\n\n// 直接测试API\nconst testDirectAPI = async () => {\n  try {\n    console.log('=== 直接测试API ===')\n\n    // 1. 测试简单用户接口\n    console.log('1. 测试简单用户接口...')\n    const usersResponse = await fetch('http://localhost:9192/userAPI/users')\n    console.log('Users接口响应状态:', usersResponse.status)\n\n    const usersText = await usersResponse.text()\n    console.log('Users接口原始响应:', usersText)\n\n    if (usersText) {\n      try {\n        const usersResult = JSON.parse(usersText)\n        console.log('Users接口JSON结果:', usersResult)\n\n        if (usersResult.code === '200') {\n          users.value = usersResult.data || []\n          ElMessage.success(`简单接口成功获取 ${users.value.length} 个用户`)\n          return // 如果简单接口成功，就不需要测试复杂接口了\n        }\n      } catch (jsonError) {\n        console.error('Users接口JSON解析失败:', jsonError)\n      }\n    }\n\n    // 2. 测试queryALL接口\n    console.log('2. 测试queryALL接口...')\n    const queryResponse = await fetch('http://localhost:9192/userAPI/queryALL')\n    console.log('QueryALL接口响应状态:', queryResponse.status)\n\n    const queryText = await queryResponse.text()\n    console.log('QueryALL接口原始响应:', queryText)\n    console.log('QueryALL接口响应长度:', queryText.length)\n\n    if (queryText && queryText.length > 0) {\n      try {\n        const queryResult = JSON.parse(queryText)\n        console.log('QueryALL接口JSON结果:', queryResult)\n\n        if (queryResult.code === '200') {\n          users.value = queryResult.data || []\n          ElMessage.success(`QueryALL接口成功获取 ${users.value.length} 个用户`)\n        } else {\n          ElMessage.error(`API返回错误: ${queryResult.msg}`)\n        }\n      } catch (jsonError) {\n        console.error('QueryALL接口JSON解析失败:', jsonError)\n        console.log('响应内容不是有效JSON:', queryText)\n        ElMessage.error('服务器返回的数据格式错误')\n      }\n    } else {\n      console.error('QueryALL接口返回空响应')\n      ElMessage.error('QueryALL接口返回空响应')\n    }\n\n  } catch (error) {\n    console.error('直接测试API失败:', error)\n    ElMessage.error(`API测试失败: ${error.message}`)\n  }\n}\n\n// 组件挂载时获取用户列表\nonMounted(() => {\n  console.log('Admin组件已挂载')\n  testBackend()\n  fetchUsers()\n})\n</script>\n\n<style scoped>\n.admin-container {\n  padding: 20px;\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.admin-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.admin-header h1 {\n  margin: 0;\n  color: #333;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.admin-content {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.toolbar {\n  margin-bottom: 20px;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n</style>\n"], "mappings": ";AAmGA,SAASA,GAAG,EAAEC,SAAS,QAAQ,KAAI;AACnC,SAASC,SAAS,QAAQ,YAAW;AACrC,SAASC,SAAS,EAAEC,YAAY,EAAEC,cAAc,QAAQ,cAAa;;;;;;;IAErE,MAAMC,MAAM,GAAGJ,SAAS,CAAC;;IAEzB;IACA,MAAMK,QAAQ,GAAGP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE;IAC3D,MAAMC,KAAK,GAAGV,GAAG,CAAC,EAAE;IACpB,MAAMW,OAAO,GAAGX,GAAG,CAAC,KAAK;IACzB,MAAMY,aAAa,GAAGZ,GAAG,CAAC,KAAK;IAC/B,MAAMa,WAAW,GAAGb,GAAG,CAAC,MAAM;IAC9B,MAAMc,MAAM,GAAGd,GAAG,CAAC,KAAK;IACxB,MAAMe,WAAW,GAAGf,GAAG,CAAC,KAAK;;IAE7B;IACA,MAAMgB,QAAQ,GAAGhB,GAAG,CAAC;MACnBiB,EAAE,EAAE,IAAI;MACRV,QAAQ,EAAE,EAAE;MACZW,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE;IACR,CAAC;IAED,MAAMC,WAAW,GAAGxB,GAAG,CAAC;;IAExB;IACA,MAAMyB,SAAS,GAAG;MAChBlB,QAAQ,EAAE,CACR;QAAEmB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,eAAe;QAAEC,OAAO,EAAE;MAAO,EAC9D;MACDV,QAAQ,EAAE,CACR;QAAEQ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,cAAc;QAAEC,OAAO,EAAE;MAAO,EAC7D;MACDT,QAAQ,EAAE,CACR;QAAEO,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,EACrD;MACDR,KAAK,EAAE,CACL;QAAEW,IAAI,EAAE,OAAO;QAAEJ,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,EACzD;MACDL,IAAI,EAAE,CACJ;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS;IAE1D;;IAEA;IACA,MAAMI,QAAQ,GAAGA,CAAA,KAAMxB,YAAY,CAACC,OAAO,CAAC,OAAO;;IAEnD;IACA,MAAMwB,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7BtB,OAAO,CAACuB,KAAK,GAAG,IAAG;MACnBC,OAAO,CAACC,GAAG,CAAC,aAAa;MAEzB,IAAI;QACF;QACAD,OAAO,CAACC,GAAG,CAAC,aAAa;QACzB,MAAMC,cAAc,GAAG,MAAMC,KAAK,CAAC,qCAAqC;QACxEH,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,cAAc,CAACE,MAAM;QAE9C,MAAMC,UAAU,GAAG,MAAMH,cAAc,CAACI,IAAI,CAAC;QAC7CN,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEI,UAAU;QAEnC,IAAIA,UAAU,IAAIA,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;UACvC,IAAI;YACF,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU;YAC1CL,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEO,YAAY;YAErC,IAAIA,YAAY,CAACG,IAAI,KAAK,KAAK,EAAE;cAC/BpC,KAAK,CAACwB,KAAK,GAAGS,YAAY,CAACI,IAAI,IAAI,EAAC;cACpCZ,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE1B,KAAK,CAACwB,KAAK;cACvC/B,SAAS,CAAC6C,OAAO,CAAC,QAAQtC,KAAK,CAACwB,KAAK,CAACQ,MAAM,YAAY;cACxD;YACF;UACF,CAAC,CAAC,OAAOO,SAAS,EAAE;YAClBd,OAAO,CAACe,KAAK,CAAC,eAAe,EAAED,SAAS;UAC1C;QACF;;QAEA;QACAd,OAAO,CAACC,GAAG,CAAC,aAAa;QACzB,MAAMe,QAAQ,GAAG,MAAMb,KAAK,CAAC,wCAAwC,EAAE;UACrEc,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,OAAO,EAAErB,QAAQ,CAAC;UACpB;QACF,CAAC;QAEDG,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEe,QAAQ,CAACZ,MAAM;;QAExC;QACA,MAAMe,YAAY,GAAG,MAAMH,QAAQ,CAACV,IAAI,CAAC;QACzCN,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEkB,YAAY;QACrCnB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEkB,YAAY,CAACZ,MAAM;QAE5C,IAAI,CAACY,YAAY,IAAIA,YAAY,CAACZ,MAAM,KAAK,CAAC,EAAE;UAC9C,MAAM,IAAIa,KAAK,CAAC,UAAU;QAC5B;;QAEA;QACA,IAAIC,MAAK;QACT,IAAI;UACFA,MAAM,GAAGZ,IAAI,CAACC,KAAK,CAACS,YAAY;UAChCnB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEoB,MAAM;QACrC,CAAC,CAAC,OAAOP,SAAS,EAAE;UAClBd,OAAO,CAACe,KAAK,CAAC,eAAe,EAAED,SAAS;UACxCd,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkB,YAAY;UACtC,MAAM,IAAIC,KAAK,CAAC,cAAc;QAChC;QAEA,IAAIC,MAAM,CAACV,IAAI,KAAK,KAAK,EAAE;UACzBpC,KAAK,CAACwB,KAAK,GAAGsB,MAAM,CAACT,IAAI,IAAI,EAAC;UAC9BZ,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE1B,KAAK,CAACwB,KAAK;UACvC,IAAIxB,KAAK,CAACwB,KAAK,CAACQ,MAAM,KAAK,CAAC,EAAE;YAC5BvC,SAAS,CAACsD,IAAI,CAAC,QAAQ;UACzB,CAAC,MAAM;YACLtD,SAAS,CAAC6C,OAAO,CAAC,QAAQtC,KAAK,CAACwB,KAAK,CAACQ,MAAM,YAAY;UAC1D;QACF,CAAC,MAAM;UACLP,OAAO,CAACe,KAAK,CAAC,UAAU,EAAEM,MAAM;UAChCrD,SAAS,CAAC+C,KAAK,CAACM,MAAM,CAACE,GAAG,IAAI,UAAU;QAC1C;MACF,CAAC,CAAC,OAAOR,KAAK,EAAE;QACdf,OAAO,CAACe,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC/C,SAAS,CAAC+C,KAAK,CAAC,QAAQA,KAAK,CAACvB,OAAO,EAAE;MACzC,CAAC,SAAS;QACRhB,OAAO,CAACuB,KAAK,GAAG,KAAI;MACtB;IACF;;IAEA;IACA,MAAMyB,YAAY,GAAGA,CAAA,KAAM;MACzB1B,UAAU,CAAC;IACb;;IAEA;IACA,MAAM2B,aAAa,GAAGA,CAAA,KAAM;MAC1B/C,WAAW,CAACqB,KAAK,GAAG,MAAK;MACzBpB,MAAM,CAACoB,KAAK,GAAG,KAAI;MACnBlB,QAAQ,CAACkB,KAAK,GAAG;QACfjB,EAAE,EAAE,IAAI;QACRV,QAAQ,EAAE,EAAE;QACZW,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXC,IAAI,EAAE;MACR;MACAX,aAAa,CAACsB,KAAK,GAAG,IAAG;IAC3B;;IAEA;IACA,MAAM2B,QAAQ,GAAIC,IAAI,IAAK;MACzBjD,WAAW,CAACqB,KAAK,GAAG,MAAK;MACzBpB,MAAM,CAACoB,KAAK,GAAG,IAAG;MAClBlB,QAAQ,CAACkB,KAAK,GAAG;QAAE,GAAG4B;MAAK;MAC3BlD,aAAa,CAACsB,KAAK,GAAG,IAAG;IAC3B;;IAEA;IACA,MAAM6B,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI,CAACvC,WAAW,CAACU,KAAK,EAAE;MAExB,MAAMV,WAAW,CAACU,KAAK,CAAC8B,QAAQ,CAAC,MAAOC,KAAK,IAAK;QAChD,IAAIA,KAAK,EAAE;UACTlD,WAAW,CAACmB,KAAK,GAAG,IAAG;UACvB,IAAI;YACF,MAAMgC,GAAG,GAAGpD,MAAM,CAACoB,KAAK,GACpB,sCAAqC,GACrC,mCAAkC;YAEtC,MAAMkB,MAAM,GAAGtC,MAAM,CAACoB,KAAK,GAAG,KAAK,GAAG,MAAK;YAE3C,MAAMiB,QAAQ,GAAG,MAAMb,KAAK,CAAC4B,GAAG,EAAE;cAChCd,MAAM;cACNC,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAErB,QAAQ,CAAC;cACpB,CAAC;cACDmC,IAAI,EAAEvB,IAAI,CAACwB,SAAS,CAACpD,QAAQ,CAACkB,KAAK;YACrC,CAAC;YAED,MAAMsB,MAAM,GAAG,MAAML,QAAQ,CAACkB,IAAI,CAAC;YAEnC,IAAIb,MAAM,CAACV,IAAI,KAAK,KAAK,EAAE;cACzBzC,cAAc,CAAC;gBACbiE,KAAK,EAAE,IAAI;gBACX3C,OAAO,EAAEb,MAAM,CAACoB,KAAK,GAAG,QAAQ,GAAG,QAAQ;gBAC3CH,IAAI,EAAE;cACR,CAAC;cACDnB,aAAa,CAACsB,KAAK,GAAG,KAAI;cAC1BD,UAAU,CAAC;YACb,CAAC,MAAM;cACL9B,SAAS,CAAC+C,KAAK,CAACM,MAAM,CAACE,GAAG,IAAI,MAAM;YACtC;UACF,CAAC,CAAC,OAAOR,KAAK,EAAE;YACdf,OAAO,CAACe,KAAK,CAAC,SAAS,EAAEA,KAAK;YAC9B/C,SAAS,CAAC+C,KAAK,CAAC,YAAY;UAC9B,CAAC,SAAS;YACRnC,WAAW,CAACmB,KAAK,GAAG,KAAI;UAC1B;QACF;MACF,CAAC;IACH;;IAEA;IACA,MAAMqC,UAAU,GAAG,MAAOT,IAAI,IAAK;MACjC,IAAIA,IAAI,CAACvD,QAAQ,KAAK,MAAM,EAAE;QAC5BJ,SAAS,CAACqE,OAAO,CAAC,YAAY;QAC9B;MACF;MAEA,IAAI;QACF,MAAMpE,YAAY,CAACqE,OAAO,CACxB,YAAYX,IAAI,CAACvD,QAAQ,MAAM,EAC/B,MAAM,EACN;UACEmE,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtB5C,IAAI,EAAE;QACR,CACF;QAEA,MAAMoB,QAAQ,GAAG,MAAMb,KAAK,CAAC,wCAAwCwB,IAAI,CAAC7C,EAAE,EAAE,EAAE;UAC9EmC,MAAM,EAAE,QAAQ;UAChBC,OAAO,EAAE;YACP,OAAO,EAAErB,QAAQ,CAAC;UACpB;QACF,CAAC;QAED,MAAMwB,MAAM,GAAG,MAAML,QAAQ,CAACkB,IAAI,CAAC;QAEnC,IAAIb,MAAM,CAACV,IAAI,KAAK,KAAK,EAAE;UACzBzC,cAAc,CAAC;YACbiE,KAAK,EAAE,IAAI;YACX3C,OAAO,EAAE,QAAQ;YACjBI,IAAI,EAAE;UACR,CAAC;UACDE,UAAU,CAAC;QACb,CAAC,MAAM;UACL9B,SAAS,CAAC+C,KAAK,CAACM,MAAM,CAACE,GAAG,IAAI,MAAM;QACtC;MACF,CAAC,CAAC,OAAOR,KAAK,EAAE;QACd,IAAIA,KAAK,KAAK,QAAQ,EAAE;UACtBf,OAAO,CAACe,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9B/C,SAAS,CAAC+C,KAAK,CAAC,YAAY;QAC9B;MACF;IACF;;IAEA;IACA,MAAM0B,MAAM,GAAGA,CAAA,KAAM;MACnBpE,YAAY,CAACqE,KAAK,CAAC;MACnB1E,SAAS,CAAC6C,OAAO,CAAC,OAAO;MACzB1C,MAAM,CAACwE,IAAI,CAAC,QAAQ;IACtB;;IAEA;IACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACF5C,OAAO,CAACC,GAAG,CAAC,gBAAgB;;QAE5B;QACAD,OAAO,CAACC,GAAG,CAAC,iBAAiB;QAC7B,MAAMC,cAAc,GAAG,MAAMC,KAAK,CAAC,sCAAsC;QACzEH,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEC,cAAc,CAACE,MAAM;QAC5C,MAAMC,UAAU,GAAG,MAAMH,cAAc,CAACI,IAAI,CAAC;QAC7CN,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEI,UAAU;;QAEjC;QACAL,OAAO,CAACC,GAAG,CAAC,gBAAgB;QAC5B,MAAM4C,YAAY,GAAG,MAAM1C,KAAK,CAAC,wCAAwC;QACzEH,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE4C,YAAY,CAACzC,MAAM;QAC5C,MAAM0C,QAAQ,GAAG,MAAMD,YAAY,CAACvC,IAAI,CAAC;QACzCN,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE6C,QAAQ;QAEnC,IAAI;UACF,MAAMC,UAAU,GAAGtC,IAAI,CAACC,KAAK,CAACoC,QAAQ;UACtC9C,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE8C,UAAU;QACvC,CAAC,CAAC,OAAOC,CAAC,EAAE;UACVhD,OAAO,CAACe,KAAK,CAAC,aAAa,EAAEiC,CAAC;QAChC;;QAEA;QACAhD,OAAO,CAACC,GAAG,CAAC,cAAc;QAC1B,MAAMgD,YAAY,GAAG,MAAM9C,KAAK,CAAC,oCAAoC;QACrEH,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEgD,YAAY,CAAC7C,MAAM;QAC1C,MAAM8C,QAAQ,GAAG,MAAMD,YAAY,CAAC3C,IAAI,CAAC;QACzCN,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEiD,QAAQ;QAEjC,IAAIA,QAAQ,EAAE;UACZ,IAAI;YACF,MAAMC,UAAU,GAAG1C,IAAI,CAACC,KAAK,CAACwC,QAAQ;YACtClD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEkD,UAAU;YAEnC,IAAIA,UAAU,CAACxC,IAAI,KAAK,KAAK,IAAIwC,UAAU,CAACvC,IAAI,IAAIuC,UAAU,CAACvC,IAAI,CAACL,MAAM,GAAG,CAAC,EAAE;cAC9EP,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEkD,UAAU,CAACvC,IAAI,CAACL,MAAM,EAAE,GAAG;cACvDvC,SAAS,CAAC6C,OAAO,CAAC,QAAQ;YAC5B,CAAC,MAAM;cACLb,OAAO,CAACoD,IAAI,CAAC,cAAc;YAC7B;UACF,CAAC,CAAC,OAAOJ,CAAC,EAAE;YACVhD,OAAO,CAACe,KAAK,CAAC,eAAe,EAAEiC,CAAC;UAClC;QACF;MAEF,CAAC,CAAC,OAAOjC,KAAK,EAAE;QACdf,OAAO,CAACe,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC/C,SAAS,CAAC+C,KAAK,CAAC,qBAAqB;MACvC;IACF;;IAEA;IACA,MAAMsC,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFrD,OAAO,CAACC,GAAG,CAAC,iBAAiB;;QAE7B;QACAD,OAAO,CAACC,GAAG,CAAC,gBAAgB;QAC5B,MAAMqD,aAAa,GAAG,MAAMnD,KAAK,CAAC,qCAAqC;QACvEH,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEqD,aAAa,CAAClD,MAAM;QAEhD,MAAMmD,SAAS,GAAG,MAAMD,aAAa,CAAChD,IAAI,CAAC;QAC3CN,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEsD,SAAS;QAErC,IAAIA,SAAS,EAAE;UACb,IAAI;YACF,MAAMC,WAAW,GAAG/C,IAAI,CAACC,KAAK,CAAC6C,SAAS;YACxCvD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEuD,WAAW;YAEzC,IAAIA,WAAW,CAAC7C,IAAI,KAAK,KAAK,EAAE;cAC9BpC,KAAK,CAACwB,KAAK,GAAGyD,WAAW,CAAC5C,IAAI,IAAI,EAAC;cACnC5C,SAAS,CAAC6C,OAAO,CAAC,YAAYtC,KAAK,CAACwB,KAAK,CAACQ,MAAM,MAAM;cACtD,OAAM,CAAC;YACT;UACF,CAAC,CAAC,OAAOO,SAAS,EAAE;YAClBd,OAAO,CAACe,KAAK,CAAC,kBAAkB,EAAED,SAAS;UAC7C;QACF;;QAEA;QACAd,OAAO,CAACC,GAAG,CAAC,oBAAoB;QAChC,MAAMwD,aAAa,GAAG,MAAMtD,KAAK,CAAC,wCAAwC;QAC1EH,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEwD,aAAa,CAACrD,MAAM;QAEnD,MAAMsD,SAAS,GAAG,MAAMD,aAAa,CAACnD,IAAI,CAAC;QAC3CN,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEyD,SAAS;QACxC1D,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEyD,SAAS,CAACnD,MAAM;QAE/C,IAAImD,SAAS,IAAIA,SAAS,CAACnD,MAAM,GAAG,CAAC,EAAE;UACrC,IAAI;YACF,MAAMoD,WAAW,GAAGlD,IAAI,CAACC,KAAK,CAACgD,SAAS;YACxC1D,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE0D,WAAW;YAE5C,IAAIA,WAAW,CAAChD,IAAI,KAAK,KAAK,EAAE;cAC9BpC,KAAK,CAACwB,KAAK,GAAG4D,WAAW,CAAC/C,IAAI,IAAI,EAAC;cACnC5C,SAAS,CAAC6C,OAAO,CAAC,kBAAkBtC,KAAK,CAACwB,KAAK,CAACQ,MAAM,MAAM;YAC9D,CAAC,MAAM;cACLvC,SAAS,CAAC+C,KAAK,CAAC,YAAY4C,WAAW,CAACpC,GAAG,EAAE;YAC/C;UACF,CAAC,CAAC,OAAOT,SAAS,EAAE;YAClBd,OAAO,CAACe,KAAK,CAAC,qBAAqB,EAAED,SAAS;YAC9Cd,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEyD,SAAS;YACtC1F,SAAS,CAAC+C,KAAK,CAAC,cAAc;UAChC;QACF,CAAC,MAAM;UACLf,OAAO,CAACe,KAAK,CAAC,iBAAiB;UAC/B/C,SAAS,CAAC+C,KAAK,CAAC,iBAAiB;QACnC;MAEF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdf,OAAO,CAACe,KAAK,CAAC,YAAY,EAAEA,KAAK;QACjC/C,SAAS,CAAC+C,KAAK,CAAC,YAAYA,KAAK,CAACvB,OAAO,EAAE;MAC7C;IACF;;IAEA;IACA1B,SAAS,CAAC,MAAM;MACdkC,OAAO,CAACC,GAAG,CAAC,YAAY;MACxB2C,WAAW,CAAC;MACZ9C,UAAU,CAAC;IACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}