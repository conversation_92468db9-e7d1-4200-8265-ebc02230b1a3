{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Login = _resolveComponent(\"Login\", true);\n  return _openBlock(), _createElementBlock(\"div\", null, [_createVNode(_component_Login)]);\n}", "map": {"version": 3, "names": ["_createElementBlock", "_createVNode", "_component_Login"], "sources": ["D:\\2025_down\\project\\shoppingOnline-20250826-sfl\\shoppingOnline-20250826\\shopping\\src\\views\\Login.vue"], "sourcesContent": ["<template>\n  <div>\n    <Login />\n  </div>\n</template>\n\n<script>\nimport Login from '../components/login.vue'\n\nexport default {\n  name: 'LoginView',\n  components: {\n    Login\n  }\n}\n</script>\n"], "mappings": ";;;uBACEA,mBAAA,CAEM,cADJC,YAAA,CAASC,gBAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}