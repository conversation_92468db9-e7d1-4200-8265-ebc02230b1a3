package com.cn.controller;

import com.cn.annotation.Authority;
import com.cn.common.Result;
import com.cn.entity.AuthorityType;
import com.cn.entity.User;
import com.cn.service.UserService;
import com.cn.utils.TokenUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Authority(AuthorityType.noRequire)
@RestController
@RequestMapping("/userAPI")
@CrossOrigin(origins = "*")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 登录接口
     * @param user
     * @return
     */
    @PostMapping("/login")
    public Result login(@RequestBody User user) {
        try {
            User loginUser = userService.login(user.getUsername(), user.getPassword());
            if (loginUser != null) {
                // 生成token
                String token = TokenUtils.genToken(loginUser.getId().toString(), loginUser.getUsername());

                // 清除密码信息，避免返回给前端
                loginUser.setPassword(null);

                Map<String, Object> data = new HashMap<>();
                data.put("user", loginUser);
                data.put("token", token);

                return Result.success(data);
            } else {
                return Result.error("401", "用户名或密码错误");
            }
        } catch (Exception e) {
            return Result.error("500", "登录失败：" + e.getMessage());
        }
    }

    /**
     * 注册接口
     * @param user
     * @return
     */
    @PostMapping("/register")
    public Result register(@RequestBody User user) {
        try {
            userService.addUser(user);
            return Result.success("注册成功");
        } catch (Exception e) {
            return Result.error("500", "注册失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有用户 - 管理员接口（临时简化权限验证）
     * @return
     */
    @GetMapping("/queryALL")
    public Result queryALL(){
        try {
            System.out.println("UserController.queryALL() - 开始处理请求");
            List<User> list = userService.queryALL();
            System.out.println("UserController.queryALL() - 查询到用户数量: " + list.size());

            // 清除密码信息
            list.forEach(user -> {
                user.setPassword(null);
                System.out.println("处理用户: " + user.getUsername() + ", 角色: " + user.getRole());
            });

            Result result = Result.success(list);
            System.out.println("UserController.queryALL() - 返回结果: " + result);
            return result;
        } catch (Exception e) {
            System.err.println("UserController.queryALL() - 发生异常: " + e.getMessage());
            e.printStackTrace();
            return Result.error("500", "查询用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 添加用户 - 管理员接口（临时简化权限验证）
     */
    @PostMapping("/add")
    public Result addUser(@RequestBody User user) {
        try {
            userService.addUser(user);
            return Result.success("用户添加成功");
        } catch (Exception e) {
            return Result.error("500", "添加用户失败：" + e.getMessage());
        }
    }

    /**
     * 更新用户 - 管理员接口（临时简化权限验证）
     */
    @PutMapping("/update")
    public Result updateUser(@RequestBody User user) {
        try {
            userService.updateUser(user);
            return Result.success("用户更新成功");
        } catch (Exception e) {
            return Result.error("500", "更新用户失败：" + e.getMessage());
        }
    }

    /**
     * 删除用户 - 管理员接口（临时简化权限验证）
     */
    @DeleteMapping("/delete/{id}")
    public Result deleteUser(@PathVariable Integer id) {
        try {
            userService.deleteUser(id);
            return Result.success("用户删除成功");
        } catch (Exception e) {
            return Result.error("500", "删除用户失败：" + e.getMessage());
        }
    }

    /**
     * 简单测试接口
     * @return
     */
    @GetMapping("/simple")
    public String simpleTest() {
        return "Hello World";
    }

    /**
     * JSON测试接口
     * @return
     */
    @GetMapping("/jsontest")
    public Map<String, Object> jsonTest() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", "200");
        result.put("msg", "success");
        result.put("data", "test data");
        return result;
    }

    /**
     * 测试接口 - 查看数据库中的用户数据
     * @return
     */
    @GetMapping("/test")
    public Result testUsers() {
        try {
            List<User> users = userService.queryALL();
            System.out.println("数据库中的用户数量: " + users.size());
            for (User u : users) {
                System.out.println("用户: " + u.getUsername() + ", 密码: " + u.getPassword() + ", 角色: " + u.getRole());
            }
            return Result.success(users);
        } catch (Exception e) {
            System.err.println("测试接口异常: " + e.getMessage());
            e.printStackTrace();
            return Result.error("500", "查询失败：" + e.getMessage());
        }
    }



}
