package com.cn.controller;

import com.cn.annotation.Authority;
import com.cn.common.Result;
import com.cn.entity.AuthorityType;
import com.cn.entity.User;
import com.cn.service.UserService;
import com.cn.utils.TokenUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Authority(AuthorityType.noRequire)
@RestController
@RequestMapping("/userAPI")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 登录接口
     * @param user
     * @return
     */
    @PostMapping("/login")
    public Result login(@RequestBody User user) {
        try {
            User loginUser = userService.login(user.getUsername(), user.getPassword());
            if (loginUser != null) {
                // 生成token
                String token = TokenUtils.genToken(loginUser.getId().toString(), loginUser.getUsername());

                Map<String, Object> data = new HashMap<>();
                data.put("user", loginUser);
                data.put("token", token);

                return Result.success(data);
            } else {
                return Result.error("401", "用户名或密码错误");
            }
        } catch (Exception e) {
            return Result.error("500", "登录失败：" + e.getMessage());
        }
    }

    /**
     * 注册接口
     * @param user
     * @return
     */
    @PostMapping("/register")
    public Result register(@RequestBody User user) {
        try {
            userService.addUser(user);
            return Result.success("注册成功");
        } catch (Exception e) {
            return Result.error("500", "注册失败：" + e.getMessage());
        }
    }

    /**
     * 127.0.0.1:9191/userAPI/queryALL
     * @return
     */
    @Authority(AuthorityType.requireAuthority)
    @RequestMapping("/queryALL")
    public Result queryALL(){
        List<User> list =userService.queryALL();
        return Result.success(list);
    }

    /**
     * 添加用户 - 仅管理员权限
     */
    @Authority(AuthorityType.requireAuthority)
    @PostMapping("/add")
    public Result addUser(@RequestBody User user) {
        try {
            userService.addUser(user);
            return Result.success("用户添加成功");
        } catch (Exception e) {
            return Result.error("500", "添加用户失败：" + e.getMessage());
        }
    }

    /**
     * 更新用户 - 仅管理员权限
     */
    @Authority(AuthorityType.requireAuthority)
    @PutMapping("/update")
    public Result updateUser(@RequestBody User user) {
        try {
            userService.updateUser(user);
            return Result.success("用户更新成功");
        } catch (Exception e) {
            return Result.error("500", "更新用户失败：" + e.getMessage());
        }
    }

    /**
     * 删除用户 - 仅管理员权限
     */
    @Authority(AuthorityType.requireAuthority)
    @DeleteMapping("/delete/{id}")
    public Result deleteUser(@PathVariable Integer id) {
        try {
            userService.deleteUser(id);
            return Result.success("用户删除成功");
        } catch (Exception e) {
            return Result.error("500", "删除用户失败：" + e.getMessage());
        }
    }

}
