package com.cn.controller;

import com.cn.annotation.Authority;
import com.cn.common.Result;
import com.cn.entity.AuthorityType;
import com.cn.entity.User;
import com.cn.service.UserService;
import com.cn.utils.TokenUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Authority(AuthorityType.noRequire)
@RestController
@RequestMapping("/userAPI")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 登录接口
     * @param user
     * @return
     */
    @PostMapping("/login")
    public Result login(@RequestBody User user) {
        try {
            User loginUser = userService.login(user.getUsername(), user.getPassword());
            if (loginUser != null) {
                // 生成token
                String token = TokenUtils.genToken(loginUser.getId().toString(), loginUser.getUsername());

                // 清除密码信息，避免返回给前端
                loginUser.setPassword(null);

                Map<String, Object> data = new HashMap<>();
                data.put("user", loginUser);
                data.put("token", token);

                return Result.success(data);
            } else {
                return Result.error("401", "用户名或密码错误");
            }
        } catch (Exception e) {
            return Result.error("500", "登录失败：" + e.getMessage());
        }
    }

    /**
     * 注册接口
     * @param user
     * @return
     */
    @PostMapping("/register")
    public Result register(@RequestBody User user) {
        try {
            userService.addUser(user);
            return Result.success("注册成功");
        } catch (Exception e) {
            return Result.error("500", "注册失败：" + e.getMessage());
        }
    }

    /**
     * 测试接口 - 查看数据库中的用户数据
     * @return
     */
    @GetMapping("/test")
    public Result testUsers() {
        try {
            List<User> users = userService.queryALL();
            System.out.println("数据库中的用户数量: " + users.size());
            for (User u : users) {
                System.out.println("用户: " + u.getUsername() + ", 密码: " + u.getPassword() + ", 角色: " + u.getRole());
            }
            return Result.success(users);
        } catch (Exception e) {
            return Result.error("500", "查询失败：" + e.getMessage());
        }
    }



}
