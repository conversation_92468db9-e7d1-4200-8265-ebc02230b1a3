package com.cn.controller;

import com.cn.annotation.Authority;
import com.cn.common.Result;
import com.cn.entity.AuthorityType;
import com.cn.entity.User;
import com.cn.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Authority(AuthorityType.noRequire)
@RestController
@RequestMapping("/userAPI")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 127.0.0.1:9191/userAPI/queryALL
     * @return
     */
    @RequestMapping("/queryALL")
    public Result queryALL(){
        List<User> list =userService.queryALL();
        return Result.success(list);
    }


}
