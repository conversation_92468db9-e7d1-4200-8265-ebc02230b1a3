{"ast": null, "code": "import lodash from './wrapperLodash.js';\n\n/**\n * Creates a `lodash` wrapper instance that wraps `value` with explicit method\n * chain sequences enabled. The result of such sequences must be unwrapped\n * with `_#value`.\n *\n * @static\n * @memberOf _\n * @since 1.3.0\n * @category Seq\n * @param {*} value The value to wrap.\n * @returns {Object} Returns the new `lodash` wrapper instance.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'age': 36 },\n *   { 'user': 'fred',    'age': 40 },\n *   { 'user': 'pebbles', 'age': 1 }\n * ];\n *\n * var youngest = _\n *   .chain(users)\n *   .sortBy('age')\n *   .map(function(o) {\n *     return o.user + ' is ' + o.age;\n *   })\n *   .head()\n *   .value();\n * // => 'pebbles is 1'\n */\nfunction chain(value) {\n  var result = lodash(value);\n  result.__chain__ = true;\n  return result;\n}\nexport default chain;", "map": {"version": 3, "names": ["lodash", "chain", "value", "result", "__chain__"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/chain.js"], "sourcesContent": ["import lodash from './wrapperLodash.js';\n\n/**\n * Creates a `lodash` wrapper instance that wraps `value` with explicit method\n * chain sequences enabled. The result of such sequences must be unwrapped\n * with `_#value`.\n *\n * @static\n * @memberOf _\n * @since 1.3.0\n * @category Seq\n * @param {*} value The value to wrap.\n * @returns {Object} Returns the new `lodash` wrapper instance.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'age': 36 },\n *   { 'user': 'fred',    'age': 40 },\n *   { 'user': 'pebbles', 'age': 1 }\n * ];\n *\n * var youngest = _\n *   .chain(users)\n *   .sortBy('age')\n *   .map(function(o) {\n *     return o.user + ' is ' + o.age;\n *   })\n *   .head()\n *   .value();\n * // => 'pebbles is 1'\n */\nfunction chain(value) {\n  var result = lodash(value);\n  result.__chain__ = true;\n  return result;\n}\n\nexport default chain;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAE;EACpB,IAAIC,MAAM,GAAGH,MAAM,CAACE,KAAK,CAAC;EAC1BC,MAAM,CAACC,SAAS,GAAG,IAAI;EACvB,OAAOD,MAAM;AACf;AAEA,eAAeF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}