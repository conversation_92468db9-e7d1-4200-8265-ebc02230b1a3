import { createRouter, createWebHistory } from 'vue-router'
import Goods from '../views/goods.vue'
import Category from '../views/category.vue'
import Login from '../views/Login.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/',
    name: 'Goods',
    component: Goods,
    meta: { requiresAuth: true }
  },
  {
    path: '/goods',
    name: 'GoodsPage',
    component: Goods,
    meta: { requiresAuth: true }
  },
  {
    path: '/category',
    name: 'Category',
    component: Category,
    meta: { requiresAuth: true }
  },

]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true'

  // 如果访问登录页面且已登录，重定向到首页
  if (to.name === 'Login' && isLoggedIn) {
    next({ name: 'Goods' })
    return
  }

  // 如果需要登录但未登录，重定向到登录页
  if (to.meta.requiresAuth && !isLoggedIn) {
    next({ name: 'Login' })
    return
  }

  next()
})

export default router