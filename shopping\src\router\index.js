import { createRouter, createWebHistory } from 'vue-router'
import Goods from '../views/goods.vue'
import Category from '../views/category.vue'
import Login from '../views/Login.vue' // 👈 新增：导入登录组件

const routes = [
  {
    path: '/',
    name: 'Goods',
    component: Goods
  },
  {
    path: '/goods',
    name: 'GoodsPage',
    component: Goods
  },
  {
    path: '/category',
    name: 'Category',
    component: Category
  },

]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router