{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, onMounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage, ElMessageBox, ElNotification } from 'element-plus';\nexport default {\n  __name: 'Admin',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const router = useRouter();\n\n    // 响应式数据\n    const username = ref(localStorage.getItem('username') || '');\n    const users = ref([]);\n    const loading = ref(false);\n    const dialogVisible = ref(false);\n    const dialogTitle = ref('添加用户');\n    const isEdit = ref(false);\n    const saveLoading = ref(false);\n\n    // 表单数据\n    const userForm = ref({\n      id: null,\n      username: '',\n      password: '',\n      nickname: '',\n      email: '',\n      phone: '',\n      address: '',\n      role: 'user'\n    });\n    const userFormRef = ref();\n\n    // 表单验证规则\n    const userRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }, {\n        min: 3,\n        max: 20,\n        message: '用户名长度为3-20个字符',\n        trigger: 'blur'\n      }],\n      password: [{\n        required: true,\n        message: '请输入密码',\n        trigger: 'blur'\n      }, {\n        min: 6,\n        max: 20,\n        message: '密码长度为6-20个字符',\n        trigger: 'blur'\n      }],\n      nickname: [{\n        required: true,\n        message: '请输入昵称',\n        trigger: 'blur'\n      }],\n      email: [{\n        type: 'email',\n        message: '请输入正确的邮箱地址',\n        trigger: 'blur'\n      }],\n      role: [{\n        required: true,\n        message: '请选择角色',\n        trigger: 'change'\n      }]\n    };\n\n    // 获取token\n    const getToken = () => localStorage.getItem('token');\n\n    // 获取用户列表\n    const fetchUsers = async () => {\n      loading.value = true;\n      console.log('开始获取用户列表...');\n      console.log('Token:', getToken());\n      try {\n        const response = await fetch('http://localhost:9192/userAPI/queryALL', {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n            'token': getToken()\n          }\n        });\n        console.log('响应状态:', response.status);\n        console.log('响应头:', response.headers);\n\n        // 先获取原始文本\n        const responseText = await response.text();\n        console.log('原始响应内容:', responseText);\n        if (!responseText) {\n          throw new Error('服务器返回空响应');\n        }\n\n        // 尝试解析JSON\n        let result;\n        try {\n          result = JSON.parse(responseText);\n          console.log('解析后的JSON:', result);\n        } catch (jsonError) {\n          console.error('JSON解析失败:', jsonError);\n          console.log('无法解析的响应内容:', responseText);\n          throw new Error('服务器返回的数据格式错误');\n        }\n        if (result.code === '200') {\n          users.value = result.data || [];\n          console.log('用户列表:', users.value);\n          if (users.value.length === 0) {\n            ElMessage.info('暂无用户数据');\n          } else {\n            ElMessage.success(`成功加载 ${users.value.length} 个用户`);\n          }\n        } else {\n          console.error('API返回错误:', result);\n          ElMessage.error(result.msg || '获取用户列表失败');\n        }\n      } catch (error) {\n        console.error('获取用户列表失败:', error);\n        ElMessage.error(`网络错误：${error.message}`);\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 刷新用户列表\n    const refreshUsers = () => {\n      fetchUsers();\n    };\n\n    // 显示添加用户对话框\n    const showAddDialog = () => {\n      dialogTitle.value = '添加用户';\n      isEdit.value = false;\n      userForm.value = {\n        id: null,\n        username: '',\n        password: '',\n        nickname: '',\n        email: '',\n        phone: '',\n        address: '',\n        role: 'user'\n      };\n      dialogVisible.value = true;\n    };\n\n    // 编辑用户\n    const editUser = user => {\n      dialogTitle.value = '编辑用户';\n      isEdit.value = true;\n      userForm.value = {\n        ...user\n      };\n      dialogVisible.value = true;\n    };\n\n    // 保存用户\n    const saveUser = async () => {\n      if (!userFormRef.value) return;\n      await userFormRef.value.validate(async valid => {\n        if (valid) {\n          saveLoading.value = true;\n          try {\n            const url = isEdit.value ? 'http://localhost:9192/userAPI/update' : 'http://localhost:9192/userAPI/add';\n            const method = isEdit.value ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n              method,\n              headers: {\n                'Content-Type': 'application/json',\n                'token': getToken()\n              },\n              body: JSON.stringify(userForm.value)\n            });\n            const result = await response.json();\n            if (result.code === '200') {\n              ElNotification({\n                title: '成功',\n                message: isEdit.value ? '用户更新成功' : '用户添加成功',\n                type: 'success'\n              });\n              dialogVisible.value = false;\n              fetchUsers();\n            } else {\n              ElMessage.error(result.msg || '操作失败');\n            }\n          } catch (error) {\n            console.error('保存用户失败:', error);\n            ElMessage.error('网络错误，请稍后重试');\n          } finally {\n            saveLoading.value = false;\n          }\n        }\n      });\n    };\n\n    // 删除用户\n    const deleteUser = async user => {\n      if (user.username === 'root') {\n        ElMessage.warning('不能删除root用户');\n        return;\n      }\n      try {\n        await ElMessageBox.confirm(`确定要删除用户 \"${user.username}\" 吗？`, '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        const response = await fetch(`http://localhost:9192/userAPI/delete/${user.id}`, {\n          method: 'DELETE',\n          headers: {\n            'token': getToken()\n          }\n        });\n        const result = await response.json();\n        if (result.code === '200') {\n          ElNotification({\n            title: '成功',\n            message: '用户删除成功',\n            type: 'success'\n          });\n          fetchUsers();\n        } else {\n          ElMessage.error(result.msg || '删除失败');\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除用户失败:', error);\n          ElMessage.error('网络错误，请稍后重试');\n        }\n      }\n    };\n\n    // 退出登录\n    const logout = () => {\n      localStorage.clear();\n      ElMessage.success('已退出登录');\n      router.push('/login');\n    };\n\n    // 测试后端连接\n    const testBackend = async () => {\n      try {\n        console.log('测试后端连接...');\n        const response = await fetch('http://localhost:9192/userAPI/test');\n        console.log('测试接口HTTP状态:', response.status);\n        if (!response.ok) {\n          console.error('HTTP错误:', response.status, response.statusText);\n          return;\n        }\n        const result = await response.json();\n        console.log('测试接口响应:', result);\n        if (result.code === '200' && result.data && result.data.length > 0) {\n          console.log('后端连接正常，用户数据:', result.data.length, '条');\n        } else {\n          console.warn('后端连接正常但无用户数据');\n        }\n      } catch (error) {\n        console.error('测试后端连接失败:', error);\n        ElMessage.error('无法连接到后端服务，请检查后端是否启动');\n      }\n    };\n\n    // 直接测试API\n    const testDirectAPI = async () => {\n      try {\n        console.log('=== 直接测试API ===');\n\n        // 测试test接口\n        console.log('正在调用test接口...');\n        const testResponse = await fetch('http://localhost:9192/userAPI/test');\n        console.log('Test接口响应状态:', testResponse.status);\n        console.log('Test接口响应头:', testResponse.headers);\n        const testText = await testResponse.text();\n        console.log('Test接口原始响应:', testText);\n        if (testText) {\n          try {\n            const testResult = JSON.parse(testText);\n            console.log('Test接口JSON结果:', testResult);\n          } catch (jsonError) {\n            console.error('Test接口JSON解析失败:', jsonError);\n            console.log('响应内容不是有效JSON:', testText);\n          }\n        } else {\n          console.error('Test接口返回空响应');\n        }\n\n        // 测试queryALL接口\n        console.log('正在调用queryALL接口...');\n        const queryResponse = await fetch('http://localhost:9192/userAPI/queryALL');\n        console.log('QueryALL接口响应状态:', queryResponse.status);\n        const queryText = await queryResponse.text();\n        console.log('QueryALL接口原始响应:', queryText);\n        if (queryText) {\n          try {\n            const queryResult = JSON.parse(queryText);\n            console.log('QueryALL接口JSON结果:', queryResult);\n            if (queryResult.code === '200') {\n              users.value = queryResult.data || [];\n              ElMessage.success(`成功获取 ${users.value.length} 个用户`);\n            } else {\n              ElMessage.error(`API返回错误: ${queryResult.msg}`);\n            }\n          } catch (jsonError) {\n            console.error('QueryALL接口JSON解析失败:', jsonError);\n            console.log('响应内容不是有效JSON:', queryText);\n            ElMessage.error('服务器返回的数据格式错误');\n          }\n        } else {\n          console.error('QueryALL接口返回空响应');\n          ElMessage.error('服务器返回空响应');\n        }\n      } catch (error) {\n        console.error('直接测试API失败:', error);\n        ElMessage.error(`API测试失败: ${error.message}`);\n      }\n    };\n\n    // 组件挂载时获取用户列表\n    onMounted(() => {\n      console.log('Admin组件已挂载');\n      testBackend();\n      fetchUsers();\n    });\n    const __returned__ = {\n      router,\n      username,\n      users,\n      loading,\n      dialogVisible,\n      dialogTitle,\n      isEdit,\n      saveLoading,\n      userForm,\n      userFormRef,\n      userRules,\n      getToken,\n      fetchUsers,\n      refreshUsers,\n      showAddDialog,\n      editUser,\n      saveUser,\n      deleteUser,\n      logout,\n      testBackend,\n      testDirectAPI,\n      ref,\n      onMounted,\n      get useRouter() {\n        return useRouter;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get ElNotification() {\n        return ElNotification;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "useRouter", "ElMessage", "ElMessageBox", "ElNotification", "router", "username", "localStorage", "getItem", "users", "loading", "dialogVisible", "dialogTitle", "isEdit", "saveLoading", "userForm", "id", "password", "nickname", "email", "phone", "address", "role", "userFormRef", "userRules", "required", "message", "trigger", "min", "max", "type", "getToken", "fetchUsers", "value", "console", "log", "response", "fetch", "method", "headers", "status", "responseText", "text", "Error", "result", "JSON", "parse", "jsonError", "error", "code", "data", "length", "info", "success", "msg", "refreshUsers", "showAddDialog", "editUser", "user", "saveUser", "validate", "valid", "url", "body", "stringify", "json", "title", "deleteUser", "warning", "confirm", "confirmButtonText", "cancelButtonText", "logout", "clear", "push", "testBackend", "ok", "statusText", "warn", "testDirectAPI", "testResponse", "testText", "testResult", "queryResponse", "queryText", "query<PERSON><PERSON>ult"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/src/views/Admin.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-container\">\n    <div class=\"admin-header\">\n      <h1>🛠️ 后台管理系统</h1>\n      <div class=\"header-actions\">\n        <span>欢迎，{{ username }}</span>\n        <el-button type=\"danger\" @click=\"logout\">退出登录</el-button>\n      </div>\n    </div>\n\n    <div class=\"admin-content\">\n      <div class=\"toolbar\">\n        <el-button type=\"primary\" @click=\"showAddDialog\">添加用户</el-button>\n        <el-button @click=\"refreshUsers\">刷新</el-button>\n        <el-button type=\"success\" @click=\"testDirectAPI\">直接测试API</el-button>\n      </div>\n\n      <!-- 用户列表表格 -->\n      <el-table :data=\"users\" style=\"width: 100%\" v-loading=\"loading\">\n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\" />\n        <el-table-column prop=\"username\" label=\"用户名\" width=\"120\" />\n        <el-table-column prop=\"nickname\" label=\"昵称\" width=\"120\" />\n        <el-table-column prop=\"email\" label=\"邮箱\" width=\"180\" />\n        <el-table-column prop=\"phone\" label=\"电话\" width=\"120\" />\n        <el-table-column prop=\"role\" label=\"角色\" width=\"100\">\n          <template #default=\"scope\">\n            <el-tag :type=\"scope.row.role === 'admin' ? 'danger' : 'primary'\">\n              {{ scope.row.role === 'admin' ? '管理员' : '普通用户' }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"address\" label=\"地址\" />\n        <el-table-column label=\"操作\" width=\"180\">\n          <template #default=\"scope\">\n            <el-button size=\"small\" @click=\"editUser(scope.row)\">编辑</el-button>\n            <el-button \n              size=\"small\" \n              type=\"danger\" \n              @click=\"deleteUser(scope.row)\"\n              :disabled=\"scope.row.username === 'root'\"\n            >\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <!-- 添加/编辑用户对话框 -->\n    <el-dialog\n      :title=\"dialogTitle\"\n      v-model=\"dialogVisible\"\n      width=\"500px\"\n    >\n      <el-form\n        ref=\"userFormRef\"\n        :model=\"userForm\"\n        :rules=\"userRules\"\n        label-width=\"80px\"\n      >\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"userForm.username\" :disabled=\"isEdit\" />\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\" v-if=\"!isEdit\">\n          <el-input v-model=\"userForm.password\" type=\"password\" />\n        </el-form-item>\n        <el-form-item label=\"昵称\" prop=\"nickname\">\n          <el-input v-model=\"userForm.nickname\" />\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"userForm.email\" />\n        </el-form-item>\n        <el-form-item label=\"电话\" prop=\"phone\">\n          <el-input v-model=\"userForm.phone\" />\n        </el-form-item>\n        <el-form-item label=\"地址\" prop=\"address\">\n          <el-input v-model=\"userForm.address\" />\n        </el-form-item>\n        <el-form-item label=\"角色\" prop=\"role\">\n          <el-select v-model=\"userForm.role\" style=\"width: 100%\">\n            <el-option label=\"普通用户\" value=\"user\" />\n            <el-option label=\"管理员\" value=\"admin\" />\n          </el-select>\n        </el-form-item>\n      </el-form>\n      \n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveUser\" :loading=\"saveLoading\">\n            {{ isEdit ? '更新' : '添加' }}\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage, ElMessageBox, ElNotification } from 'element-plus'\n\nconst router = useRouter()\n\n// 响应式数据\nconst username = ref(localStorage.getItem('username') || '')\nconst users = ref([])\nconst loading = ref(false)\nconst dialogVisible = ref(false)\nconst dialogTitle = ref('添加用户')\nconst isEdit = ref(false)\nconst saveLoading = ref(false)\n\n// 表单数据\nconst userForm = ref({\n  id: null,\n  username: '',\n  password: '',\n  nickname: '',\n  email: '',\n  phone: '',\n  address: '',\n  role: 'user'\n})\n\nconst userFormRef = ref()\n\n// 表单验证规则\nconst userRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' },\n    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }\n  ],\n  nickname: [\n    { required: true, message: '请输入昵称', trigger: 'blur' }\n  ],\n  email: [\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\n  ],\n  role: [\n    { required: true, message: '请选择角色', trigger: 'change' }\n  ]\n}\n\n// 获取token\nconst getToken = () => localStorage.getItem('token')\n\n// 获取用户列表\nconst fetchUsers = async () => {\n  loading.value = true\n  console.log('开始获取用户列表...')\n  console.log('Token:', getToken())\n\n  try {\n    const response = await fetch('http://localhost:9192/userAPI/queryALL', {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        'token': getToken()\n      }\n    })\n\n    console.log('响应状态:', response.status)\n    console.log('响应头:', response.headers)\n\n    // 先获取原始文本\n    const responseText = await response.text()\n    console.log('原始响应内容:', responseText)\n\n    if (!responseText) {\n      throw new Error('服务器返回空响应')\n    }\n\n    // 尝试解析JSON\n    let result\n    try {\n      result = JSON.parse(responseText)\n      console.log('解析后的JSON:', result)\n    } catch (jsonError) {\n      console.error('JSON解析失败:', jsonError)\n      console.log('无法解析的响应内容:', responseText)\n      throw new Error('服务器返回的数据格式错误')\n    }\n\n    if (result.code === '200') {\n      users.value = result.data || []\n      console.log('用户列表:', users.value)\n      if (users.value.length === 0) {\n        ElMessage.info('暂无用户数据')\n      } else {\n        ElMessage.success(`成功加载 ${users.value.length} 个用户`)\n      }\n    } else {\n      console.error('API返回错误:', result)\n      ElMessage.error(result.msg || '获取用户列表失败')\n    }\n  } catch (error) {\n    console.error('获取用户列表失败:', error)\n    ElMessage.error(`网络错误：${error.message}`)\n  } finally {\n    loading.value = false\n  }\n}\n\n// 刷新用户列表\nconst refreshUsers = () => {\n  fetchUsers()\n}\n\n// 显示添加用户对话框\nconst showAddDialog = () => {\n  dialogTitle.value = '添加用户'\n  isEdit.value = false\n  userForm.value = {\n    id: null,\n    username: '',\n    password: '',\n    nickname: '',\n    email: '',\n    phone: '',\n    address: '',\n    role: 'user'\n  }\n  dialogVisible.value = true\n}\n\n// 编辑用户\nconst editUser = (user) => {\n  dialogTitle.value = '编辑用户'\n  isEdit.value = true\n  userForm.value = { ...user }\n  dialogVisible.value = true\n}\n\n// 保存用户\nconst saveUser = async () => {\n  if (!userFormRef.value) return\n  \n  await userFormRef.value.validate(async (valid) => {\n    if (valid) {\n      saveLoading.value = true\n      try {\n        const url = isEdit.value \n          ? 'http://localhost:9192/userAPI/update'\n          : 'http://localhost:9192/userAPI/add'\n        \n        const method = isEdit.value ? 'PUT' : 'POST'\n        \n        const response = await fetch(url, {\n          method,\n          headers: {\n            'Content-Type': 'application/json',\n            'token': getToken()\n          },\n          body: JSON.stringify(userForm.value)\n        })\n        \n        const result = await response.json()\n        \n        if (result.code === '200') {\n          ElNotification({\n            title: '成功',\n            message: isEdit.value ? '用户更新成功' : '用户添加成功',\n            type: 'success'\n          })\n          dialogVisible.value = false\n          fetchUsers()\n        } else {\n          ElMessage.error(result.msg || '操作失败')\n        }\n      } catch (error) {\n        console.error('保存用户失败:', error)\n        ElMessage.error('网络错误，请稍后重试')\n      } finally {\n        saveLoading.value = false\n      }\n    }\n  })\n}\n\n// 删除用户\nconst deleteUser = async (user) => {\n  if (user.username === 'root') {\n    ElMessage.warning('不能删除root用户')\n    return\n  }\n  \n  try {\n    await ElMessageBox.confirm(\n      `确定要删除用户 \"${user.username}\" 吗？`,\n      '确认删除',\n      {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning',\n      }\n    )\n    \n    const response = await fetch(`http://localhost:9192/userAPI/delete/${user.id}`, {\n      method: 'DELETE',\n      headers: {\n        'token': getToken()\n      }\n    })\n    \n    const result = await response.json()\n    \n    if (result.code === '200') {\n      ElNotification({\n        title: '成功',\n        message: '用户删除成功',\n        type: 'success'\n      })\n      fetchUsers()\n    } else {\n      ElMessage.error(result.msg || '删除失败')\n    }\n  } catch (error) {\n    if (error !== 'cancel') {\n      console.error('删除用户失败:', error)\n      ElMessage.error('网络错误，请稍后重试')\n    }\n  }\n}\n\n// 退出登录\nconst logout = () => {\n  localStorage.clear()\n  ElMessage.success('已退出登录')\n  router.push('/login')\n}\n\n// 测试后端连接\nconst testBackend = async () => {\n  try {\n    console.log('测试后端连接...')\n    const response = await fetch('http://localhost:9192/userAPI/test')\n    console.log('测试接口HTTP状态:', response.status)\n\n    if (!response.ok) {\n      console.error('HTTP错误:', response.status, response.statusText)\n      return\n    }\n\n    const result = await response.json()\n    console.log('测试接口响应:', result)\n\n    if (result.code === '200' && result.data && result.data.length > 0) {\n      console.log('后端连接正常，用户数据:', result.data.length, '条')\n    } else {\n      console.warn('后端连接正常但无用户数据')\n    }\n  } catch (error) {\n    console.error('测试后端连接失败:', error)\n    ElMessage.error('无法连接到后端服务，请检查后端是否启动')\n  }\n}\n\n// 直接测试API\nconst testDirectAPI = async () => {\n  try {\n    console.log('=== 直接测试API ===')\n\n    // 测试test接口\n    console.log('正在调用test接口...')\n    const testResponse = await fetch('http://localhost:9192/userAPI/test')\n    console.log('Test接口响应状态:', testResponse.status)\n    console.log('Test接口响应头:', testResponse.headers)\n\n    const testText = await testResponse.text()\n    console.log('Test接口原始响应:', testText)\n\n    if (testText) {\n      try {\n        const testResult = JSON.parse(testText)\n        console.log('Test接口JSON结果:', testResult)\n      } catch (jsonError) {\n        console.error('Test接口JSON解析失败:', jsonError)\n        console.log('响应内容不是有效JSON:', testText)\n      }\n    } else {\n      console.error('Test接口返回空响应')\n    }\n\n    // 测试queryALL接口\n    console.log('正在调用queryALL接口...')\n    const queryResponse = await fetch('http://localhost:9192/userAPI/queryALL')\n    console.log('QueryALL接口响应状态:', queryResponse.status)\n\n    const queryText = await queryResponse.text()\n    console.log('QueryALL接口原始响应:', queryText)\n\n    if (queryText) {\n      try {\n        const queryResult = JSON.parse(queryText)\n        console.log('QueryALL接口JSON结果:', queryResult)\n\n        if (queryResult.code === '200') {\n          users.value = queryResult.data || []\n          ElMessage.success(`成功获取 ${users.value.length} 个用户`)\n        } else {\n          ElMessage.error(`API返回错误: ${queryResult.msg}`)\n        }\n      } catch (jsonError) {\n        console.error('QueryALL接口JSON解析失败:', jsonError)\n        console.log('响应内容不是有效JSON:', queryText)\n        ElMessage.error('服务器返回的数据格式错误')\n      }\n    } else {\n      console.error('QueryALL接口返回空响应')\n      ElMessage.error('服务器返回空响应')\n    }\n\n  } catch (error) {\n    console.error('直接测试API失败:', error)\n    ElMessage.error(`API测试失败: ${error.message}`)\n  }\n}\n\n// 组件挂载时获取用户列表\nonMounted(() => {\n  console.log('Admin组件已挂载')\n  testBackend()\n  fetchUsers()\n})\n</script>\n\n<style scoped>\n.admin-container {\n  padding: 20px;\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.admin-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.admin-header h1 {\n  margin: 0;\n  color: #333;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.admin-content {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.toolbar {\n  margin-bottom: 20px;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n</style>\n"], "mappings": ";AAmGA,SAASA,GAAG,EAAEC,SAAS,QAAQ,KAAI;AACnC,SAASC,SAAS,QAAQ,YAAW;AACrC,SAASC,SAAS,EAAEC,YAAY,EAAEC,cAAc,QAAQ,cAAa;;;;;;;IAErE,MAAMC,MAAM,GAAGJ,SAAS,CAAC;;IAEzB;IACA,MAAMK,QAAQ,GAAGP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE;IAC3D,MAAMC,KAAK,GAAGV,GAAG,CAAC,EAAE;IACpB,MAAMW,OAAO,GAAGX,GAAG,CAAC,KAAK;IACzB,MAAMY,aAAa,GAAGZ,GAAG,CAAC,KAAK;IAC/B,MAAMa,WAAW,GAAGb,GAAG,CAAC,MAAM;IAC9B,MAAMc,MAAM,GAAGd,GAAG,CAAC,KAAK;IACxB,MAAMe,WAAW,GAAGf,GAAG,CAAC,KAAK;;IAE7B;IACA,MAAMgB,QAAQ,GAAGhB,GAAG,CAAC;MACnBiB,EAAE,EAAE,IAAI;MACRV,QAAQ,EAAE,EAAE;MACZW,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE;IACR,CAAC;IAED,MAAMC,WAAW,GAAGxB,GAAG,CAAC;;IAExB;IACA,MAAMyB,SAAS,GAAG;MAChBlB,QAAQ,EAAE,CACR;QAAEmB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,eAAe;QAAEC,OAAO,EAAE;MAAO,EAC9D;MACDV,QAAQ,EAAE,CACR;QAAEQ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,cAAc;QAAEC,OAAO,EAAE;MAAO,EAC7D;MACDT,QAAQ,EAAE,CACR;QAAEO,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,EACrD;MACDR,KAAK,EAAE,CACL;QAAEW,IAAI,EAAE,OAAO;QAAEJ,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,EACzD;MACDL,IAAI,EAAE,CACJ;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS;IAE1D;;IAEA;IACA,MAAMI,QAAQ,GAAGA,CAAA,KAAMxB,YAAY,CAACC,OAAO,CAAC,OAAO;;IAEnD;IACA,MAAMwB,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7BtB,OAAO,CAACuB,KAAK,GAAG,IAAG;MACnBC,OAAO,CAACC,GAAG,CAAC,aAAa;MACzBD,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEJ,QAAQ,CAAC,CAAC;MAEhC,IAAI;QACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwC,EAAE;UACrEC,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,OAAO,EAAER,QAAQ,CAAC;UACpB;QACF,CAAC;QAEDG,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEC,QAAQ,CAACI,MAAM;QACpCN,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEC,QAAQ,CAACG,OAAO;;QAEpC;QACA,MAAME,YAAY,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC;QACzCR,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEM,YAAY;QAEnC,IAAI,CAACA,YAAY,EAAE;UACjB,MAAM,IAAIE,KAAK,CAAC,UAAU;QAC5B;;QAEA;QACA,IAAIC,MAAK;QACT,IAAI;UACFA,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,YAAY;UAChCP,OAAO,CAACC,GAAG,CAAC,WAAW,EAAES,MAAM;QACjC,CAAC,CAAC,OAAOG,SAAS,EAAE;UAClBb,OAAO,CAACc,KAAK,CAAC,WAAW,EAAED,SAAS;UACpCb,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEM,YAAY;UACtC,MAAM,IAAIE,KAAK,CAAC,cAAc;QAChC;QAEA,IAAIC,MAAM,CAACK,IAAI,KAAK,KAAK,EAAE;UACzBxC,KAAK,CAACwB,KAAK,GAAGW,MAAM,CAACM,IAAI,IAAI,EAAC;UAC9BhB,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE1B,KAAK,CAACwB,KAAK;UAChC,IAAIxB,KAAK,CAACwB,KAAK,CAACkB,MAAM,KAAK,CAAC,EAAE;YAC5BjD,SAAS,CAACkD,IAAI,CAAC,QAAQ;UACzB,CAAC,MAAM;YACLlD,SAAS,CAACmD,OAAO,CAAC,QAAQ5C,KAAK,CAACwB,KAAK,CAACkB,MAAM,MAAM;UACpD;QACF,CAAC,MAAM;UACLjB,OAAO,CAACc,KAAK,CAAC,UAAU,EAAEJ,MAAM;UAChC1C,SAAS,CAAC8C,KAAK,CAACJ,MAAM,CAACU,GAAG,IAAI,UAAU;QAC1C;MACF,CAAC,CAAC,OAAON,KAAK,EAAE;QACdd,OAAO,CAACc,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC9C,SAAS,CAAC8C,KAAK,CAAC,QAAQA,KAAK,CAACtB,OAAO,EAAE;MACzC,CAAC,SAAS;QACRhB,OAAO,CAACuB,KAAK,GAAG,KAAI;MACtB;IACF;;IAEA;IACA,MAAMsB,YAAY,GAAGA,CAAA,KAAM;MACzBvB,UAAU,CAAC;IACb;;IAEA;IACA,MAAMwB,aAAa,GAAGA,CAAA,KAAM;MAC1B5C,WAAW,CAACqB,KAAK,GAAG,MAAK;MACzBpB,MAAM,CAACoB,KAAK,GAAG,KAAI;MACnBlB,QAAQ,CAACkB,KAAK,GAAG;QACfjB,EAAE,EAAE,IAAI;QACRV,QAAQ,EAAE,EAAE;QACZW,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXC,IAAI,EAAE;MACR;MACAX,aAAa,CAACsB,KAAK,GAAG,IAAG;IAC3B;;IAEA;IACA,MAAMwB,QAAQ,GAAIC,IAAI,IAAK;MACzB9C,WAAW,CAACqB,KAAK,GAAG,MAAK;MACzBpB,MAAM,CAACoB,KAAK,GAAG,IAAG;MAClBlB,QAAQ,CAACkB,KAAK,GAAG;QAAE,GAAGyB;MAAK;MAC3B/C,aAAa,CAACsB,KAAK,GAAG,IAAG;IAC3B;;IAEA;IACA,MAAM0B,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI,CAACpC,WAAW,CAACU,KAAK,EAAE;MAExB,MAAMV,WAAW,CAACU,KAAK,CAAC2B,QAAQ,CAAC,MAAOC,KAAK,IAAK;QAChD,IAAIA,KAAK,EAAE;UACT/C,WAAW,CAACmB,KAAK,GAAG,IAAG;UACvB,IAAI;YACF,MAAM6B,GAAG,GAAGjD,MAAM,CAACoB,KAAK,GACpB,sCAAqC,GACrC,mCAAkC;YAEtC,MAAMK,MAAM,GAAGzB,MAAM,CAACoB,KAAK,GAAG,KAAK,GAAG,MAAK;YAE3C,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAACyB,GAAG,EAAE;cAChCxB,MAAM;cACNC,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAER,QAAQ,CAAC;cACpB,CAAC;cACDgC,IAAI,EAAElB,IAAI,CAACmB,SAAS,CAACjD,QAAQ,CAACkB,KAAK;YACrC,CAAC;YAED,MAAMW,MAAM,GAAG,MAAMR,QAAQ,CAAC6B,IAAI,CAAC;YAEnC,IAAIrB,MAAM,CAACK,IAAI,KAAK,KAAK,EAAE;cACzB7C,cAAc,CAAC;gBACb8D,KAAK,EAAE,IAAI;gBACXxC,OAAO,EAAEb,MAAM,CAACoB,KAAK,GAAG,QAAQ,GAAG,QAAQ;gBAC3CH,IAAI,EAAE;cACR,CAAC;cACDnB,aAAa,CAACsB,KAAK,GAAG,KAAI;cAC1BD,UAAU,CAAC;YACb,CAAC,MAAM;cACL9B,SAAS,CAAC8C,KAAK,CAACJ,MAAM,CAACU,GAAG,IAAI,MAAM;YACtC;UACF,CAAC,CAAC,OAAON,KAAK,EAAE;YACdd,OAAO,CAACc,KAAK,CAAC,SAAS,EAAEA,KAAK;YAC9B9C,SAAS,CAAC8C,KAAK,CAAC,YAAY;UAC9B,CAAC,SAAS;YACRlC,WAAW,CAACmB,KAAK,GAAG,KAAI;UAC1B;QACF;MACF,CAAC;IACH;;IAEA;IACA,MAAMkC,UAAU,GAAG,MAAOT,IAAI,IAAK;MACjC,IAAIA,IAAI,CAACpD,QAAQ,KAAK,MAAM,EAAE;QAC5BJ,SAAS,CAACkE,OAAO,CAAC,YAAY;QAC9B;MACF;MAEA,IAAI;QACF,MAAMjE,YAAY,CAACkE,OAAO,CACxB,YAAYX,IAAI,CAACpD,QAAQ,MAAM,EAC/B,MAAM,EACN;UACEgE,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBzC,IAAI,EAAE;QACR,CACF;QAEA,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwCqB,IAAI,CAAC1C,EAAE,EAAE,EAAE;UAC9EsB,MAAM,EAAE,QAAQ;UAChBC,OAAO,EAAE;YACP,OAAO,EAAER,QAAQ,CAAC;UACpB;QACF,CAAC;QAED,MAAMa,MAAM,GAAG,MAAMR,QAAQ,CAAC6B,IAAI,CAAC;QAEnC,IAAIrB,MAAM,CAACK,IAAI,KAAK,KAAK,EAAE;UACzB7C,cAAc,CAAC;YACb8D,KAAK,EAAE,IAAI;YACXxC,OAAO,EAAE,QAAQ;YACjBI,IAAI,EAAE;UACR,CAAC;UACDE,UAAU,CAAC;QACb,CAAC,MAAM;UACL9B,SAAS,CAAC8C,KAAK,CAACJ,MAAM,CAACU,GAAG,IAAI,MAAM;QACtC;MACF,CAAC,CAAC,OAAON,KAAK,EAAE;QACd,IAAIA,KAAK,KAAK,QAAQ,EAAE;UACtBd,OAAO,CAACc,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9B9C,SAAS,CAAC8C,KAAK,CAAC,YAAY;QAC9B;MACF;IACF;;IAEA;IACA,MAAMwB,MAAM,GAAGA,CAAA,KAAM;MACnBjE,YAAY,CAACkE,KAAK,CAAC;MACnBvE,SAAS,CAACmD,OAAO,CAAC,OAAO;MACzBhD,MAAM,CAACqE,IAAI,CAAC,QAAQ;IACtB;;IAEA;IACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFzC,OAAO,CAACC,GAAG,CAAC,WAAW;QACvB,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,oCAAoC;QACjEH,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,QAAQ,CAACI,MAAM;QAE1C,IAAI,CAACJ,QAAQ,CAACwC,EAAE,EAAE;UAChB1C,OAAO,CAACc,KAAK,CAAC,SAAS,EAAEZ,QAAQ,CAACI,MAAM,EAAEJ,QAAQ,CAACyC,UAAU;UAC7D;QACF;QAEA,MAAMjC,MAAM,GAAG,MAAMR,QAAQ,CAAC6B,IAAI,CAAC;QACnC/B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAES,MAAM;QAE7B,IAAIA,MAAM,CAACK,IAAI,KAAK,KAAK,IAAIL,MAAM,CAACM,IAAI,IAAIN,MAAM,CAACM,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;UAClEjB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAES,MAAM,CAACM,IAAI,CAACC,MAAM,EAAE,GAAG;QACrD,CAAC,MAAM;UACLjB,OAAO,CAAC4C,IAAI,CAAC,cAAc;QAC7B;MACF,CAAC,CAAC,OAAO9B,KAAK,EAAE;QACdd,OAAO,CAACc,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC9C,SAAS,CAAC8C,KAAK,CAAC,qBAAqB;MACvC;IACF;;IAEA;IACA,MAAM+B,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF7C,OAAO,CAACC,GAAG,CAAC,iBAAiB;;QAE7B;QACAD,OAAO,CAACC,GAAG,CAAC,eAAe;QAC3B,MAAM6C,YAAY,GAAG,MAAM3C,KAAK,CAAC,oCAAoC;QACrEH,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE6C,YAAY,CAACxC,MAAM;QAC9CN,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE6C,YAAY,CAACzC,OAAO;QAE9C,MAAM0C,QAAQ,GAAG,MAAMD,YAAY,CAACtC,IAAI,CAAC;QACzCR,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE8C,QAAQ;QAEnC,IAAIA,QAAQ,EAAE;UACZ,IAAI;YACF,MAAMC,UAAU,GAAGrC,IAAI,CAACC,KAAK,CAACmC,QAAQ;YACtC/C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE+C,UAAU;UACzC,CAAC,CAAC,OAAOnC,SAAS,EAAE;YAClBb,OAAO,CAACc,KAAK,CAAC,iBAAiB,EAAED,SAAS;YAC1Cb,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE8C,QAAQ;UACvC;QACF,CAAC,MAAM;UACL/C,OAAO,CAACc,KAAK,CAAC,aAAa;QAC7B;;QAEA;QACAd,OAAO,CAACC,GAAG,CAAC,mBAAmB;QAC/B,MAAMgD,aAAa,GAAG,MAAM9C,KAAK,CAAC,wCAAwC;QAC1EH,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEgD,aAAa,CAAC3C,MAAM;QAEnD,MAAM4C,SAAS,GAAG,MAAMD,aAAa,CAACzC,IAAI,CAAC;QAC3CR,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEiD,SAAS;QAExC,IAAIA,SAAS,EAAE;UACb,IAAI;YACF,MAAMC,WAAW,GAAGxC,IAAI,CAACC,KAAK,CAACsC,SAAS;YACxClD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkD,WAAW;YAE5C,IAAIA,WAAW,CAACpC,IAAI,KAAK,KAAK,EAAE;cAC9BxC,KAAK,CAACwB,KAAK,GAAGoD,WAAW,CAACnC,IAAI,IAAI,EAAC;cACnChD,SAAS,CAACmD,OAAO,CAAC,QAAQ5C,KAAK,CAACwB,KAAK,CAACkB,MAAM,MAAM;YACpD,CAAC,MAAM;cACLjD,SAAS,CAAC8C,KAAK,CAAC,YAAYqC,WAAW,CAAC/B,GAAG,EAAE;YAC/C;UACF,CAAC,CAAC,OAAOP,SAAS,EAAE;YAClBb,OAAO,CAACc,KAAK,CAAC,qBAAqB,EAAED,SAAS;YAC9Cb,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEiD,SAAS;YACtClF,SAAS,CAAC8C,KAAK,CAAC,cAAc;UAChC;QACF,CAAC,MAAM;UACLd,OAAO,CAACc,KAAK,CAAC,iBAAiB;UAC/B9C,SAAS,CAAC8C,KAAK,CAAC,UAAU;QAC5B;MAEF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdd,OAAO,CAACc,KAAK,CAAC,YAAY,EAAEA,KAAK;QACjC9C,SAAS,CAAC8C,KAAK,CAAC,YAAYA,KAAK,CAACtB,OAAO,EAAE;MAC7C;IACF;;IAEA;IACA1B,SAAS,CAAC,MAAM;MACdkC,OAAO,CAACC,GAAG,CAAC,YAAY;MACxBwC,WAAW,CAAC;MACZ3C,UAAU,CAAC;IACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}