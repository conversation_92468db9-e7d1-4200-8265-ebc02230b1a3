<template>
  <div class="goods-container">
    <h2 class="page-title">在线购物商城</h2>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">加载中...</div>

    <!-- 商品列表（卡片样式） -->
    <div v-else-if="goodsList.length > 0" class="goods-list">
      <div class="goods-card" v-for="good in goodsList" :key="good.id">
        <div class="goods-card-header">{{ good.categoryName || '未分类' }}</div>
        <div class="goods-card-title">{{ good.name }}</div>
        <div class="goods-card-price">¥{{ formatPrice(good.price) }}</div>
        <button class="btn-add-cart" @click="addToCart(good)">加入购物车</button>
      </div>
    </div>

    <!-- 无数据 -->
    <div v-else class="no-data">
      暂无商品
    </div>
  </div>
</template>

<script>
  import axios from 'axios'

  export default {
    name: 'GoodsView',

    data() {
      return {
        name: '',   // 初始为空
        awesome: false,
        loading: true,
        goodsList: [],
        categoryMap: {}
      };
    },

    computed: {
      // 示例：计算商品总数
      totalGoods() {
        return this.goodsList.length;
      }
    },

    created() {
      const BASE = 'http://localhost:9191'
      this.loading = true
      Promise.all([
        axios.get(BASE + '/goodapi/list'),
        axios.get(BASE + '/categoryapi/list')
      ])
              .then(([goodsResp, categoryResp]) => {
                const goodsData = (goodsResp.data && goodsResp.data.data) || []
                const categories = (categoryResp.data && categoryResp.data.data) || []
                const map = {}
                categories.forEach(c => { map[c.id] = c.name })
                this.categoryMap = map
                this.goodsList = goodsData.map(g => ({
                  id: g.id,
                  name: g.name,
                  description: g.description,
                  // 后端 imgs 形如 /file/xxx.jpg，需要拼接服务器前缀
                  image: g.imgs ? (BASE + g.imgs) : '',
                  // 暂用 saleMoney 做展示价格（后端无单价字段时）
                  price: g.saleMoney,
                  categoryId: g.categoryId,
                  categoryName: map[g.categoryId]
                }))
              })
              .catch(err => {
                console.error('加载商品/分类失败', err)
              })
              .finally(() => {
                this.loading = false
              })
    },

    methods: {
      formatPrice(v){
        if(v === null || v === undefined) return '-'
        const n = Number(v)
        return Number.isNaN(n) ? v : n.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 2 })
      },
      addToCart(good) {
        // TODO: 实际项目中调用 Vuex 或 API
        console.log('加入购物车:', good.name);
        this.$emit('add-to-cart', good); // 可用于父组件监听
        alert(`已加入购物车：${good.name}`);
      },
      clear() {
        this.name = '';
      }
    },

    mounted() {
      console.log('商品页面已挂载');
    },

    beforeUnmount() {
      console.log('商品页面即将卸载');
    }
  };
</script>

<style scoped>
  .goods-container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 0 16px;
  }

  .page-title {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
    font-size: 28px;
  }

  .loading,
  .no-data {
    text-align: center;
    color: #999;
    font-size: 16px;
    padding: 40px 0;
  }

  .goods-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
  }

  .goods-card {
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    background: #fff;
    padding: 16px 16px 12px;
    text-align: center;
  }

  .goods-card-header {
    color: #888;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .goods-card-title {
    font-weight: 700;
    color: #222;
    margin-bottom: 8px;
  }

  .goods-card-price {
    color: #e60000;
    font-weight: 700;
    margin-bottom: 12px;
  }

  .btn-add-cart {
    width: 60%;
    margin: 0 auto;
    padding: 10px 12px;
    background-color: #42b983;
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
  }

  .btn-add-cart:hover {
    background-color: #369a6e;
  }

  /* 响应式：手机适配 */
  @media (max-width: 768px) {
    .goods-list {
      grid-template-columns: 1fr 1fr;
      padding: 0 8px;
    }

    .page-title {
      font-size: 24px;
    }
  }
</style>