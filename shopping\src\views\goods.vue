<template>
  <div class="goods-container">
    <div class="header">
      <h2 class="page-title">在线购物商城</h2>
      <div class="user-info">
        <span>欢迎，{{ username }}</span>
        <el-button v-if="userRole === 'admin'" type="success" @click="showAddGoodDialog">添加商品</el-button>
        <el-button v-if="userRole === 'admin'" type="primary" @click="goToAdmin">管理后台</el-button>
        <el-button type="danger" @click="logout">退出登录</el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">加载中...</div>

    <!-- 商品列表（卡片样式） -->
    <div v-else-if="goodsList.length > 0" class="goods-list">
      <div class="goods-card" v-for="good in goodsList" :key="good.id">
        <div class="goods-card-header">{{ good.categoryName || '未分类' }}</div>
        <div class="goods-card-title" @click="showGoodDetail(good)">{{ good.name }}</div>
        <div class="goods-card-description">{{ good.description || '暂无描述' }}</div>
        <div class="goods-card-price">¥{{ formatPrice(good.price) }}</div>
        <div class="goods-card-actions">
          <button class="btn-add-cart" @click="addToCart(good)">加入购物车</button>
          <button v-if="userRole === 'admin'" class="btn-edit" @click="editGood(good)">编辑</button>
          <button v-if="userRole === 'admin'" class="btn-delete" @click="deleteGood(good)">删除</button>
        </div>
      </div>
    </div>

    <!-- 无数据 -->
    <div v-else class="no-data">
      暂无商品
    </div>

    <!-- 添加/编辑商品对话框 -->
    <el-dialog
      :title="goodDialogTitle"
      v-model="goodDialogVisible"
      width="600px"
    >
      <el-form
        ref="goodFormRef"
        :model="goodForm"
        :rules="goodRules"
        label-width="100px"
      >
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="goodForm.name" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品描述" prop="description">
          <el-input v-model="goodForm.description" type="textarea" rows="3" placeholder="请输入商品描述" />
        </el-form-item>
        <el-form-item label="价格" prop="saleMoney">
          <el-input-number v-model="goodForm.saleMoney" :min="0" :precision="2" placeholder="请输入价格" />
        </el-form-item>
        <el-form-item label="折扣" prop="discount">
          <el-input-number v-model="goodForm.discount" :min="0" :max="1" :step="0.1" :precision="2" placeholder="请输入折扣(0-1)" />
        </el-form-item>
        <el-form-item label="商品分类" prop="categoryId">
          <el-select v-model="goodForm.categoryId" placeholder="请选择分类" style="width: 100%">
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否推荐" prop="recommend">
          <el-switch v-model="goodForm.recommend" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="goodDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveGood" :loading="goodSaveLoading">
            {{ isEditGood ? '更新' : '添加' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 商品详情对话框 -->
    <el-dialog
      title="商品详情"
      v-model="detailDialogVisible"
      width="500px"
    >
      <div v-if="selectedGood" class="good-detail">
        <div class="detail-item">
          <label>商品名称：</label>
          <span>{{ selectedGood.name }}</span>
        </div>
        <div class="detail-item">
          <label>商品描述：</label>
          <span>{{ selectedGood.description || '暂无描述' }}</span>
        </div>
        <div class="detail-item">
          <label>价格：</label>
          <span>¥{{ formatPrice(selectedGood.price) }}</span>
        </div>
        <div class="detail-item">
          <label>折扣：</label>
          <span>{{ selectedGood.discount ? (selectedGood.discount * 100).toFixed(0) + '%' : '无折扣' }}</span>
        </div>
        <div class="detail-item">
          <label>分类：</label>
          <span>{{ selectedGood.categoryName || '未分类' }}</span>
        </div>
        <div class="detail-item">
          <label>销量：</label>
          <span>{{ selectedGood.sales || 0 }}</span>
        </div>
        <div class="detail-item">
          <label>是否推荐：</label>
          <span>{{ selectedGood.recommend ? '是' : '否' }}</span>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="addToCart(selectedGood)">加入购物车</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import axios from 'axios'
  import { ElMessage } from 'element-plus'

  export default {
    name: 'GoodsView',

    data() {
      return {
        name: '',   // 初始为空
        awesome: false,
        loading: true,
        goodsList: [],
        categoryMap: {},
        categories: [], // 商品分类列表
        username: localStorage.getItem('username') || '',
        userRole: localStorage.getItem('userRole') || '',

        // 商品管理相关
        goodDialogVisible: false,
        goodDialogTitle: '添加商品',
        isEditGood: false,
        goodSaveLoading: false,
        goodForm: {
          id: null,
          name: '',
          description: '',
          saleMoney: 0,
          discount: 0,
          categoryId: null,
          recommend: false
        },

        // 商品详情相关
        detailDialogVisible: false,
        selectedGood: null
      };
    },

    computed: {
      // 示例：计算商品总数
      totalGoods() {
        return this.goodsList.length;
      },

      // 商品表单验证规则
      goodRules() {
        return {
          name: [
            { required: true, message: '请输入商品名称', trigger: 'blur' },
            { min: 2, max: 50, message: '商品名称长度为2-50个字符', trigger: 'blur' }
          ],
          saleMoney: [
            { required: true, message: '请输入商品价格', trigger: 'blur' },
            { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
          ],
          categoryId: [
            { required: true, message: '请选择商品分类', trigger: 'change' }
          ]
        }
      }
    },

    created() {
      const BASE = 'http://localhost:9192'
      this.loading = true
      Promise.all([
        axios.get(BASE + '/goodapi/list'),
        axios.get(BASE + '/categoryapi/list')
      ])
              .then(([goodsResp, categoryResp]) => {
                const goodsData = (goodsResp.data && goodsResp.data.data) || []
                const categories = (categoryResp.data && categoryResp.data.data) || []
                const map = {}
                categories.forEach(c => { map[c.id] = c.name })
                this.categoryMap = map
                this.categories = categories
                this.goodsList = goodsData.map(g => ({
                  id: g.id,
                  name: g.name,
                  description: g.description,
                  // 后端 imgs 形如 /file/xxx.jpg，需要拼接服务器前缀
                  image: g.imgs ? (BASE + g.imgs) : '',
                  // 暂用 saleMoney 做展示价格（后端无单价字段时）
                  price: g.saleMoney,
                  categoryId: g.categoryId,
                  categoryName: map[g.categoryId]
                }))
              })
              .catch(err => {
                console.error('加载商品/分类失败', err)
              })
              .finally(() => {
                this.loading = false
              })
    },

    methods: {
      formatPrice(v){
        if(v === null || v === undefined) return '-'
        const n = Number(v)
        return Number.isNaN(n) ? v : n.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 2 })
      },
      addToCart(good) {
        // TODO: 实际项目中调用 Vuex 或 API
        console.log('加入购物车:', good.name);
        this.$emit('add-to-cart', good); // 可用于父组件监听
        alert(`已加入购物车：${good.name}`);
      },
      clear() {
        this.name = '';
      },
      logout() {
        localStorage.clear()
        ElMessage.success('已退出登录')
        this.$router.push('/login')
      },
      goToAdmin() {
        this.$router.push('/admin')
      },

      // 显示商品详情
      showGoodDetail(good) {
        this.selectedGood = good
        this.detailDialogVisible = true
      },

      // 显示添加商品对话框
      showAddGoodDialog() {
        this.goodDialogTitle = '添加商品'
        this.isEditGood = false
        this.goodForm = {
          id: null,
          name: '',
          description: '',
          saleMoney: 0,
          discount: 0,
          categoryId: null,
          recommend: false
        }
        this.goodDialogVisible = true
      },

      // 编辑商品
      editGood(good) {
        this.goodDialogTitle = '编辑商品'
        this.isEditGood = true
        this.goodForm = {
          id: good.id,
          name: good.name,
          description: good.description,
          saleMoney: good.saleMoney,
          discount: good.discount,
          categoryId: good.categoryId,
          recommend: good.recommend
        }
        this.goodDialogVisible = true
      },

      // 保存商品
      async saveGood() {
        if (!this.$refs.goodFormRef) return

        this.$refs.goodFormRef.validate(async (valid) => {
          if (valid) {
            this.goodSaveLoading = true

            try {
              const url = this.isEditGood
                ? 'http://localhost:9192/goodapi/update'
                : 'http://localhost:9192/goodapi/add'

              const method = this.isEditGood ? 'PUT' : 'POST'

              const response = await fetch(url, {
                method,
                headers: {
                  'Content-Type': 'application/json',
                  'token': localStorage.getItem('token')
                },
                body: JSON.stringify(this.goodForm)
              })

              const result = await response.json()

              if (result.code === '200') {
                this.$message.success(this.isEditGood ? '商品更新成功' : '商品添加成功')
                this.goodDialogVisible = false
                this.refreshGoods()
              } else {
                this.$message.error(result.msg || '操作失败')
              }
            } catch (error) {
              console.error('保存商品失败:', error)
              this.$message.error('网络错误，请稍后重试')
            } finally {
              this.goodSaveLoading = false
            }
          }
        })
      },

      // 删除商品
      async deleteGood(good) {
        try {
          await this.$confirm(`确定要删除商品 "${good.name}" 吗？`, '确认删除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })

          const response = await fetch(`http://localhost:9192/goodapi/delete/${good.id}`, {
            method: 'DELETE',
            headers: {
              'token': localStorage.getItem('token')
            }
          })

          const result = await response.json()

          if (result.code === '200') {
            this.$message.success('商品删除成功')
            this.refreshGoods()
          } else {
            this.$message.error(result.msg || '删除失败')
          }
        } catch (error) {
          if (error !== 'cancel') {
            console.error('删除商品失败:', error)
            this.$message.error('网络错误，请稍后重试')
          }
        }
      },

      // 刷新商品列表
      refreshGoods() {
        this.loading = true
        const BASE = 'http://localhost:9192'
        axios.get(BASE + '/goodapi/list')
          .then(response => {
            const goodsData = (response.data && response.data.data) || []
            this.goodsList = goodsData.map(good => ({
              ...good,
              categoryName: this.categoryMap[good.categoryId] || '未分类',
              price: good.saleMoney * (good.discount || 1)
            }))
          })
          .catch(error => {
            console.error('获取商品列表失败:', error)
            this.$message.error('获取商品列表失败')
          })
          .finally(() => {
            this.loading = false
          })
      }
    },

    mounted() {
      console.log('商品页面已挂载');
    },

    beforeUnmount() {
      console.log('商品页面即将卸载');
    }
  };
</script>

<style scoped>
  /* 滚动条样式优化 */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
  .goods-container {
    width: 100%;
    min-height: 100vh;
    padding: 20px;
    background-color: #f5f5f5;
    overflow-x: hidden;
    scroll-behavior: smooth;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    max-width: 1400px;
    margin: 0 auto 30px auto;
  }

  .page-title {
    color: #333;
    margin: 0;
    font-size: 28px;
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .user-info span {
    color: #666;
    font-size: 14px;
  }

  .loading {
    text-align: center;
    font-size: 16px;
    padding: 60px 0;
    color: #666;
    max-width: 1400px;
    margin: 0 auto;
  }

  .no-data {
    text-align: center;
    color: #999;
    font-size: 18px;
    padding: 80px 0;
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }

  .goods-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .goods-card {
    border: 1px solid #e5e5e5;
    border-radius: 12px;
    background: #fff;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    height: auto;
    min-height: 320px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .goods-card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transform: translateY(-4px);
  }

  .goods-card-header {
    color: #888;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .goods-card-title {
    font-weight: 700;
    color: #222;
    margin-bottom: 8px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .goods-card-title:hover {
    color: #409EFF;
  }

  .goods-card-description {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.4;
    height: 2.8em;
    flex-grow: 1;
  }

  .goods-card-price {
    color: #e60000;
    font-weight: 700;
    margin-bottom: 12px;
  }

  .btn-add-cart {
    width: 60%;
    margin: 0 auto;
    padding: 10px 12px;
    background-color: #42b983;
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
  }

  .btn-add-cart:hover {
    background-color: #369a6e;
  }

  .goods-card-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .btn-edit, .btn-delete {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 12px;
  }

  .btn-edit {
    background-color: #409EFF;
    color: #fff;
  }

  .btn-edit:hover {
    background-color: #337ecc;
  }

  .btn-delete {
    background-color: #F56C6C;
    color: #fff;
  }

  .btn-delete:hover {
    background-color: #dd6161;
  }

  .good-detail {
    padding: 20px 0;
  }

  .detail-item {
    display: flex;
    margin-bottom: 15px;
    align-items: center;
  }

  .detail-item label {
    font-weight: bold;
    width: 100px;
    color: #333;
  }

  .detail-item span {
    color: #666;
    flex: 1;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }

  /* 响应式设计 */

  /* 大屏幕 */
  @media (min-width: 1400px) {
    .goods-list {
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
  }

  /* 中等屏幕 */
  @media (max-width: 1200px) {
    .goods-container {
      padding: 15px;
    }

    .goods-list {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      padding: 0 10px;
    }

    .header {
      padding: 15px;
      margin-bottom: 20px;
    }
  }

  /* 平板适配 */
  @media (max-width: 768px) {
    .goods-container {
      padding: 10px;
    }

    .goods-list {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 15px;
      padding: 0 5px;
    }

    .goods-card {
      padding: 15px;
      min-height: 280px;
    }

    .goods-card-title {
      font-size: 16px;
    }

    .header {
      flex-direction: column;
      gap: 15px;
      text-align: center;
    }

    .user-info {
      flex-wrap: wrap;
      justify-content: center;
    }

    .page-title {
      font-size: 22px;
    }
  }

  /* 手机适配 */
  @media (max-width: 480px) {
    .goods-container {
      padding: 8px;
    }

    .goods-list {
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
      gap: 12px;
      padding: 0;
    }

    .goods-card {
      padding: 12px;
      min-height: 250px;
    }

    .goods-card-title {
      font-size: 14px;
    }

    .goods-card-description {
      font-size: 12px;
    }

    .goods-card-price {
      font-size: 16px;
    }

    .header {
      padding: 12px;
      margin-bottom: 15px;
    }

    .page-title {
      font-size: 18px;
    }

    .user-info span {
      font-size: 12px;
    }

    .btn-add-cart, .btn-edit, .btn-delete {
      padding: 6px 8px;
      font-size: 11px;
    }
  }
</style>