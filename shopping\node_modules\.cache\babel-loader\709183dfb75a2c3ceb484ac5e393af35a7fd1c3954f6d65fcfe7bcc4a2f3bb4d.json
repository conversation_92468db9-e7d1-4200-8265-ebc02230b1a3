{"ast": null, "code": "import baseTimes from './_baseTimes.js';\nimport castFunction from './_castFunction.js';\nimport toInteger from './toInteger.js';\n\n/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min;\n\n/**\n * Invokes the iteratee `n` times, returning an array of the results of\n * each invocation. The iteratee is invoked with one argument; (index).\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n * @example\n *\n * _.times(3, String);\n * // => ['0', '1', '2']\n *\n *  _.times(4, _.constant(0));\n * // => [0, 0, 0, 0]\n */\nfunction times(n, iteratee) {\n  n = toInteger(n);\n  if (n < 1 || n > MAX_SAFE_INTEGER) {\n    return [];\n  }\n  var index = MAX_ARRAY_LENGTH,\n    length = nativeMin(n, MAX_ARRAY_LENGTH);\n  iteratee = castFunction(iteratee);\n  n -= MAX_ARRAY_LENGTH;\n  var result = baseTimes(length, iteratee);\n  while (++index < n) {\n    iteratee(index);\n  }\n  return result;\n}\nexport default times;", "map": {"version": 3, "names": ["baseTimes", "castFunction", "toInteger", "MAX_SAFE_INTEGER", "MAX_ARRAY_LENGTH", "nativeMin", "Math", "min", "times", "n", "iteratee", "index", "length", "result"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/times.js"], "sourcesContent": ["import baseTimes from './_baseTimes.js';\nimport castFunction from './_castFunction.js';\nimport toInteger from './toInteger.js';\n\n/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min;\n\n/**\n * Invokes the iteratee `n` times, returning an array of the results of\n * each invocation. The iteratee is invoked with one argument; (index).\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n * @example\n *\n * _.times(3, String);\n * // => ['0', '1', '2']\n *\n *  _.times(4, _.constant(0));\n * // => [0, 0, 0, 0]\n */\nfunction times(n, iteratee) {\n  n = toInteger(n);\n  if (n < 1 || n > MAX_SAFE_INTEGER) {\n    return [];\n  }\n  var index = MAX_ARRAY_LENGTH,\n      length = nativeMin(n, MAX_ARRAY_LENGTH);\n\n  iteratee = castFunction(iteratee);\n  n -= MAX_ARRAY_LENGTH;\n\n  var result = baseTimes(length, iteratee);\n  while (++index < n) {\n    iteratee(index);\n  }\n  return result;\n}\n\nexport default times;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA,IAAIC,gBAAgB,GAAG,gBAAgB;;AAEvC;AACA,IAAIC,gBAAgB,GAAG,UAAU;;AAEjC;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,CAAC,EAAEC,QAAQ,EAAE;EAC1BD,CAAC,GAAGP,SAAS,CAACO,CAAC,CAAC;EAChB,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAGN,gBAAgB,EAAE;IACjC,OAAO,EAAE;EACX;EACA,IAAIQ,KAAK,GAAGP,gBAAgB;IACxBQ,MAAM,GAAGP,SAAS,CAACI,CAAC,EAAEL,gBAAgB,CAAC;EAE3CM,QAAQ,GAAGT,YAAY,CAACS,QAAQ,CAAC;EACjCD,CAAC,IAAIL,gBAAgB;EAErB,IAAIS,MAAM,GAAGb,SAAS,CAACY,MAAM,EAAEF,QAAQ,CAAC;EACxC,OAAO,EAAEC,KAAK,GAAGF,CAAC,EAAE;IAClBC,QAAQ,CAACC,KAAK,CAAC;EACjB;EACA,OAAOE,MAAM;AACf;AAEA,eAAeL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}