{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport axios from 'axios';\nimport { ElMessage } from 'element-plus';\nexport default {\n  name: 'GoodsView',\n  data() {\n    return {\n      name: '',\n      // 初始为空\n      awesome: false,\n      loading: true,\n      goodsList: [],\n      categoryMap: {},\n      categories: [],\n      // 商品分类列表\n      username: localStorage.getItem('username') || '',\n      userRole: localStorage.getItem('userRole') || '',\n      // 商品管理相关\n      goodDialogVisible: false,\n      goodDialogTitle: '添加商品',\n      isEditGood: false,\n      goodSaveLoading: false,\n      goodForm: {\n        id: null,\n        name: '',\n        description: '',\n        saleMoney: 0,\n        discount: 0,\n        categoryId: null,\n        recommend: false\n      },\n      // 商品详情相关\n      detailDialogVisible: false,\n      selectedGood: null\n    };\n  },\n  computed: {\n    // 示例：计算商品总数\n    totalGoods() {\n      return this.goodsList.length;\n    },\n    // 商品表单验证规则\n    goodRules() {\n      return {\n        name: [{\n          required: true,\n          message: '请输入商品名称',\n          trigger: 'blur'\n        }, {\n          min: 2,\n          max: 50,\n          message: '商品名称长度为2-50个字符',\n          trigger: 'blur'\n        }],\n        saleMoney: [{\n          required: true,\n          message: '请输入商品价格',\n          trigger: 'blur'\n        }, {\n          type: 'number',\n          min: 0,\n          message: '价格不能小于0',\n          trigger: 'blur'\n        }],\n        categoryId: [{\n          required: true,\n          message: '请选择商品分类',\n          trigger: 'change'\n        }]\n      };\n    }\n  },\n  created() {\n    const BASE = 'http://localhost:9192';\n    this.loading = true;\n    Promise.all([axios.get(BASE + '/goodapi/list'), axios.get(BASE + '/categoryapi/list')]).then(([goodsResp, categoryResp]) => {\n      const goodsData = goodsResp.data && goodsResp.data.data || [];\n      const categories = categoryResp.data && categoryResp.data.data || [];\n      const map = {};\n      categories.forEach(c => {\n        map[c.id] = c.name;\n      });\n      this.categoryMap = map;\n      this.categories = categories;\n      this.goodsList = goodsData.map(g => ({\n        id: g.id,\n        name: g.name,\n        description: g.description,\n        // 后端 imgs 形如 /file/xxx.jpg，需要拼接服务器前缀\n        image: g.imgs ? BASE + g.imgs : '',\n        // 暂用 saleMoney 做展示价格（后端无单价字段时）\n        price: g.saleMoney,\n        categoryId: g.categoryId,\n        categoryName: map[g.categoryId]\n      }));\n    }).catch(err => {\n      console.error('加载商品/分类失败', err);\n    }).finally(() => {\n      this.loading = false;\n    });\n  },\n  methods: {\n    formatPrice(v) {\n      if (v === null || v === undefined) return '-';\n      const n = Number(v);\n      return Number.isNaN(n) ? v : n.toLocaleString('zh-CN', {\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 2\n      });\n    },\n    addToCart(good) {\n      // TODO: 实际项目中调用 Vuex 或 API\n      console.log('加入购物车:', good.name);\n      this.$emit('add-to-cart', good); // 可用于父组件监听\n      alert(`已加入购物车：${good.name}`);\n    },\n    clear() {\n      this.name = '';\n    },\n    logout() {\n      localStorage.clear();\n      ElMessage.success('已退出登录');\n      this.$router.push('/login');\n    },\n    goToAdmin() {\n      this.$router.push('/admin');\n    },\n    // 显示商品详情\n    showGoodDetail(good) {\n      this.selectedGood = good;\n      this.detailDialogVisible = true;\n    },\n    // 显示添加商品对话框\n    showAddGoodDialog() {\n      this.goodDialogTitle = '添加商品';\n      this.isEditGood = false;\n      this.goodForm = {\n        id: null,\n        name: '',\n        description: '',\n        saleMoney: 0,\n        discount: 0,\n        categoryId: null,\n        recommend: false\n      };\n      this.goodDialogVisible = true;\n    },\n    // 编辑商品\n    editGood(good) {\n      this.goodDialogTitle = '编辑商品';\n      this.isEditGood = true;\n      this.goodForm = {\n        id: good.id,\n        name: good.name,\n        description: good.description,\n        saleMoney: good.saleMoney,\n        discount: good.discount,\n        categoryId: good.categoryId,\n        recommend: good.recommend\n      };\n      this.goodDialogVisible = true;\n    },\n    // 保存商品\n    async saveGood() {\n      if (!this.$refs.goodFormRef) return;\n      this.$refs.goodFormRef.validate(async valid => {\n        if (valid) {\n          this.goodSaveLoading = true;\n          try {\n            const url = this.isEditGood ? 'http://localhost:9192/goodapi/update' : 'http://localhost:9192/goodapi/add';\n            const method = this.isEditGood ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n              method,\n              headers: {\n                'Content-Type': 'application/json',\n                'token': localStorage.getItem('token')\n              },\n              body: JSON.stringify(this.goodForm)\n            });\n            const result = await response.json();\n            if (result.code === '200') {\n              this.$message.success(this.isEditGood ? '商品更新成功' : '商品添加成功');\n              this.goodDialogVisible = false;\n              this.refreshGoods();\n            } else {\n              this.$message.error(result.msg || '操作失败');\n            }\n          } catch (error) {\n            console.error('保存商品失败:', error);\n            this.$message.error('网络错误，请稍后重试');\n          } finally {\n            this.goodSaveLoading = false;\n          }\n        }\n      });\n    },\n    // 删除商品\n    async deleteGood(good) {\n      try {\n        await this.$confirm(`确定要删除商品 \"${good.name}\" 吗？`, '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        const response = await fetch(`http://localhost:9192/goodapi/delete/${good.id}`, {\n          method: 'DELETE',\n          headers: {\n            'token': localStorage.getItem('token')\n          }\n        });\n        const result = await response.json();\n        if (result.code === '200') {\n          this.$message.success('商品删除成功');\n          this.refreshGoods();\n        } else {\n          this.$message.error(result.msg || '删除失败');\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除商品失败:', error);\n          this.$message.error('网络错误，请稍后重试');\n        }\n      }\n    },\n    // 刷新商品列表\n    refreshGoods() {\n      this.loading = true;\n      const BASE = 'http://localhost:9192';\n      axios.get(BASE + '/goodapi/list').then(response => {\n        const goodsData = response.data && response.data.data || [];\n        this.goodsList = goodsData.map(good => ({\n          ...good,\n          categoryName: this.categoryMap[good.categoryId] || '未分类',\n          price: good.saleMoney * (good.discount || 1)\n        }));\n      }).catch(error => {\n        console.error('获取商品列表失败:', error);\n        this.$message.error('获取商品列表失败');\n      }).finally(() => {\n        this.loading = false;\n      });\n    }\n  },\n  mounted() {\n    console.log('商品页面已挂载');\n  },\n  beforeUnmount() {\n    console.log('商品页面即将卸载');\n  }\n};", "map": {"version": 3, "names": ["axios", "ElMessage", "name", "data", "awesome", "loading", "goodsList", "categoryMap", "categories", "username", "localStorage", "getItem", "userRole", "goodDialogVisible", "goodDialogTitle", "isEditGood", "goodSaveLoading", "goodForm", "id", "description", "saleMoney", "discount", "categoryId", "recommend", "detailDialogVisible", "<PERSON><PERSON><PERSON>", "computed", "totalGoods", "length", "goodRules", "required", "message", "trigger", "min", "max", "type", "created", "BASE", "Promise", "all", "get", "then", "goodsResp", "categoryResp", "goodsData", "map", "for<PERSON>ach", "c", "g", "image", "imgs", "price", "categoryName", "catch", "err", "console", "error", "finally", "methods", "formatPrice", "v", "undefined", "n", "Number", "isNaN", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "addToCart", "good", "log", "$emit", "alert", "clear", "logout", "success", "$router", "push", "goToAdmin", "showGoodDetail", "showAddGoodDialog", "editGood", "saveGood", "$refs", "goodFormRef", "validate", "valid", "url", "method", "response", "fetch", "headers", "body", "JSON", "stringify", "result", "json", "code", "$message", "refreshGoods", "msg", "deleteGood", "$confirm", "confirmButtonText", "cancelButtonText", "mounted", "beforeUnmount"], "sources": ["D:\\2025_down\\project\\shoppingOnline-20250826-sfl\\shoppingOnline-20250826\\shopping\\src\\views\\goods.vue"], "sourcesContent": ["<template>\r\n  <div class=\"goods-container\">\r\n    <div class=\"header\">\r\n      <h2 class=\"page-title\">在线购物商城</h2>\r\n      <div class=\"user-info\">\r\n        <span>欢迎，{{ username }}</span>\r\n        <el-button v-if=\"userRole === 'admin'\" type=\"success\" @click=\"showAddGoodDialog\">添加商品</el-button>\r\n        <el-button v-if=\"userRole === 'admin'\" type=\"primary\" @click=\"goToAdmin\">管理后台</el-button>\r\n        <el-button type=\"danger\" @click=\"logout\">退出登录</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n\r\n    <!-- 商品列表（卡片样式） -->\r\n    <div v-else-if=\"goodsList.length > 0\" class=\"goods-list\">\r\n      <div class=\"goods-card\" v-for=\"good in goodsList\" :key=\"good.id\">\r\n        <div class=\"goods-card-header\">{{ good.categoryName || '未分类' }}</div>\r\n        <div class=\"goods-card-title\" @click=\"showGoodDetail(good)\">{{ good.name }}</div>\r\n        <div class=\"goods-card-description\">{{ good.description || '暂无描述' }}</div>\r\n        <div class=\"goods-card-price\">¥{{ formatPrice(good.price) }}</div>\r\n        <div class=\"goods-card-actions\">\r\n          <button class=\"btn-add-cart\" @click=\"addToCart(good)\">加入购物车</button>\r\n          <button v-if=\"userRole === 'admin'\" class=\"btn-edit\" @click=\"editGood(good)\">编辑</button>\r\n          <button v-if=\"userRole === 'admin'\" class=\"btn-delete\" @click=\"deleteGood(good)\">删除</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 无数据 -->\r\n    <div v-else class=\"no-data\">\r\n      暂无商品\r\n    </div>\r\n\r\n    <!-- 添加/编辑商品对话框 -->\r\n    <el-dialog\r\n      :title=\"goodDialogTitle\"\r\n      v-model=\"goodDialogVisible\"\r\n      width=\"600px\"\r\n    >\r\n      <el-form\r\n        ref=\"goodFormRef\"\r\n        :model=\"goodForm\"\r\n        :rules=\"goodRules\"\r\n        label-width=\"100px\"\r\n      >\r\n        <el-form-item label=\"商品名称\" prop=\"name\">\r\n          <el-input v-model=\"goodForm.name\" placeholder=\"请输入商品名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"商品描述\" prop=\"description\">\r\n          <el-input v-model=\"goodForm.description\" type=\"textarea\" rows=\"3\" placeholder=\"请输入商品描述\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"价格\" prop=\"saleMoney\">\r\n          <el-input-number v-model=\"goodForm.saleMoney\" :min=\"0\" :precision=\"2\" placeholder=\"请输入价格\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"折扣\" prop=\"discount\">\r\n          <el-input-number v-model=\"goodForm.discount\" :min=\"0\" :max=\"1\" :step=\"0.1\" :precision=\"2\" placeholder=\"请输入折扣(0-1)\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"商品分类\" prop=\"categoryId\">\r\n          <el-select v-model=\"goodForm.categoryId\" placeholder=\"请选择分类\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"category in categories\"\r\n              :key=\"category.id\"\r\n              :label=\"category.name\"\r\n              :value=\"category.id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否推荐\" prop=\"recommend\">\r\n          <el-switch v-model=\"goodForm.recommend\" />\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"goodDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"saveGood\" :loading=\"goodSaveLoading\">\r\n            {{ isEditGood ? '更新' : '添加' }}\r\n          </el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- 商品详情对话框 -->\r\n    <el-dialog\r\n      title=\"商品详情\"\r\n      v-model=\"detailDialogVisible\"\r\n      width=\"500px\"\r\n    >\r\n      <div v-if=\"selectedGood\" class=\"good-detail\">\r\n        <div class=\"detail-item\">\r\n          <label>商品名称：</label>\r\n          <span>{{ selectedGood.name }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>商品描述：</label>\r\n          <span>{{ selectedGood.description || '暂无描述' }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>价格：</label>\r\n          <span>¥{{ formatPrice(selectedGood.price) }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>折扣：</label>\r\n          <span>{{ selectedGood.discount ? (selectedGood.discount * 100).toFixed(0) + '%' : '无折扣' }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>分类：</label>\r\n          <span>{{ selectedGood.categoryName || '未分类' }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>销量：</label>\r\n          <span>{{ selectedGood.sales || 0 }}</span>\r\n        </div>\r\n        <div class=\"detail-item\">\r\n          <label>是否推荐：</label>\r\n          <span>{{ selectedGood.recommend ? '是' : '否' }}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"detailDialogVisible = false\">关闭</el-button>\r\n          <el-button type=\"primary\" @click=\"addToCart(selectedGood)\">加入购物车</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import axios from 'axios'\r\n  import { ElMessage } from 'element-plus'\r\n\r\n  export default {\r\n    name: 'GoodsView',\r\n\r\n    data() {\r\n      return {\r\n        name: '',   // 初始为空\r\n        awesome: false,\r\n        loading: true,\r\n        goodsList: [],\r\n        categoryMap: {},\r\n        categories: [], // 商品分类列表\r\n        username: localStorage.getItem('username') || '',\r\n        userRole: localStorage.getItem('userRole') || '',\r\n\r\n        // 商品管理相关\r\n        goodDialogVisible: false,\r\n        goodDialogTitle: '添加商品',\r\n        isEditGood: false,\r\n        goodSaveLoading: false,\r\n        goodForm: {\r\n          id: null,\r\n          name: '',\r\n          description: '',\r\n          saleMoney: 0,\r\n          discount: 0,\r\n          categoryId: null,\r\n          recommend: false\r\n        },\r\n\r\n        // 商品详情相关\r\n        detailDialogVisible: false,\r\n        selectedGood: null\r\n      };\r\n    },\r\n\r\n    computed: {\r\n      // 示例：计算商品总数\r\n      totalGoods() {\r\n        return this.goodsList.length;\r\n      },\r\n\r\n      // 商品表单验证规则\r\n      goodRules() {\r\n        return {\r\n          name: [\r\n            { required: true, message: '请输入商品名称', trigger: 'blur' },\r\n            { min: 2, max: 50, message: '商品名称长度为2-50个字符', trigger: 'blur' }\r\n          ],\r\n          saleMoney: [\r\n            { required: true, message: '请输入商品价格', trigger: 'blur' },\r\n            { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }\r\n          ],\r\n          categoryId: [\r\n            { required: true, message: '请选择商品分类', trigger: 'change' }\r\n          ]\r\n        }\r\n      }\r\n    },\r\n\r\n    created() {\r\n      const BASE = 'http://localhost:9192'\r\n      this.loading = true\r\n      Promise.all([\r\n        axios.get(BASE + '/goodapi/list'),\r\n        axios.get(BASE + '/categoryapi/list')\r\n      ])\r\n              .then(([goodsResp, categoryResp]) => {\r\n                const goodsData = (goodsResp.data && goodsResp.data.data) || []\r\n                const categories = (categoryResp.data && categoryResp.data.data) || []\r\n                const map = {}\r\n                categories.forEach(c => { map[c.id] = c.name })\r\n                this.categoryMap = map\r\n                this.categories = categories\r\n                this.goodsList = goodsData.map(g => ({\r\n                  id: g.id,\r\n                  name: g.name,\r\n                  description: g.description,\r\n                  // 后端 imgs 形如 /file/xxx.jpg，需要拼接服务器前缀\r\n                  image: g.imgs ? (BASE + g.imgs) : '',\r\n                  // 暂用 saleMoney 做展示价格（后端无单价字段时）\r\n                  price: g.saleMoney,\r\n                  categoryId: g.categoryId,\r\n                  categoryName: map[g.categoryId]\r\n                }))\r\n              })\r\n              .catch(err => {\r\n                console.error('加载商品/分类失败', err)\r\n              })\r\n              .finally(() => {\r\n                this.loading = false\r\n              })\r\n    },\r\n\r\n    methods: {\r\n      formatPrice(v){\r\n        if(v === null || v === undefined) return '-'\r\n        const n = Number(v)\r\n        return Number.isNaN(n) ? v : n.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 2 })\r\n      },\r\n      addToCart(good) {\r\n        // TODO: 实际项目中调用 Vuex 或 API\r\n        console.log('加入购物车:', good.name);\r\n        this.$emit('add-to-cart', good); // 可用于父组件监听\r\n        alert(`已加入购物车：${good.name}`);\r\n      },\r\n      clear() {\r\n        this.name = '';\r\n      },\r\n      logout() {\r\n        localStorage.clear()\r\n        ElMessage.success('已退出登录')\r\n        this.$router.push('/login')\r\n      },\r\n      goToAdmin() {\r\n        this.$router.push('/admin')\r\n      },\r\n\r\n      // 显示商品详情\r\n      showGoodDetail(good) {\r\n        this.selectedGood = good\r\n        this.detailDialogVisible = true\r\n      },\r\n\r\n      // 显示添加商品对话框\r\n      showAddGoodDialog() {\r\n        this.goodDialogTitle = '添加商品'\r\n        this.isEditGood = false\r\n        this.goodForm = {\r\n          id: null,\r\n          name: '',\r\n          description: '',\r\n          saleMoney: 0,\r\n          discount: 0,\r\n          categoryId: null,\r\n          recommend: false\r\n        }\r\n        this.goodDialogVisible = true\r\n      },\r\n\r\n      // 编辑商品\r\n      editGood(good) {\r\n        this.goodDialogTitle = '编辑商品'\r\n        this.isEditGood = true\r\n        this.goodForm = {\r\n          id: good.id,\r\n          name: good.name,\r\n          description: good.description,\r\n          saleMoney: good.saleMoney,\r\n          discount: good.discount,\r\n          categoryId: good.categoryId,\r\n          recommend: good.recommend\r\n        }\r\n        this.goodDialogVisible = true\r\n      },\r\n\r\n      // 保存商品\r\n      async saveGood() {\r\n        if (!this.$refs.goodFormRef) return\r\n\r\n        this.$refs.goodFormRef.validate(async (valid) => {\r\n          if (valid) {\r\n            this.goodSaveLoading = true\r\n\r\n            try {\r\n              const url = this.isEditGood\r\n                ? 'http://localhost:9192/goodapi/update'\r\n                : 'http://localhost:9192/goodapi/add'\r\n\r\n              const method = this.isEditGood ? 'PUT' : 'POST'\r\n\r\n              const response = await fetch(url, {\r\n                method,\r\n                headers: {\r\n                  'Content-Type': 'application/json',\r\n                  'token': localStorage.getItem('token')\r\n                },\r\n                body: JSON.stringify(this.goodForm)\r\n              })\r\n\r\n              const result = await response.json()\r\n\r\n              if (result.code === '200') {\r\n                this.$message.success(this.isEditGood ? '商品更新成功' : '商品添加成功')\r\n                this.goodDialogVisible = false\r\n                this.refreshGoods()\r\n              } else {\r\n                this.$message.error(result.msg || '操作失败')\r\n              }\r\n            } catch (error) {\r\n              console.error('保存商品失败:', error)\r\n              this.$message.error('网络错误，请稍后重试')\r\n            } finally {\r\n              this.goodSaveLoading = false\r\n            }\r\n          }\r\n        })\r\n      },\r\n\r\n      // 删除商品\r\n      async deleteGood(good) {\r\n        try {\r\n          await this.$confirm(`确定要删除商品 \"${good.name}\" 吗？`, '确认删除', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning',\r\n          })\r\n\r\n          const response = await fetch(`http://localhost:9192/goodapi/delete/${good.id}`, {\r\n            method: 'DELETE',\r\n            headers: {\r\n              'token': localStorage.getItem('token')\r\n            }\r\n          })\r\n\r\n          const result = await response.json()\r\n\r\n          if (result.code === '200') {\r\n            this.$message.success('商品删除成功')\r\n            this.refreshGoods()\r\n          } else {\r\n            this.$message.error(result.msg || '删除失败')\r\n          }\r\n        } catch (error) {\r\n          if (error !== 'cancel') {\r\n            console.error('删除商品失败:', error)\r\n            this.$message.error('网络错误，请稍后重试')\r\n          }\r\n        }\r\n      },\r\n\r\n      // 刷新商品列表\r\n      refreshGoods() {\r\n        this.loading = true\r\n        const BASE = 'http://localhost:9192'\r\n        axios.get(BASE + '/goodapi/list')\r\n          .then(response => {\r\n            const goodsData = (response.data && response.data.data) || []\r\n            this.goodsList = goodsData.map(good => ({\r\n              ...good,\r\n              categoryName: this.categoryMap[good.categoryId] || '未分类',\r\n              price: good.saleMoney * (good.discount || 1)\r\n            }))\r\n          })\r\n          .catch(error => {\r\n            console.error('获取商品列表失败:', error)\r\n            this.$message.error('获取商品列表失败')\r\n          })\r\n          .finally(() => {\r\n            this.loading = false\r\n          })\r\n      }\r\n    },\r\n\r\n    mounted() {\r\n      console.log('商品页面已挂载');\r\n    },\r\n\r\n    beforeUnmount() {\r\n      console.log('商品页面即将卸载');\r\n    }\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .goods-container {\r\n    width: 100%;\r\n    min-height: 100vh;\r\n    padding: 20px;\r\n    background-color: #f5f5f5;\r\n  }\r\n\r\n  .header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 30px;\r\n    padding: 20px;\r\n    background: white;\r\n    border-radius: 12px;\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.1);\r\n    max-width: 1400px;\r\n    margin: 0 auto 30px auto;\r\n  }\r\n\r\n  .page-title {\r\n    color: #333;\r\n    margin: 0;\r\n    font-size: 28px;\r\n  }\r\n\r\n  .user-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 15px;\r\n  }\r\n\r\n  .user-info span {\r\n    color: #666;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .loading,\r\n  .no-data {\r\n    text-align: center;\r\n    color: #999;\r\n    font-size: 16px;\r\n    padding: 40px 0;\r\n  }\r\n\r\n  .goods-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n    gap: 24px;\r\n    max-width: 1400px;\r\n    margin: 0 auto;\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .goods-card {\r\n    border: 1px solid #e5e5e5;\r\n    border-radius: 12px;\r\n    background: #fff;\r\n    padding: 20px;\r\n    text-align: center;\r\n    transition: all 0.3s ease;\r\n    height: auto;\r\n    min-height: 320px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .goods-card:hover {\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n    transform: translateY(-4px);\r\n  }\r\n\r\n  .goods-card-header {\r\n    color: #888;\r\n    font-size: 14px;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .goods-card-title {\r\n    font-weight: 700;\r\n    color: #222;\r\n    margin-bottom: 8px;\r\n    cursor: pointer;\r\n    transition: color 0.3s;\r\n  }\r\n\r\n  .goods-card-title:hover {\r\n    color: #409EFF;\r\n  }\r\n\r\n  .goods-card-description {\r\n    font-size: 14px;\r\n    color: #666;\r\n    margin-bottom: 8px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n\r\n  .goods-card-price {\r\n    color: #e60000;\r\n    font-weight: 700;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .btn-add-cart {\r\n    width: 60%;\r\n    margin: 0 auto;\r\n    padding: 10px 12px;\r\n    background-color: #42b983;\r\n    color: #fff;\r\n    border: none;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .btn-add-cart:hover {\r\n    background-color: #369a6e;\r\n  }\r\n\r\n  .goods-card-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .btn-edit, .btn-delete {\r\n    padding: 8px 12px;\r\n    border: none;\r\n    border-radius: 4px;\r\n    cursor: pointer;\r\n    transition: background-color 0.3s;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .btn-edit {\r\n    background-color: #409EFF;\r\n    color: #fff;\r\n  }\r\n\r\n  .btn-edit:hover {\r\n    background-color: #337ecc;\r\n  }\r\n\r\n  .btn-delete {\r\n    background-color: #F56C6C;\r\n    color: #fff;\r\n  }\r\n\r\n  .btn-delete:hover {\r\n    background-color: #dd6161;\r\n  }\r\n\r\n  .good-detail {\r\n    padding: 20px 0;\r\n  }\r\n\r\n  .detail-item {\r\n    display: flex;\r\n    margin-bottom: 15px;\r\n    align-items: center;\r\n  }\r\n\r\n  .detail-item label {\r\n    font-weight: bold;\r\n    width: 100px;\r\n    color: #333;\r\n  }\r\n\r\n  .detail-item span {\r\n    color: #666;\r\n    flex: 1;\r\n  }\r\n\r\n  .dialog-footer {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    gap: 10px;\r\n  }\r\n\r\n  /* 响应式设计 */\r\n\r\n  /* 大屏幕 */\r\n  @media (min-width: 1400px) {\r\n    .goods-list {\r\n      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n    }\r\n  }\r\n\r\n  /* 中等屏幕 */\r\n  @media (max-width: 1200px) {\r\n    .goods-container {\r\n      padding: 15px;\r\n    }\r\n\r\n    .goods-list {\r\n      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n      gap: 20px;\r\n      padding: 0 10px;\r\n    }\r\n\r\n    .header {\r\n      padding: 15px;\r\n      margin-bottom: 20px;\r\n    }\r\n  }\r\n\r\n  /* 平板适配 */\r\n  @media (max-width: 768px) {\r\n    .goods-container {\r\n      padding: 10px;\r\n    }\r\n\r\n    .goods-list {\r\n      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n      gap: 15px;\r\n      padding: 0 5px;\r\n    }\r\n\r\n    .goods-card {\r\n      padding: 15px;\r\n      min-height: 280px;\r\n    }\r\n\r\n    .goods-card-title {\r\n      font-size: 16px;\r\n    }\r\n\r\n    .header {\r\n      flex-direction: column;\r\n      gap: 15px;\r\n      text-align: center;\r\n    }\r\n\r\n    .user-info {\r\n      flex-wrap: wrap;\r\n      justify-content: center;\r\n    }\r\n\r\n    .page-title {\r\n      font-size: 22px;\r\n    }\r\n  }\r\n\r\n  /* 手机适配 */\r\n  @media (max-width: 480px) {\r\n    .goods-container {\r\n      padding: 8px;\r\n    }\r\n\r\n    .goods-list {\r\n      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));\r\n      gap: 12px;\r\n      padding: 0;\r\n    }\r\n\r\n    .goods-card {\r\n      padding: 12px;\r\n      min-height: 250px;\r\n    }\r\n\r\n    .goods-card-title {\r\n      font-size: 14px;\r\n    }\r\n\r\n    .goods-card-description {\r\n      font-size: 12px;\r\n    }\r\n\r\n    .goods-card-price {\r\n      font-size: 16px;\r\n    }\r\n\r\n    .header {\r\n      padding: 12px;\r\n      margin-bottom: 15px;\r\n    }\r\n\r\n    .page-title {\r\n      font-size: 18px;\r\n    }\r\n\r\n    .user-info span {\r\n      font-size: 12px;\r\n    }\r\n\r\n    .btn-add-cart, .btn-edit, .btn-delete {\r\n      padding: 6px 8px;\r\n      font-size: 11px;\r\n    }\r\n  }\r\n</style>"], "mappings": ";;;;AAoIE,OAAOA,KAAI,MAAO,OAAM;AACxB,SAASC,SAAQ,QAAS,cAAa;AAEvC,eAAe;EACbC,IAAI,EAAE,WAAW;EAEjBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLD,IAAI,EAAE,EAAE;MAAI;MACZE,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,CAAC,CAAC;MACfC,UAAU,EAAE,EAAE;MAAE;MAChBC,QAAQ,EAAEC,YAAY,CAACC,OAAO,CAAC,UAAU,KAAK,EAAE;MAChDC,QAAQ,EAAEF,YAAY,CAACC,OAAO,CAAC,UAAU,KAAK,EAAE;MAEhD;MACAE,iBAAiB,EAAE,KAAK;MACxBC,eAAe,EAAE,MAAM;MACvBC,UAAU,EAAE,KAAK;MACjBC,eAAe,EAAE,KAAK;MACtBC,QAAQ,EAAE;QACRC,EAAE,EAAE,IAAI;QACRhB,IAAI,EAAE,EAAE;QACRiB,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE;MACb,CAAC;MAED;MACAC,mBAAmB,EAAE,KAAK;MAC1BC,YAAY,EAAE;IAChB,CAAC;EACH,CAAC;EAEDC,QAAQ,EAAE;IACR;IACAC,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACrB,SAAS,CAACsB,MAAM;IAC9B,CAAC;IAED;IACAC,SAASA,CAAA,EAAG;MACV,OAAO;QACL3B,IAAI,EAAE,CACJ;UAAE4B,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EACvD;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE,EAAE;UAAEH,OAAO,EAAE,gBAAgB;UAAEC,OAAO,EAAE;QAAO,EAC/D;QACDZ,SAAS,EAAE,CACT;UAAEU,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EACvD;UAAEG,IAAI,EAAE,QAAQ;UAAEF,GAAG,EAAE,CAAC;UAAEF,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,EAC/D;QACDV,UAAU,EAAE,CACV;UAAEQ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAS;MAE5D;IACF;EACF,CAAC;EAEDI,OAAOA,CAAA,EAAG;IACR,MAAMC,IAAG,GAAI,uBAAsB;IACnC,IAAI,CAAChC,OAAM,GAAI,IAAG;IAClBiC,OAAO,CAACC,GAAG,CAAC,CACVvC,KAAK,CAACwC,GAAG,CAACH,IAAG,GAAI,eAAe,CAAC,EACjCrC,KAAK,CAACwC,GAAG,CAACH,IAAG,GAAI,mBAAmB,EACrC,EACQI,IAAI,CAAC,CAAC,CAACC,SAAS,EAAEC,YAAY,CAAC,KAAK;MACnC,MAAMC,SAAQ,GAAKF,SAAS,CAACvC,IAAG,IAAKuC,SAAS,CAACvC,IAAI,CAACA,IAAI,IAAK,EAAC;MAC9D,MAAMK,UAAS,GAAKmC,YAAY,CAACxC,IAAG,IAAKwC,YAAY,CAACxC,IAAI,CAACA,IAAI,IAAK,EAAC;MACrE,MAAM0C,GAAE,GAAI,CAAC;MACbrC,UAAU,CAACsC,OAAO,CAACC,CAAA,IAAK;QAAEF,GAAG,CAACE,CAAC,CAAC7B,EAAE,IAAI6B,CAAC,CAAC7C,IAAG;MAAE,CAAC;MAC9C,IAAI,CAACK,WAAU,GAAIsC,GAAE;MACrB,IAAI,CAACrC,UAAS,GAAIA,UAAS;MAC3B,IAAI,CAACF,SAAQ,GAAIsC,SAAS,CAACC,GAAG,CAACG,CAAA,KAAM;QACnC9B,EAAE,EAAE8B,CAAC,CAAC9B,EAAE;QACRhB,IAAI,EAAE8C,CAAC,CAAC9C,IAAI;QACZiB,WAAW,EAAE6B,CAAC,CAAC7B,WAAW;QAC1B;QACA8B,KAAK,EAAED,CAAC,CAACE,IAAG,GAAKb,IAAG,GAAIW,CAAC,CAACE,IAAI,GAAI,EAAE;QACpC;QACAC,KAAK,EAAEH,CAAC,CAAC5B,SAAS;QAClBE,UAAU,EAAE0B,CAAC,CAAC1B,UAAU;QACxB8B,YAAY,EAAEP,GAAG,CAACG,CAAC,CAAC1B,UAAU;MAChC,CAAC,CAAC;IACJ,CAAC,EACA+B,KAAK,CAACC,GAAE,IAAK;MACZC,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEF,GAAG;IAChC,CAAC,EACAG,OAAO,CAAC,MAAM;MACb,IAAI,CAACpD,OAAM,GAAI,KAAI;IACrB,CAAC;EACX,CAAC;EAEDqD,OAAO,EAAE;IACPC,WAAWA,CAACC,CAAC,EAAC;MACZ,IAAGA,CAAA,KAAM,IAAG,IAAKA,CAAA,KAAMC,SAAS,EAAE,OAAO,GAAE;MAC3C,MAAMC,CAAA,GAAIC,MAAM,CAACH,CAAC;MAClB,OAAOG,MAAM,CAACC,KAAK,CAACF,CAAC,IAAIF,CAAA,GAAIE,CAAC,CAACG,cAAc,CAAC,OAAO,EAAE;QAAEC,qBAAqB,EAAE,CAAC;QAAEC,qBAAqB,EAAE;MAAE,CAAC;IAC/G,CAAC;IACDC,SAASA,CAACC,IAAI,EAAE;MACd;MACAd,OAAO,CAACe,GAAG,CAAC,QAAQ,EAAED,IAAI,CAACnE,IAAI,CAAC;MAChC,IAAI,CAACqE,KAAK,CAAC,aAAa,EAAEF,IAAI,CAAC,EAAE;MACjCG,KAAK,CAAC,UAAUH,IAAI,CAACnE,IAAI,EAAE,CAAC;IAC9B,CAAC;IACDuE,KAAKA,CAAA,EAAG;MACN,IAAI,CAACvE,IAAG,GAAI,EAAE;IAChB,CAAC;IACDwE,MAAMA,CAAA,EAAG;MACPhE,YAAY,CAAC+D,KAAK,CAAC;MACnBxE,SAAS,CAAC0E,OAAO,CAAC,OAAO;MACzB,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,QAAQ;IAC5B,CAAC;IACDC,SAASA,CAAA,EAAG;MACV,IAAI,CAACF,OAAO,CAACC,IAAI,CAAC,QAAQ;IAC5B,CAAC;IAED;IACAE,cAAcA,CAACV,IAAI,EAAE;MACnB,IAAI,CAAC5C,YAAW,GAAI4C,IAAG;MACvB,IAAI,CAAC7C,mBAAkB,GAAI,IAAG;IAChC,CAAC;IAED;IACAwD,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAAClE,eAAc,GAAI,MAAK;MAC5B,IAAI,CAACC,UAAS,GAAI,KAAI;MACtB,IAAI,CAACE,QAAO,GAAI;QACdC,EAAE,EAAE,IAAI;QACRhB,IAAI,EAAE,EAAE;QACRiB,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE;MACb;MACA,IAAI,CAACV,iBAAgB,GAAI,IAAG;IAC9B,CAAC;IAED;IACAoE,QAAQA,CAACZ,IAAI,EAAE;MACb,IAAI,CAACvD,eAAc,GAAI,MAAK;MAC5B,IAAI,CAACC,UAAS,GAAI,IAAG;MACrB,IAAI,CAACE,QAAO,GAAI;QACdC,EAAE,EAAEmD,IAAI,CAACnD,EAAE;QACXhB,IAAI,EAAEmE,IAAI,CAACnE,IAAI;QACfiB,WAAW,EAAEkD,IAAI,CAAClD,WAAW;QAC7BC,SAAS,EAAEiD,IAAI,CAACjD,SAAS;QACzBC,QAAQ,EAAEgD,IAAI,CAAChD,QAAQ;QACvBC,UAAU,EAAE+C,IAAI,CAAC/C,UAAU;QAC3BC,SAAS,EAAE8C,IAAI,CAAC9C;MAClB;MACA,IAAI,CAACV,iBAAgB,GAAI,IAAG;IAC9B,CAAC;IAED;IACA,MAAMqE,QAAQA,CAAA,EAAG;MACf,IAAI,CAAC,IAAI,CAACC,KAAK,CAACC,WAAW,EAAE;MAE7B,IAAI,CAACD,KAAK,CAACC,WAAW,CAACC,QAAQ,CAAC,MAAOC,KAAK,IAAK;QAC/C,IAAIA,KAAK,EAAE;UACT,IAAI,CAACtE,eAAc,GAAI,IAAG;UAE1B,IAAI;YACF,MAAMuE,GAAE,GAAI,IAAI,CAACxE,UAAS,GACtB,sCAAqC,GACrC,mCAAkC;YAEtC,MAAMyE,MAAK,GAAI,IAAI,CAACzE,UAAS,GAAI,KAAI,GAAI,MAAK;YAE9C,MAAM0E,QAAO,GAAI,MAAMC,KAAK,CAACH,GAAG,EAAE;cAChCC,MAAM;cACNG,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAEjF,YAAY,CAACC,OAAO,CAAC,OAAO;cACvC,CAAC;cACDiF,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC7E,QAAQ;YACpC,CAAC;YAED,MAAM8E,MAAK,GAAI,MAAMN,QAAQ,CAACO,IAAI,CAAC;YAEnC,IAAID,MAAM,CAACE,IAAG,KAAM,KAAK,EAAE;cACzB,IAAI,CAACC,QAAQ,CAACvB,OAAO,CAAC,IAAI,CAAC5D,UAAS,GAAI,QAAO,GAAI,QAAQ;cAC3D,IAAI,CAACF,iBAAgB,GAAI,KAAI;cAC7B,IAAI,CAACsF,YAAY,CAAC;YACpB,OAAO;cACL,IAAI,CAACD,QAAQ,CAAC1C,KAAK,CAACuC,MAAM,CAACK,GAAE,IAAK,MAAM;YAC1C;UACF,EAAE,OAAO5C,KAAK,EAAE;YACdD,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEA,KAAK;YAC9B,IAAI,CAAC0C,QAAQ,CAAC1C,KAAK,CAAC,YAAY;UAClC,UAAU;YACR,IAAI,CAACxC,eAAc,GAAI,KAAI;UAC7B;QACF;MACF,CAAC;IACH,CAAC;IAED;IACA,MAAMqF,UAAUA,CAAChC,IAAI,EAAE;MACrB,IAAI;QACF,MAAM,IAAI,CAACiC,QAAQ,CAAC,YAAYjC,IAAI,CAACnE,IAAI,MAAM,EAAE,MAAM,EAAE;UACvDqG,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBrE,IAAI,EAAE;QACR,CAAC;QAED,MAAMsD,QAAO,GAAI,MAAMC,KAAK,CAAC,wCAAwCrB,IAAI,CAACnD,EAAE,EAAE,EAAE;UAC9EsE,MAAM,EAAE,QAAQ;UAChBG,OAAO,EAAE;YACP,OAAO,EAAEjF,YAAY,CAACC,OAAO,CAAC,OAAO;UACvC;QACF,CAAC;QAED,MAAMoF,MAAK,GAAI,MAAMN,QAAQ,CAACO,IAAI,CAAC;QAEnC,IAAID,MAAM,CAACE,IAAG,KAAM,KAAK,EAAE;UACzB,IAAI,CAACC,QAAQ,CAACvB,OAAO,CAAC,QAAQ;UAC9B,IAAI,CAACwB,YAAY,CAAC;QACpB,OAAO;UACL,IAAI,CAACD,QAAQ,CAAC1C,KAAK,CAACuC,MAAM,CAACK,GAAE,IAAK,MAAM;QAC1C;MACF,EAAE,OAAO5C,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBD,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9B,IAAI,CAAC0C,QAAQ,CAAC1C,KAAK,CAAC,YAAY;QAClC;MACF;IACF,CAAC;IAED;IACA2C,YAAYA,CAAA,EAAG;MACb,IAAI,CAAC9F,OAAM,GAAI,IAAG;MAClB,MAAMgC,IAAG,GAAI,uBAAsB;MACnCrC,KAAK,CAACwC,GAAG,CAACH,IAAG,GAAI,eAAe,EAC7BI,IAAI,CAACgD,QAAO,IAAK;QAChB,MAAM7C,SAAQ,GAAK6C,QAAQ,CAACtF,IAAG,IAAKsF,QAAQ,CAACtF,IAAI,CAACA,IAAI,IAAK,EAAC;QAC5D,IAAI,CAACG,SAAQ,GAAIsC,SAAS,CAACC,GAAG,CAACwB,IAAG,KAAM;UACtC,GAAGA,IAAI;UACPjB,YAAY,EAAE,IAAI,CAAC7C,WAAW,CAAC8D,IAAI,CAAC/C,UAAU,KAAK,KAAK;UACxD6B,KAAK,EAAEkB,IAAI,CAACjD,SAAQ,IAAKiD,IAAI,CAAChD,QAAO,IAAK,CAAC;QAC7C,CAAC,CAAC;MACJ,CAAC,EACAgC,KAAK,CAACG,KAAI,IAAK;QACdD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC,IAAI,CAAC0C,QAAQ,CAAC1C,KAAK,CAAC,UAAU;MAChC,CAAC,EACAC,OAAO,CAAC,MAAM;QACb,IAAI,CAACpD,OAAM,GAAI,KAAI;MACrB,CAAC;IACL;EACF,CAAC;EAEDoG,OAAOA,CAAA,EAAG;IACRlD,OAAO,CAACe,GAAG,CAAC,SAAS,CAAC;EACxB,CAAC;EAEDoC,aAAaA,CAAA,EAAG;IACdnD,OAAO,CAACe,GAAG,CAAC,UAAU,CAAC;EACzB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}