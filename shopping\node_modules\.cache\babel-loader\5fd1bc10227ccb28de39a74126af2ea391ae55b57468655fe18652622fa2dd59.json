{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive, onMounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage, ElNotification } from 'element-plus';\n\n// 表单引用\n\nexport default {\n  __name: 'login',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const loginFormRef = ref();\n\n    // 路由实例\n    const router = useRouter();\n\n    // 登录表单数据\n    const loginForm = reactive({\n      username: '',\n      password: ''\n    });\n\n    // 记住密码\n    const rememberMe = ref(false);\n\n    // 加载状态\n    const loading = ref(false);\n\n    // 表单验证规则\n    const loginRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }, {\n        min: 2,\n        max: 20,\n        message: '用户名长度为2-20个字符',\n        trigger: 'blur'\n      }],\n      password: [{\n        required: true,\n        message: '请输入密码',\n        trigger: 'blur'\n      }, {\n        min: 1,\n        max: 20,\n        message: '密码长度为1-20个字符',\n        trigger: 'blur'\n      }]\n    };\n\n    // 处理登录\n    const handleLogin = async () => {\n      if (!loginFormRef.value) return;\n      await loginFormRef.value.validate(async valid => {\n        if (valid) {\n          // 调用后端登录接口\n          loading.value = true;\n          try {\n            const response = await fetch('http://localhost:9191/userAPI/login', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                username: loginForm.username,\n                password: loginForm.password\n              })\n            });\n            const result = await response.json();\n            if (result.code === '200') {\n              const {\n                user,\n                token\n              } = result.data;\n              ElNotification({\n                title: '登录成功',\n                message: `欢迎回来，${user.username}！`,\n                type: 'success',\n                duration: 2000\n              });\n\n              // 保存登录状态和用户信息\n              localStorage.setItem('isLoggedIn', 'true');\n              localStorage.setItem('username', user.username);\n              localStorage.setItem('userRole', user.role);\n              localStorage.setItem('token', token);\n              localStorage.setItem('userId', user.id);\n\n              // 根据用户角色跳转\n              if (user.role === 'admin') {\n                router.push('/admin');\n              } else {\n                router.push('/');\n              }\n            } else {\n              ElMessage.error(result.msg || '登录失败');\n            }\n          } catch (error) {\n            console.error('登录请求失败:', error);\n            ElMessage.error('网络错误，请稍后重试');\n          } finally {\n            loading.value = false;\n          }\n        } else {\n          ElMessage.error('请填写正确的登录信息');\n          return false;\n        }\n      });\n    };\n\n    // 忘记密码\n    const handleForgetPassword = () => {\n      ElMessage.info('忘记密码功能开发中...');\n    };\n\n    // 组件挂载时的初始化\n    onMounted(() => {\n      // 检查是否记住密码\n      const savedUsername = localStorage.getItem('savedUsername');\n      if (savedUsername) {\n        loginForm.username = savedUsername;\n        rememberMe.value = true;\n      }\n    });\n    const __returned__ = {\n      loginFormRef,\n      router,\n      loginForm,\n      rememberMe,\n      loading,\n      loginRules,\n      handleLogin,\n      handleForgetPassword,\n      ref,\n      reactive,\n      onMounted,\n      get useRouter() {\n        return useRouter;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElNotification() {\n        return ElNotification;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "useRouter", "ElMessage", "ElNotification", "loginFormRef", "router", "loginForm", "username", "password", "rememberMe", "loading", "loginRules", "required", "message", "trigger", "min", "max", "handleLogin", "value", "validate", "valid", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "result", "json", "code", "user", "token", "data", "title", "type", "duration", "localStorage", "setItem", "role", "id", "push", "error", "msg", "console", "handleForgetPassword", "info", "savedUsername", "getItem"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/src/components/login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-box\">\n      <div class=\"login-header\">\n        <h2>🛒 购物商城</h2>\n        <p>欢迎登录</p>\n      </div>\n      \n      <el-form\n        ref=\"loginFormRef\"\n        :model=\"loginForm\"\n        :rules=\"loginRules\"\n        class=\"login-form\"\n        @keyup.enter=\"handleLogin\"\n      >\n        <!-- 用户名输入框 -->\n        <el-form-item prop=\"username\">\n          <el-input\n            v-model=\"loginForm.username\"\n            placeholder=\"请输入用户名\"\n            prefix-icon=\"User\"\n            clearable\n            size=\"large\"\n          />\n        </el-form-item>\n        \n        <!-- 密码输入框 -->\n        <el-form-item prop=\"password\">\n          <el-input\n            v-model=\"loginForm.password\"\n            type=\"password\"\n            placeholder=\"请输入密码\"\n            prefix-icon=\"Lock\"\n            show-password\n            size=\"large\"\n          />\n        </el-form-item>\n        \n\n        \n        <!-- 记住密码和忘记密码 -->\n        <el-form-item>\n          <div class=\"login-options\">\n            <el-checkbox v-model=\"rememberMe\">记住密码</el-checkbox>\n            <el-link type=\"primary\" @click=\"handleForgetPassword\">忘记密码？</el-link>\n          </div>\n        </el-form-item>\n        \n        <!-- 登录按钮 -->\n        <el-form-item>\n          <el-button\n            type=\"primary\"\n            size=\"large\"\n            class=\"login-button\"\n            :loading=\"loading\"\n            @click=\"handleLogin\"\n            block\n          >\n            {{ loading ? '登录中...' : '登录' }}\n          </el-button>\n        </el-form-item>\n        \n\n      </el-form>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage, ElNotification } from 'element-plus'\n\n// 表单引用\nconst loginFormRef = ref()\n\n// 路由实例\nconst router = useRouter()\n\n// 登录表单数据\nconst loginForm = reactive({\n  username: '',\n  password: ''\n})\n\n// 记住密码\nconst rememberMe = ref(false)\n\n// 加载状态\nconst loading = ref(false)\n\n// 表单验证规则\nconst loginRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 2, max: 20, message: '用户名长度为2-20个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' },\n    { min: 1, max: 20, message: '密码长度为1-20个字符', trigger: 'blur' }\n  ]\n}\n\n\n\n// 处理登录\nconst handleLogin = async () => {\n  if (!loginFormRef.value) return\n\n  await loginFormRef.value.validate(async (valid) => {\n    if (valid) {\n      // 调用后端登录接口\n      loading.value = true\n\n      try {\n        const response = await fetch('http://localhost:9191/userAPI/login', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            username: loginForm.username,\n            password: loginForm.password\n          })\n        })\n\n        const result = await response.json()\n\n        if (result.code === '200') {\n          const { user, token } = result.data\n\n          ElNotification({\n            title: '登录成功',\n            message: `欢迎回来，${user.username}！`,\n            type: 'success',\n            duration: 2000\n          })\n\n          // 保存登录状态和用户信息\n          localStorage.setItem('isLoggedIn', 'true')\n          localStorage.setItem('username', user.username)\n          localStorage.setItem('userRole', user.role)\n          localStorage.setItem('token', token)\n          localStorage.setItem('userId', user.id)\n\n          // 根据用户角色跳转\n          if (user.role === 'admin') {\n            router.push('/admin')\n          } else {\n            router.push('/')\n          }\n        } else {\n          ElMessage.error(result.msg || '登录失败')\n        }\n      } catch (error) {\n        console.error('登录请求失败:', error)\n        ElMessage.error('网络错误，请稍后重试')\n      } finally {\n        loading.value = false\n      }\n    } else {\n      ElMessage.error('请填写正确的登录信息')\n      return false\n    }\n  })\n}\n\n// 忘记密码\nconst handleForgetPassword = () => {\n  ElMessage.info('忘记密码功能开发中...')\n}\n\n// 组件挂载时的初始化\nonMounted(() => {\n  // 检查是否记住密码\n  const savedUsername = localStorage.getItem('savedUsername')\n  if (savedUsername) {\n    loginForm.username = savedUsername\n    rememberMe.value = true\n  }\n})\n</script>\n\n<style scoped>\n.login-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n}\n\n.login-box {\n  width: 100%;\n  max-width: 400px;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 10px;\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  padding: 40px 30px;\n  backdrop-filter: blur(10px);\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.login-header h2 {\n  color: #333;\n  margin-bottom: 10px;\n  font-size: 24px;\n}\n\n.login-header p {\n  color: #666;\n  font-size: 14px;\n}\n\n.login-form {\n  width: 100%;\n}\n\n.captcha-item :deep(.el-form-item__content) {\n  display: block;\n}\n\n.captcha-wrapper {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.captcha-input {\n  flex: 1;\n}\n\n.captcha-image {\n  width: 100px;\n  height: 40px;\n  background: linear-gradient(45deg, #f0f0f0, #e0e0e0);\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 18px;\n  color: #333;\n  cursor: pointer;\n  user-select: none;\n  transition: all 0.3s;\n}\n\n.captcha-image:hover {\n  background: linear-gradient(45deg, #e0e0e0, #d0d0d0);\n}\n\n.login-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.login-button {\n  margin-top: 10px;\n}\n\n.other-login {\n  text-align: center;\n  color: #666;\n  font-size: 14px;\n}\n\n.other-login span {\n  margin-right: 10px;\n}\n\n.register-link {\n  text-align: center;\n  color: #666;\n  font-size: 14px;\n}\n\n:deep(.el-input__wrapper) {\n  border-radius: 20px;\n}\n\n:deep(.el-button) {\n  border-radius: 20px;\n}\n\n@media (max-width: 480px) {\n  .login-box {\n    padding: 30px 20px;\n    margin: 10px;\n  }\n  \n  .captcha-wrapper {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .captcha-image {\n    width: 100%;\n    margin-top: 10px;\n  }\n}\n</style>"], "mappings": ";AAqEA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,KAAI;AAC7C,SAASC,SAAS,QAAQ,YAAW;AACrC,SAASC,SAAS,EAAEC,cAAc,QAAQ,cAAa;;AAEvD;;;;;;;;IACA,MAAMC,YAAY,GAAGN,GAAG,CAAC;;IAEzB;IACA,MAAMO,MAAM,GAAGJ,SAAS,CAAC;;IAEzB;IACA,MAAMK,SAAS,GAAGP,QAAQ,CAAC;MACzBQ,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC;;IAED;IACA,MAAMC,UAAU,GAAGX,GAAG,CAAC,KAAK;;IAE5B;IACA,MAAMY,OAAO,GAAGZ,GAAG,CAAC,KAAK;;IAEzB;IACA,MAAMa,UAAU,GAAG;MACjBJ,QAAQ,EAAE,CACR;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,eAAe;QAAEC,OAAO,EAAE;MAAO,EAC9D;MACDN,QAAQ,EAAE,CACR;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,cAAc;QAAEC,OAAO,EAAE;MAAO;IAEhE;;IAIA;IACA,MAAMG,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI,CAACb,YAAY,CAACc,KAAK,EAAE;MAEzB,MAAMd,YAAY,CAACc,KAAK,CAACC,QAAQ,CAAC,MAAOC,KAAK,IAAK;QACjD,IAAIA,KAAK,EAAE;UACT;UACAV,OAAO,CAACQ,KAAK,GAAG,IAAG;UAEnB,IAAI;YACF,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,EAAE;cAClEC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE;gBACP,cAAc,EAAE;cAClB,CAAC;cACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;gBACnBpB,QAAQ,EAAED,SAAS,CAACC,QAAQ;gBAC5BC,QAAQ,EAAEF,SAAS,CAACE;cACtB,CAAC;YACH,CAAC;YAED,MAAMoB,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC;YAEnC,IAAID,MAAM,CAACE,IAAI,KAAK,KAAK,EAAE;cACzB,MAAM;gBAAEC,IAAI;gBAAEC;cAAM,CAAC,GAAGJ,MAAM,CAACK,IAAG;cAElC9B,cAAc,CAAC;gBACb+B,KAAK,EAAE,MAAM;gBACbrB,OAAO,EAAE,QAAQkB,IAAI,CAACxB,QAAQ,GAAG;gBACjC4B,IAAI,EAAE,SAAS;gBACfC,QAAQ,EAAE;cACZ,CAAC;;cAED;cACAC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,MAAM;cACzCD,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEP,IAAI,CAACxB,QAAQ;cAC9C8B,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEP,IAAI,CAACQ,IAAI;cAC1CF,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEN,KAAK;cACnCK,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAEP,IAAI,CAACS,EAAE;;cAEtC;cACA,IAAIT,IAAI,CAACQ,IAAI,KAAK,OAAO,EAAE;gBACzBlC,MAAM,CAACoC,IAAI,CAAC,QAAQ;cACtB,CAAC,MAAM;gBACLpC,MAAM,CAACoC,IAAI,CAAC,GAAG;cACjB;YACF,CAAC,MAAM;cACLvC,SAAS,CAACwC,KAAK,CAACd,MAAM,CAACe,GAAG,IAAI,MAAM;YACtC;UACF,CAAC,CAAC,OAAOD,KAAK,EAAE;YACdE,OAAO,CAACF,KAAK,CAAC,SAAS,EAAEA,KAAK;YAC9BxC,SAAS,CAACwC,KAAK,CAAC,YAAY;UAC9B,CAAC,SAAS;YACRhC,OAAO,CAACQ,KAAK,GAAG,KAAI;UACtB;QACF,CAAC,MAAM;UACLhB,SAAS,CAACwC,KAAK,CAAC,YAAY;UAC5B,OAAO,KAAI;QACb;MACF,CAAC;IACH;;IAEA;IACA,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;MACjC3C,SAAS,CAAC4C,IAAI,CAAC,cAAc;IAC/B;;IAEA;IACA9C,SAAS,CAAC,MAAM;MACd;MACA,MAAM+C,aAAa,GAAGV,YAAY,CAACW,OAAO,CAAC,eAAe;MAC1D,IAAID,aAAa,EAAE;QACjBzC,SAAS,CAACC,QAAQ,GAAGwC,aAAY;QACjCtC,UAAU,CAACS,KAAK,GAAG,IAAG;MACxB;IACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}