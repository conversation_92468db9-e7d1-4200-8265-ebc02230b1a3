{"ast": null, "code": "import Login from '../components/login.vue';\nexport default {\n  name: 'LoginView',\n  components: {\n    Login\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON>", "name", "components"], "sources": ["D:\\2025_down\\project\\shoppingOnline-20250826-sfl\\shoppingOnline-20250826\\shopping\\src\\views\\Login.vue"], "sourcesContent": ["<template>\n  <div>\n    <Login />\n  </div>\n</template>\n\n<script>\nimport Login from '../components/login.vue'\n\nexport default {\n  name: 'LoginView',\n  components: {\n    Login\n  }\n}\n</script>\n"], "mappings": "AAOA,OAAOA,KAAI,MAAO,yBAAwB;AAE1C,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE;IACVF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}