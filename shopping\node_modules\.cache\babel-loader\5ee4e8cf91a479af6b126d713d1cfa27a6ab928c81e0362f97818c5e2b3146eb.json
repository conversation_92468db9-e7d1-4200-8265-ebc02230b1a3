{"ast": null, "code": "import escape from './escape.js';\nimport reEscape from './_reEscape.js';\nimport reEvaluate from './_reEvaluate.js';\nimport reInterpolate from './_reInterpolate.js';\n\n/**\n * By default, the template delimiters used by lodash are like those in\n * embedded Ruby (ERB) as well as ES2015 template strings. Change the\n * following template settings to use alternative delimiters.\n *\n * @static\n * @memberOf _\n * @type {Object}\n */\nvar templateSettings = {\n  /**\n   * Used to detect `data` property values to be HTML-escaped.\n   *\n   * @memberOf _.templateSettings\n   * @type {RegExp}\n   */\n  'escape': reEscape,\n  /**\n   * Used to detect code to be evaluated.\n   *\n   * @memberOf _.templateSettings\n   * @type {RegExp}\n   */\n  'evaluate': reEvaluate,\n  /**\n   * Used to detect `data` property values to inject.\n   *\n   * @memberOf _.templateSettings\n   * @type {RegExp}\n   */\n  'interpolate': reInterpolate,\n  /**\n   * Used to reference the data object in the template text.\n   *\n   * @memberOf _.templateSettings\n   * @type {string}\n   */\n  'variable': '',\n  /**\n   * Used to import variables into the compiled template.\n   *\n   * @memberOf _.templateSettings\n   * @type {Object}\n   */\n  'imports': {\n    /**\n     * A reference to the `lodash` function.\n     *\n     * @memberOf _.templateSettings.imports\n     * @type {Function}\n     */\n    '_': {\n      'escape': escape\n    }\n  }\n};\nexport default templateSettings;", "map": {"version": 3, "names": ["escape", "reEscape", "reEvaluate", "reInterpolate", "templateSettings"], "sources": ["D:/2025_down/project/shoppingOnline-20250826-sfl/shoppingOnline-20250826/shopping/node_modules/lodash-es/templateSettings.js"], "sourcesContent": ["import escape from './escape.js';\nimport reEscape from './_reEscape.js';\nimport reEvaluate from './_reEvaluate.js';\nimport reInterpolate from './_reInterpolate.js';\n\n/**\n * By default, the template delimiters used by lodash are like those in\n * embedded Ruby (ERB) as well as ES2015 template strings. Change the\n * following template settings to use alternative delimiters.\n *\n * @static\n * @memberOf _\n * @type {Object}\n */\nvar templateSettings = {\n\n  /**\n   * Used to detect `data` property values to be HTML-escaped.\n   *\n   * @memberOf _.templateSettings\n   * @type {RegExp}\n   */\n  'escape': reEscape,\n\n  /**\n   * Used to detect code to be evaluated.\n   *\n   * @memberOf _.templateSettings\n   * @type {RegExp}\n   */\n  'evaluate': reEvaluate,\n\n  /**\n   * Used to detect `data` property values to inject.\n   *\n   * @memberOf _.templateSettings\n   * @type {RegExp}\n   */\n  'interpolate': reInterpolate,\n\n  /**\n   * Used to reference the data object in the template text.\n   *\n   * @memberOf _.templateSettings\n   * @type {string}\n   */\n  'variable': '',\n\n  /**\n   * Used to import variables into the compiled template.\n   *\n   * @memberOf _.templateSettings\n   * @type {Object}\n   */\n  'imports': {\n\n    /**\n     * A reference to the `lodash` function.\n     *\n     * @memberOf _.templateSettings.imports\n     * @type {Function}\n     */\n    '_': { 'escape': escape }\n  }\n};\n\nexport default templateSettings;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAChC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,aAAa,MAAM,qBAAqB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,gBAAgB,GAAG;EAErB;AACF;AACA;AACA;AACA;AACA;EACE,QAAQ,EAAEH,QAAQ;EAElB;AACF;AACA;AACA;AACA;AACA;EACE,UAAU,EAAEC,UAAU;EAEtB;AACF;AACA;AACA;AACA;AACA;EACE,aAAa,EAAEC,aAAa;EAE5B;AACF;AACA;AACA;AACA;AACA;EACE,UAAU,EAAE,EAAE;EAEd;AACF;AACA;AACA;AACA;AACA;EACE,SAAS,EAAE;IAET;AACJ;AACA;AACA;AACA;AACA;IACI,GAAG,EAAE;MAAE,QAAQ,EAAEH;IAAO;EAC1B;AACF,CAAC;AAED,eAAeI,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}