{"ast": null, "code": "import { isArray } from '@vue/shared';\nfunction isValidValue(val) {\n  return val || val === 0;\n}\nfunction isValidArray(val) {\n  return isArray(val) && val.length;\n}\nfunction toValidArray(val) {\n  return isArray(val) ? val : isValidValue(val) ? [val] : [];\n}\nfunction treeFind(treeData, findCallback, getChildren, resultCallback, parent) {\n  for (let i = 0; i < treeData.length; i++) {\n    const data = treeData[i];\n    if (findCallback(data, i, treeData, parent)) {\n      return resultCallback ? resultCallback(data, i, treeData, parent) : data;\n    } else {\n      const children = getChildren(data);\n      if (isValidArray(children)) {\n        const find = treeFind(children, findCallback, getChildren, resultCallback, data);\n        if (find) return find;\n      }\n    }\n  }\n}\nfunction treeEach(treeData, callback, getChildren, parent) {\n  for (let i = 0; i < treeData.length; i++) {\n    const data = treeData[i];\n    callback(data, i, treeData, parent);\n    const children = getChildren(data);\n    if (isValidArray(children)) {\n      treeEach(children, callback, getChildren, data);\n    }\n  }\n}\nexport { isValidArray, isValidValue, toValidArray, treeEach, treeFind };", "map": {"version": 3, "names": ["isValidValue", "val", "isValidArray", "isArray", "length", "to<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "treeFind", "treeData", "find<PERSON><PERSON>back", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "result<PERSON>allback", "parent", "i", "data", "children", "find", "treeEach", "callback"], "sources": ["../../../../../../packages/components/tree-select/src/utils.ts"], "sourcesContent": ["import { isArray } from '@element-plus/utils'\n\nimport type { TreeNodeData } from '@element-plus/components/tree/src/tree.type'\n\nexport function isValidValue(val: any) {\n  return val || val === 0\n}\n\nexport function isValidArray(val: any) {\n  return isArray(val) && val.length\n}\n\nexport function toValidArray(val: any) {\n  return isArray(val) ? val : isValidValue(val) ? [val] : []\n}\n\ntype TreeCallback<T extends TreeNodeData, R> = (\n  data: T,\n  index: number,\n  array: T[],\n  parent?: T\n) => R\n\ntype TreeFindCallback<T extends TreeNodeData> = TreeCallback<T, boolean>\n\nexport function treeFind<T extends TreeNodeData>(\n  treeData: T[],\n  findCallback: TreeFindCallback<T>,\n  getChildren: (data: T) => T[]\n): T | undefined\nexport function treeFind<T extends TreeNodeData, R>(\n  treeData: T[],\n  findCallback: TreeFindCallback<T>,\n  getChildren: (data: T) => T[],\n  resultCallback?: TreeCallback<T, R>,\n  parent?: T\n): R | undefined\nexport function treeFind<T extends TreeNodeData, R>(\n  treeData: T[],\n  findCallback: TreeFindCallback<T>,\n  getChildren: (data: T) => T[],\n  resultCallback?: TreeCallback<T, R>,\n  parent?: T\n): T | R | undefined {\n  for (let i = 0; i < treeData.length; i++) {\n    const data = treeData[i]\n    if (findCallback(data, i, treeData, parent)) {\n      return resultCallback ? resultCallback(data, i, treeData, parent) : data\n    } else {\n      const children = getChildren(data)\n      if (isValidArray(children)) {\n        const find = treeFind(\n          children,\n          findCallback,\n          getChildren,\n          resultCallback,\n          data\n        )\n        if (find) return find\n      }\n    }\n  }\n}\n\nexport function treeEach<T extends TreeNodeData>(\n  treeData: T[],\n  callback: TreeCallback<T, void>,\n  getChildren: (data: T) => T[],\n  parent?: T\n) {\n  for (let i = 0; i < treeData.length; i++) {\n    const data = treeData[i]\n    callback(data, i, treeData, parent)\n\n    const children = getChildren(data)\n    if (isValidArray(children)) {\n      treeEach(children, callback, getChildren, data)\n    }\n  }\n}\n"], "mappings": ";AACO,SAASA,YAAYA,CAACC,GAAG,EAAE;EAChC,OAAOA,GAAG,IAAIA,GAAG,KAAK,CAAC;AACzB;AACO,SAASC,YAAYA,CAACD,GAAG,EAAE;EAChC,OAAOE,OAAO,CAACF,GAAG,CAAC,IAAIA,GAAG,CAACG,MAAM;AACnC;AACO,SAASC,YAAYA,CAACJ,GAAG,EAAE;EAChC,OAAOE,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAGD,YAAY,CAACC,GAAG,CAAC,GAAG,CAACA,GAAG,CAAC,GAAG,EAAE;AAC5D;AACO,SAASK,QAAQA,CAACC,QAAQ,EAAEC,YAAY,EAAEC,WAAW,EAAEC,cAAc,EAAEC,MAAM,EAAE;EACpF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,QAAQ,CAACH,MAAM,EAAEQ,CAAC,EAAE,EAAE;IACxC,MAAMC,IAAI,GAAGN,QAAQ,CAACK,CAAC,CAAC;IACxB,IAAIJ,YAAY,CAACK,IAAI,EAAED,CAAC,EAAEL,QAAQ,EAAEI,MAAM,CAAC,EAAE;MAC3C,OAAOD,cAAc,GAAGA,cAAc,CAACG,IAAI,EAAED,CAAC,EAAEL,QAAQ,EAAEI,MAAM,CAAC,GAAGE,IAAI;IAC9E,CAAK,MAAM;MACL,MAAMC,QAAQ,GAAGL,WAAW,CAACI,IAAI,CAAC;MAClC,IAAIX,YAAY,CAACY,QAAQ,CAAC,EAAE;QAC1B,MAAMC,IAAI,GAAGT,QAAQ,CAACQ,QAAQ,EAAEN,YAAY,EAAEC,WAAW,EAAEC,cAAc,EAAEG,IAAI,CAAC;QAChF,IAAIE,IAAI,EACN,OAAOA,IAAI;MACrB;IACA;EACA;AACA;AACO,SAASC,QAAQA,CAACT,QAAQ,EAAEU,QAAQ,EAAER,WAAW,EAAEE,MAAM,EAAE;EAChE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,QAAQ,CAACH,MAAM,EAAEQ,CAAC,EAAE,EAAE;IACxC,MAAMC,IAAI,GAAGN,QAAQ,CAACK,CAAC,CAAC;IACxBK,QAAQ,CAACJ,IAAI,EAAED,CAAC,EAAEL,QAAQ,EAAEI,MAAM,CAAC;IACnC,MAAMG,QAAQ,GAAGL,WAAW,CAACI,IAAI,CAAC;IAClC,IAAIX,YAAY,CAACY,QAAQ,CAAC,EAAE;MAC1BE,QAAQ,CAACF,QAAQ,EAAEG,QAAQ,EAAER,WAAW,EAAEI,IAAI,CAAC;IACrD;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}