{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  id: \"app\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_view = _resolveComponent(\"router-view\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_router_view)]);\n}", "map": {"version": 3, "names": ["id", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_router_view"], "sources": ["D:\\2025_down\\project\\shoppingOnline-20250826-sfl\\shoppingOnline-20250826\\shopping\\src\\App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'App'\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n}\n</style>\n"], "mappings": ";;EACOA,EAAE,EAAC;AAAK;;;uBAAbC,mBAAA,CAEM,OAFNC,UAEM,GADJC,YAAA,CAAeC,sBAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}